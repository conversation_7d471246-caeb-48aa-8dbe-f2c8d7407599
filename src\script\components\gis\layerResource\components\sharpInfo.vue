<template>
    <div class="sharp-info">
        <div v-if="row.resourceName" class="head">
            <span class="title-dot"></span>
            <span class="titleName">{{ row.resourceName }}</span>
        </div>
        <div v-for="(item, inx) in sharpList" class="wrap" :key="inx">
            <span class="title">{{ item.label }}</span>
            <!--  <el-table
                v-if="item.isTable"
                :data="item.content"
                size="mini"
                :max-height="142"
                :show-header="false"
            >
                <el-table-column type="index" align="center" />
                <el-table-column property="lng" label="经度"></el-table-column>
                <el-table-column property="lat" label="纬度"></el-table-column>
                <el-table-column
                    v-if="shapeType === 1"
                    property="radius"
                    :width="72"
                ></el-table-column>
            </el-table> -->
            <dataTable
                v-if="item.isTable"
                :columns="columns"
                :data="item.content"
                size="mini"
                :max-height="142"
                :show-header="false"
            >
                <template #index="{ inx }">
                    <span>{{ inx + 1 }}</span>
                </template>
            </dataTable>
            <div v-else class="content">
                <template v-for="(text, i) in item.content">
                    <div :key="i">{{ text }}</div>
                    <br v-if="i < item.content.length - 1" :key="`bar-${i}`" />
                </template>
            </div>
        </div>
    </div>
</template>

<script>
import { toWKTCoordinates, toPointSequence } from '@/utils/method.js';
import dataTable from '@/script/components/dataTableLast.vue';
export default {
    name: 'sharpInfo',
    components: {
        dataTable,
    },
    props: {
        planeObjs: {
            type: Array,
            default: () => [],
        },
        sharps: {
            type: Array,
            default: () => [],
        },
        shapeType: {
            // 多边形或者圆形，默认多边形
            type: Number,
            default: 2,
        },
        row: {
            type: Object,
            default: () => ({}),
        },
    },
    data() {
        return {
            sharpList: [],
        };
    },
    computed: {
        columns() {
            const columns = [
                { prop: 'index', align: 'center', width: 40, },
                {
                    label: '经度',
                    prop: 'lng',
                },
                {
                    label: '纬度',
                    prop: 'lat',
                },
                {
                    label: '半径',
                    prop: 'radius',
                    width: 72,
                },
            ];
            if (this.shapeType === 1) {
                return columns;
            }
            return columns.slice(0, 3);
        },
    },
    created() {
        this.initData();
    },
    mounted() {
        this.$eventBus.$on('updateSharpInfo', this.initData);
    },
    methods: {
        initData() {
            const planeObjs =
                this.sharps && this.sharps.length ? this.sharps : this.planeObjs.filter(Boolean);

            switch (this.shapeType) {
                case 1:
                    this.sharpList = this.getCircleSharpList(planeObjs);
                    break;
                case 2:
                    this.sharpList = this.getSharpList(planeObjs);
                    break;
                case 4:
                    this.sharpList = this.getLineSharpList(planeObjs);
                    break;
                case 5:
                    this.sharpList = this.getLocatePointList(planeObjs);
                    break;
            }
        },
        // 多边形
        getSharpList(planeObjs) {
            const regionCoors = planeObjs.map((item) => item.region.points);
            const pointSequence = [];
            const hexadecimal = [];
            const wkt = [];
            const coordinates = [];
            for (const region of regionCoors) {
                pointSequence.push(toPointSequence(region));
                hexadecimal.push(this.getHEXCoordinates(region));
                wkt.push(this.getWKTCoordinates(region));
                coordinates.push({}, ...region);
            }
            return [
                { label: '点序列', content: pointSequence },
                { label: '16进制', content: hexadecimal },
                { label: 'WKT', content: wkt },
                {
                    label: '轮廓',
                    content: coordinates.slice(1),
                    isTable: true,
                },
            ];
        },
        // 圆形
        getCircleSharpList(planeObjs) {
            const regionCoors = planeObjs.map((item) => {
                const circle = item.region.circle;
                return {
                    ...circle,
                    radius: Number(circle.radius.toFixed(3)),
                };
            });
            const pointSequence = [];
            const hexadecimal = [];
            const wkt = [];
            const coordinates = [];
            for (const circle of regionCoors) {
                const { centerLongitude, centerLatitude, radius } = circle;
                pointSequence.push(`${centerLongitude},${centerLatitude};${radius}`);
                hexadecimal.push(this.getCircleHEXCoordinates(circle));
                wkt.push(this.getCircleWKTCoordinates(circle));
                coordinates.push({
                    lat: circle.centerLatitude,
                    lng: circle.centerLongitude,
                    radius: circle.radius,
                });
            }
            return [
                { label: '点序列', content: pointSequence },
                { label: '16进制', content: hexadecimal },
                { label: 'WKT', content: wkt },
                {
                    label: '轮廓',
                    content: coordinates,
                    isTable: true,
                },
            ];
        },
        // 位置点
        getLocatePointList(planeObjs) {
            const regionCoors = planeObjs.map((item) => {
                return item.point.latLng;
            });
            const pointSequence = [];
            const hexadecimal = [];
            const wkt = [];
            const coordinates = [];
            for (const latLng of regionCoors) {
                const { lat, lng } = latLng;
                pointSequence.push(`${lng},${lat}`);
                hexadecimal.push(`${Number(lng).toString(16)},${Number(lat).toString(16)}`);
                wkt.push(`POINT(${toWKTCoordinates(Number(lng), Number(lat))})`);
                coordinates.push({
                    lat,
                    lng,
                });
            }
            return [
                { label: '点序列', content: pointSequence },
                { label: '16进制', content: hexadecimal },
                { label: 'WKT', content: wkt },
                {
                    label: '轮廓',
                    content: coordinates,
                    isTable: true,
                },
            ];
        },
        // 线段
        getLineSharpList(planeObjs) {
            const regionList = planeObjs.map((item) => item.lineSegment.points);
            const pointSequence = [];
            const hexadecimal = [];
            const wkt = [];
            const coordinates = [];
            // LineString
            for (const line of regionList) {
                pointSequence.push(toPointSequence(line));
                hexadecimal.push(this.getHEXCoordinates(line));
                wkt.push(this.getLineWktCoordinates(line));
                if (coordinates.length) {
                    coordinates.push({}, ...line);
                } else {
                    coordinates.push(...line);
                }
            }
            return [
                { label: '点序列', content: pointSequence },
                { label: '16进制', content: hexadecimal },
                { label: 'WKT', content: wkt },
                {
                    label: '轮廓',
                    content: coordinates,
                    isTable: true,
                },
            ];
        },
        getWKTCoordinates(coordinates) {
            const coors = coordinates.map(({ lng, lat }) => {
                return toWKTCoordinates(Number(lng), Number(lat));
            });
            return `POLYGON((${coors.join(',')}))`;
        },
        getLineWktCoordinates(coordinates) {
            const coors = coordinates.map(({ lng, lat }) => {
                return toWKTCoordinates(Number(lng), Number(lat));
            });
            return `LineString((${coors.join(',')}))`;
        },
        getCircleWKTCoordinates(coordinates) {
            const { centerLongitude, centerLatitude, radius } = coordinates;
            const wktPoint = toWKTCoordinates(Number(centerLongitude), Number(centerLatitude));
            return `POINT(${wktPoint});${radius}`;
        },
        getHEXCoordinates(coordinates) {
            const coors = coordinates.map(({ lng, lat }) => {
                return `${Number(lng).toString(16)},${Number(lat).toString(16)}`;
            });
            return coors.join(';');
        },
        getCircleHEXCoordinates(coordinates) {
            const { centerLongitude, centerLatitude, radius } = coordinates;
            return `${Number(centerLongitude).toString(16)},${Number(centerLatitude).toString(
                16
            )};${radius}`;
        },
    },
};
</script>

<style lang="less" scoped>
.sharp-info {
    width: 370px;
    overflow: auto;
    .head {
        font-size: 16px;
        font-weight: bold;
        padding: 4px 0 12px 0;
        line-height: 22px;
        box-sizing: border-box;
        color: #ffffff;
        .title-dot {
            display: inline-block;
            width: 8px;
            height: 8px;
            background: #53ffff;
            border-radius: 4px;
            vertical-align: middle;
            margin-right: 8px;
        }
        &::before {
            content: '';
            display: block;
            height: 1px;
            width: 100%;
            position: absolute;
            top: 50px;
            left: 0;
            background-color: rgba(0, 0, 0, 0.08);
            z-index: 1;
        }
    }
    .wrap {
        margin-bottom: 8px;

        .title {
            display: inline-block;
            margin-bottom: 4px;
            height: 28px;
            line-height: 28px;
            font-size: 14px;
            font-weight: bold;
            color: #ffffff;
        }
        .content {
            padding: 8px;
            height: 86px;
            border-radius: 4px;
            font-size: 12px;
            overflow: auto;
            color: #ffffff;
            background: rgba(117, 163, 223, 0);
            box-shadow: inset 0px 0px 8px 0px #4984ff;
            border-radius: 4px;
            border: 1px solid #a8bfe8;
        }
    }
}
</style>
