<template>
    <div class="echart-wrapper">
        <el-empty v-if="isEmpty" :image="noData1" description="暂无数据"></el-empty>
        <div v-else :id="name" class="echart-data" v-resize="resize"></div>
    </div>
</template>

<script>
import * as echarts from 'echarts';
import { ChartsCon } from './chartsConfigure.js';
import { resize } from '@/script/common/directives/resize.js';
const initEcharts = (myChart, options, type, that) => {
    const option = ChartsCon[type](options);
    myChart.clear();
    myChart.setOption(option);
};
export default {
    name: 'commonCharts',
    props: {
        data: {
            type: Object,
            default: () => ({}),
        },
        name: {
            type: String,
            default: 'line',
        },
    },
    directives: {
        resize,
    },
    data() {
        return {
            myChart: '',
            noData1: require('../../../img/common/noData1.svg'),
        };
    },
    computed: {
        isEmpty() {
            if (JSON.stringify(this.data) === '{}' || this.data.isNull) {
                return true;
            }
            return false;
        },
    },
    watch: {
        data: {
            handler(newV) {
                if (newV) {
                    this.initEchart();
                }
            },
            deep: true,
        },
    },
    mounted() {
        const timer = setTimeout(() => {
            this.$watch(
                'data',
                (newData) => {
                    this.initEchart();
                },
                { immediate: true,deep:true }
            );
            // window.addEventListener('resize', this.resize);
            clearTimeout(timer);
        }, 0);
    },
    methods: {
        initEchart() {
            if (this.isEmpty) {
                this.myChart && this.myChart.dispose();
                this.myChart = null;
                return;
            }
            this.$nextTick(() => {
                if (!this.myChart) {
                    this.myChart = echarts.init(document.getElementById(this.name), null, {
                        renderer: 'canvas', // 强制使用canvas渲染
                        devicePixelRatio: 2, // 设置设备像素比为2，提高清晰度
                    });
                }
                initEcharts(this.myChart, this.data, this.data.type, this);
            });
        },
        resize() {
            if (this.myChart) {
                this.myChart.resize();
                this.initEchart();//重绘渲染字体
            }
        },
    },
};
</script>

<style lang="less" scoped>
.echart-wrapper {
    width: 100%;
    height: 100%;
    // border: 1px solid #e7e7e7;
    position: relative;
}
.echart-data {
    width: 100%;
    height: 100%;
}
/deep/.el-empty{
    width:100%;
    height:100%;
}
/deep/.el-empty__image{
    width:9rem;
}
/deep/.el-empty__description{
    margin-top:0px;
}
/deep/.el-empty__description p{
    color:#fff;
}
</style>
