const routeParamOfCircle = {
    taskId: 157,
    createUserName: 'xiaozhang',
    taskName: '上海外滩',
    taskStatus: 1,
    taskCreateTime: '2008-05-11 14:10:01',
    executeStartTime: '2008-05-11 14:30:00',
    executeEndTime: '2008-05-20 14:35:00',
    earthquakeName: '上海市闽行区',
    centerLon: 121.32075330947876,
    centerLat: 30.7399921004603,
    analysisRadius: 1000,
    earthquakeLevel: 7.8,
    earthquakeDepth: 10,
    occurTime: '2024-05-01 12:52:26',
    timeType: 1,
    analysisStartTime: '2008-05-11 14:30:00',
    analysisEndTime: '2008-05-20 15:00:00',
    taskNowSustainTime: '2008-05-20 15:00:00',
    populationIds: '1,2',
    heatMapType: 1,
    disasterType: 1,
    taskCreateSource: 1,
    shapeType: 1,
};

const routeParamOfPolygon = {
    taskId: 157,
    createUserName: 'xiaozhang',
    taskName: '上海外滩',
    taskStatus: 1,
    taskCreateTime: '2008-05-11 14:10:01',
    executeStartTime: '2008-05-11 14:30:00',
    executeEndTime: '2008-05-20 14:35:00',
    earthquakeName: '上海市闽行区',
    regionCoors: '121.33440038894653,30.7328894216057;121.3162042829895,30.7336271842476;121.31427309249878,30.7380536414879;121.31581804489136,30.7438815000542;121.32234117721558,30.7432175845938;121.33173963760376,30.738643820423',
    earthquakeLevel: 7.8,
    earthquakeDepth: 10,
    occurTime: '2024-05-01 12:52:26',
    timeType: 1,
    analysisStartTime: '2008-05-11 14:30:00',
    analysisEndTime: '2008-05-20 15:00:00',
    taskNowSustainTime: '2008-05-20 15:00:00',
    populationIds: '1,2',
    heatMapType: 1,
    disasterType: 1,
    taskCreateSource: 1,
    shapeType: 2,
}
const overviewData = {
    accPepCnt: 101,
    laccellCnt: 32,
}

export {
    routeParamOfCircle,
    routeParamOfPolygon,
    overviewData
}