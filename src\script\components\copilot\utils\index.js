// 格式化日期函数
export function formatTime(date, fmt = 'yyyy-MM-dd') {
    var o = {
        'M+': date.getMonth() + 1, //月份
        'd+': date.getDate(), //日
        'h+': date.getHours(), //小时
        'm+': date.getMinutes(), //分
        's+': date.getSeconds(), //秒
        'q+': Math.floor((date.getMonth() + 3) / 3), //季度
        S: date.getMilliseconds() //毫秒
    };
    if (/(y+)/.test(fmt))
        fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length));
    for (var k in o)
        if (new RegExp('(' + k + ')').test(fmt))
            fmt = fmt.replace(
                RegExp.$1,
                RegExp.$1.length == 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length)
            );
    return fmt;
}

export function mergeDataViews(view1, view2) {
    // 创建一个新的ArrayBuffer，大小为两个DataView大小之和
    var buffer = new ArrayBuffer(view1.byteLength + view2.byteLength);
    var view = new DataView(buffer);
    // 复制view1的内容到新的DataView
    for (let i = 0; i < view1.byteLength; i++) {
        view.setUint8(i, view1.getUint8(i, true), true);
    }
    // 复制view2的内容到新的DataView的正确位置
    for (let i = 0; i < view2.byteLength; i++) {
        view.setUint8(i + view1.byteLength, view2.getUint8(i, true), true);
    }
    // 返回合并后的DataView
    return view;
}

export function uuidv4() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        var r = (Math.random() * 16) | 0,
            v = c == 'x' ? r : (r & 0x3) | 0x8;
        return v.toString(16);
    });
}

export function b64encode(str) {
    return btoa(
        encodeURIComponent(str).replace(/%([0-9A-F]{2})/g, function (match, p1) {
            return String.fromCharCode('0x' + p1);
        })
    );
}