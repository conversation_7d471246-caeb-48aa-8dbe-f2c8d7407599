<template>
    <el-drawer
        class="base-manage"
        :modal="false"
        :visible.sync="drawer"
        size="36%"
        custom-class="custom-drawer"
        :before-close="handleClose"
    >
        <!-- 标题 -->
        <template #title>
            <div class="custom-title">
                <span class="text">基站管理</span>
            </div>
        </template>
        <!-- 主体 -->
        <div class="base-manage__tabs">
            <el-tabs v-model="activeName">
                <el-tab-pane v-for="(tab, inx) in tabList" v-bind="tab" :key="inx">
                    <keep-alive>
                        <component
                            v-if="tab.name === activeName"
                            :is="tab.name"
                            :ref="tab.name"
                            v-bind="tab.attrs"
                            v-on="tab.listeners"
                        />
                    </keep-alive>
                </el-tab-pane>
            </el-tabs>
        </div>
        <div class="base-manage__footer">
            <el-button v-if="activeName !== 'noAdd'" size="small" @click="bulkDel"
                >全部移除</el-button
            >
            <el-button v-if="activeName !== 'added'" type="primary" size="small" @click="bulkAdd"
                >全部添加</el-button
            >
        </div>
    </el-drawer>
</template>
<script>
import customTip from '@/script/components/customTip.vue';
import allInfo from './components/allInfo.vue';
import { removeDuplicateByCell } from '@/utils/method.js';
export default {
    name: 'base-manage',
    components: {
        customTip,
        allInfo,
        noAdd: () => import('./components/noAdd.vue'),
        added: () => import('./components/added.vue'),
    },
    props: {
        drawer: {
            type: Boolean,
            default: false,
        },
        row: {
            type: Object,
            default: () => ({}),
        },
        baseStations: {
            type: Array,
            default: () => [],
        },
    },
    data() {
        return {
            activeName: 'added',
            addedBaseStations: [],
            noAddBaseStations: [],
            allBaseStations: [],
            selectData: {
                allInfo: [],
                added: [],
                noAdd: [],
            },
        };
    },
    computed: {
        tabList() {
            return [
                {
                    label: '已添加',
                    name: 'added',
                    attrs: {
                        baseStations: this.addedBaseStations,
                    },
                    listeners: {
                        delAddedBase: this.delAddedBase.bind(this),
                        setSelectList: this.setSelectList.bind(this),
                        setLocation: this.setLocation.bind(this),
                    },
                },
                {
                    label: '待添加',
                    name: 'noAdd',
                    attrs: {
                        baseStations: this.noAddBaseStations,
                    },
                    listeners: {
                        addAddedBase: this.addAddedBase.bind(this),
                        setSelectList: this.setSelectList.bind(this),
                        setLocation: this.setLocation.bind(this),
                    },
                },
            ];
        },
        userInfo() {
            return {};
        },
    },
    watch: {
        addedBaseStations: {
            handler(newStations) {
                this.$emit('getAddedStations', newStations);
            },
            immediate: true,
        },
    },
    created() {
        this.allBaseStations = removeDuplicateByCell(
            this.baseStations.filter((item) => item.onceAdded)
        );
        this.addedBaseStations = this.allBaseStations.filter((item) => item.status === 'added');
        this.noAddBaseStations = this.allBaseStations.filter((item) => item.status === 'noAdd');
    },
    methods: {
        bulkDel() {
            const selectList = this.selectData[this.activeName];
            const validSelectList = selectList.filter((item) => item.status === 'added');
            for (const item of validSelectList) {
                item.status = 'noAdd';
            }
            this.noAddBaseStations.push(...validSelectList);
            this.addedBaseStations = this.addedBaseStations.filter(
                (item) => !validSelectList.includes(item)
            );
            this.$message.success('批量删除成功');
        },
        bulkAdd() {
            const selectList = this.selectData[this.activeName];
            const validSelectList = selectList.filter((item) => item.status === 'noAdd');
            for (const item of validSelectList) {
                item.status = 'added';
            }
            this.addedBaseStations.push(...validSelectList);
            this.noAddBaseStations = this.noAddBaseStations.filter(
                (item) => !validSelectList.includes(item)
            );
            this.$message.success('批量添加成功');
        },
        setSelectList(selectList, type) {
            this.selectData[type] = selectList;
        },
        handleClose() {
            this.$emit('update:drawer', false);
            this.$emit('handleClose');
        },
        addToAddedBase(row) {
            this.addedBaseStations.push(row);
            this.noAddBaseStations = this.noAddBaseStations.filter(
                (item) => item.cgiCode !== row.cgiCode
            );
        },
        addToNoAddBase(row) {
            this.noAddBaseStations.push(row);
            this.addedBaseStations = this.addedBaseStations.filter(
                (item) => item.cgiCode !== row.cgiCode
            );
        },
        delAddedBase(row) {
            const inx = this.addedBaseStations.findIndex((item) => item === row);
            this.addedBaseStations.splice(inx, 1);
            this.noAddBaseStations.push(row);
        },
        addAddedBase(row) {
            const inx = this.noAddBaseStations.findIndex((item) => item === row);
            this.noAddBaseStations.splice(inx, 1);
            this.addedBaseStations.push(row);
        },
        setLocation(latLng) {
            this.$emit('setLocation', latLng);
        },
    },
};
</script>
<style lang="less" scoped>
.base-manage {
    position: absolute;

    .custom-title {
        display: flex;
        padding-left: 12px;
        align-items: center;
        height: 48px;
        font-weight: bold;
        background: rgba(32, 58, 90, 0.6);
        border-radius: 12px 12px 0px 0px;
        backdrop-filter: blur(8px);
        .text {
            font-size: 16px;
            color: #fff;
        }
    }
    &__tabs {
        flex: 1;
        padding: 0 16px 16px 16px;
        overflow: hidden;
        .el-tabs {
            height: 100%;
            display: flex;
            flex-direction: column;
            /deep/ .el-tabs__header {
                .el-tabs__nav-wrap::after {
                    background: #36507c;
                }
                .el-tabs__item {
                    color: #9aafc1;
                    &.is-active {
                        color: #fff;
                    }
                }
            }
        }
        /deep/ .el-tabs__content {
            flex: 1;
            height: 0;
            overflow: unset;
            .el-tab-pane {
                height: 100%;
                display: flex;
                flex-direction: column;
            }
        }
    }
    &__footer {
        margin-bottom: 12px;
        padding-right: 16px;
        text-align: right;
        .el-button {
            width: 24%;
        }
    }
    /deep/ .el-drawer__header {
        margin-bottom: 0;
        padding: 0;
        .el-drawer__close-btn {
            position: absolute;
            right: 16px;
        }
    }
    /deep/ .el-drawer__body {
        display: flex;
        flex-direction: column;
    }
}
/deep/ .custom-drawer {
    max-width: 510px;
    background: rgba(31, 52, 79, 0.8);
    box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.65);
    border-radius: 8px;
    backdrop-filter: blur(8px);
}
</style>
