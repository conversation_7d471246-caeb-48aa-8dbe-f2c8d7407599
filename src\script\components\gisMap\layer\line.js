import {setFitView} from '@/utils/utils.js';
export default (g, options = {}) => {
    // 创建图层
    let layer = new g.layer();
    layer.name = '线';
    g.gis.scene.add(layer);
    layer.visible = true;
    layer.renderOrder = true;
    layer.renderOrderIndex = 1000;
    class line{
        static removeAll(){
            layer.removeAll();
        }
        static createLine(data=[],pointList){
            line.removeAll();
            data.autoScale = true;
            data.width = 3;
            let lineMesh = g.meshList.line.create(data);
            layer.add(lineMesh);
            g.gis.needUpdate = true;
            pointList && setFitView(pointList,g,3);
        }
    }
    return line;
};