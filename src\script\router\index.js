import Vue from 'vue'
import VueRouter from 'vue-router';
import NProgress from 'nprogress';
import 'nprogress/nprogress.css';

Vue.use(VueRouter)

const routes = [
    {
        path: "/",
        redirect: "/home"
    },
    // { 
    //     name:'earthquake',
    //     path: '/',
    //     component: () => import(/* webpackChunkName: "earthquake/index" */ '../plugin2x/main/indexPlugin.vue'),
    // },
    {
        name: 'home',
        path: '/home',
        component: () => import(/* webpackChunkName: "earthquake/home" */ '../plugin2x/main/homePlugin.vue'),//首页
    }, 
    {
        path: '/creation',
        name: 'creation',
        component: () => import(/* webpackChunkName: "earthquake/creation" */ '../plugin2x/taskAnalysis/creationPlugin.vue'),//任务创建
    },
    {
        path: '/myTask',
        name: 'myTask',
        component: () => import(/* webpackChunkName: "earthquake/myTask" */ '../plugin2x/taskAnalysis/myTaskPlugin.vue'),//我的任务
    },
    {
        name: 'analyzeProduct',
        path: '/analyzeProduct',
        component: () => import(/* webpackChunkName: "earthquake/analyzeProduct" */ '../plugin2x/analyzeProduct/indexPlugin.vue'),//分析产物
    },
    {
        name: 'analyzeDetails',
        path: '/analyzeDetails',
        component: () => import(/* webpackChunkName: "earthquake/analyzeDetails" */ '../plugin2x/analyzeDetails/indexPlugin.vue'),//分析详情
    },
    {
        name:'errorPage',
        path: '/errorPage',
        component: () => import(/* webpackChunkName: "earthquake/errorPage" */ '../plugin2x/errorPage/index.vue'),//错误页面
    },
    {
        name:'baseStationSituation',
        path: '/baseStationSituation',
        component: () => import(/* webpackChunkName: "earthquake/baseStationSituation" */ '../plugin2x/baseStationSituation/index.vue'),//基站情况页面
    },
    {
        name:'retiredBaseStation',
        path: '/retiredBaseStation',
        component: () => import(/* webpackChunkName: "earthquake/retiredBaseStation" */ '../plugin2x/retiredBaseStation/index.vue'),//退服基站管理
    },
    {
        name:'presetArea',
        path: '/presetArea',
        component: () => import(/* webpackChunkName: "earthquake/presetArea" */ '../plugin2x/creation/indexPlugin.vue'),//退服基站管理
    },
    {
        name:'resManifest',
        path: '/resManifest',
        component: () => import(/* webpackChunkName: "earthquake/resManifest" */ '../plugin2x/resourceView/resManifest/indexPlugin.vue'),//基站情况详情 
    },
    {
        name:'manifest',
        path: '/manifest',
        component: () => import(/* webpackChunkName: "earthquake/manifest" */ '../plugin2x/resourceView/manifest/indexPlugin.vue'),//基站情况详情 
    },
    {
        path: '/presetCreation',
        name: 'presetCreation',
        component: () => import(/* webpackChunkName: "earthquake/presetCreation" */ '../plugin2x/presetCreation/creationPlugin.vue'),//任务创建
    },
    {
        path: '/resultProduct',
        name: 'resultProduct',
        component: () => import(/* webpackChunkName: "earthquake/resultProduct" */ '../plugin2x/resultProduct/indexPlugin.vue'),//分析产物
    }
]

const router = new VueRouter({
  mode: 'hash',
  base:'/',
  routes
})

router.beforeEach((to, from, next) => {
    if(from.path !== '/errorPage'){
        NProgress.start();
        let { page,token, pageToken } = to.query;
        const query = { ...to.query };
        token && (localStorage.setItem('token',token));
        pageToken && (localStorage.setItem('pageToken',pageToken));
        delete query.page;
        delete query.token;
        delete query.pageToken;
        if (page) {
            const route = {
                name: page,
                replace: true
            };
            route.query = { outside: true, ...query };
            next(route);
            NProgress.done();
        } else {
            next();
            NProgress.done();
        }
    }
});
/**
 * 路由跳转之后执行该函数
 */
router.afterEach(() => {
    NProgress.done()
})

export default router
