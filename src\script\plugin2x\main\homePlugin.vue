<template>
    <div class="home-wrapper">
        <homeMap ref="homeMap"/>
        <div class="home-wrapper-left">
            <titleBar :img="require(`../../../img/overview.png`)" title="平台概览"/>
            <div class="statistics-title">统计总计为最近一个月</div>
            <div class="overview-list" v-for="(item,index) in platformOverview" :key="index">
                <div class="overview-list-item">
                    <img :src="require(`../../../img/home/<USER>" alt="" />
                    <div class="overview-list-item-name">{{item.name}}</div>
                    <div class="overview-list-item-value">{{item.count}}</div>
                </div>
            </div>
            <titleBar :img="require(`../../../img/base-station.png`)" title="基站退服告警"/>
            <div class="alarm-list">
                <div class="alarm earth-overflow-y">
                    <div class="alarm-list-item" v-for="(item,index) in alarmList" :key="index">
                        <span class="title">{{item.time}}</span>
                        <span style="color:#FE3333">{{item.regionName}}退服率为 {{ item.proportion }}</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="home-wrapper-center">
            <div class="text">震级参数</div>
            <div class="legend">
                <div class="earth-flex-row">
                    <div v-for="(item,index) in legend" :key="index" class="legend-item" :style="{'background':item.color}"></div>
                </div>
                <div class="earth-flex-row block">
                    <div class="text text-item" v-for="(item,index) in legendName" :key="index">{{item}}</div>
                </div>
            </div>
        </div>
        <div class="home-wrapper-right">
            <titleBar :img="require(`../../../img/earth-list.png`)" title="地震列表"/>
            <div class="list">
                <searchBar class="search-form" :fields="formCols" :form="form">
                    <template>
                        <el-button
                            type="primary"
                            size="small"
                            @click="search()"
                            >查询</el-button
                        >
                        <div class="showExpand" slot="showExpand" @click="clickExpand">
                            {{isExpand?'收起':'展开'}} &nbsp;<i :class="[isExpand?'el-icon-arrow-up':'el-icon-arrow-down']"></i>
                        </div>
                    </template>
                </searchBar>
                <dataTable
                    class="data-table"
                    :class="[isExpand?'shrinkHeight':'']"
                    :columns="columns"
                    :data="tableData"
                    :pagination="paginationData"
                    :total="total"
                    :updateTable="getTableData"
                >
                    <!-- 震级 -->
                    <template #earthquakeLevel="{ row }">
                        <div
                            v-if="row.earthquakeLevel != null"
                            class="row-list"
                            :style="[earthLevelBackground(row)]"
                        >
                            <span class="circle" :style="[earthLevelColor(row)]"></span>
                            <span class="earthquakeLevel-text">{{ row.earthquakeLevel }}</span>
                        </div>
                    </template>
                    <template #操作="{ row }">
                        <el-button style="color:#53FFFF" type="text" @click="openDetail(row)">分析</el-button>
                    </template>
                </dataTable>
            </div>
        </div>
        <div class="nanhai"></div>
    </div>
</template>

<script>
import homeMap from '_com/gisMap/homeMap.vue';
import {platformOverview,tableColumn,formCols} from '_const/home.js';
import searchBar from '_com/searchBar/searchBar.vue';
import dataTable from '_com/table/dataTable.vue';
import titleBar from '_com/titleBar/index.vue';
const dayjs = require('dayjs');
export default {
    name:'home',
    components:{
        homeMap,
        searchBar,
        dataTable,
        titleBar
    },
    data(){
        return{
            platformOverview:platformOverview,
            formCols: formCols(),
            form: {
                earthquakeName: '',
                earthquakeDepthLevel: '',
                earthquakeScaleLevel: '',
                time: [],
            },
            columns: tableColumn,
            tableData: [],
            paginationData: {
                curPage: 1,
                pageSize: 20,
            },
            total: 0,
            isExpand:false,
            legend:[
                {color:'#30B34A',min:0,max:3,background:'rgba(61, 242, 88, 0.2)'},
                {color:'#4BB8BF',min:3,max:6,background:'rgba(98, 249, 255, 0.20)'},
                {color:'#B2A91F',min:6,max:9,background:'rgba(248, 231, 28, 0.2)'},
                {color:'#800080 ',min:9,max:12,background:'rgba(128, 0, 128, 0.2)'},
                {color:'#B72C2F',min:12,max:15,background:'rgba(254, 51, 51, 0.2)'},
            ],
            legendName:['4M','5M','6M','7M','8M', '9M'],
            alarmList: [],
            timer: null,
        };
    },
    computed:{
        earthLevelBackground(){
            return function(val){
                const color = this.handlerPointColor(val.earthquakeLevel,'background');
                return {
                    'background-color':color,
                }
            }
        },
        earthLevelColor(){
            return function(val){
                const color = this.handlerPointColor(val.earthquakeLevel);
                return {
                    'background-color':color,
                }
            }
        }
    },
    created(){
        
    },
    mounted() {
        this.getOverview();
        this.search();
        // this.getStationAlertList();
        // this.startTimer();
    },
    activated(){
        this.getStationAlertList();
        this.startTimer();
    },
    deactivated(){
        clearInterval(this.timer);
        this.timer = null;
    },
    beforeDestroy(){
        clearInterval(this.timer);
        this.timer = null;
    },
    methods:{
        clickExpand(){
            this.isExpand = !this.isExpand;
            this.formCols = formCols(this.isExpand);
        },
        openDetail(row){
            this.$router.push({
                name: 'creation',
                params: row,
            });
        },
        search() {
            this.paginationData.curPage = 1;
            this.getTableData({ curPage: 1 });
        },
        async getTableData(paginationData = {}) {
            const { curPage = 1, pageSize = 20 } = paginationData;
            const { earthquakeName, earthquakeDepthLevel, earthquakeScaleLevel, time } = this.form;
            let params = {
                pageSize,
                currentPage: curPage,
                burialPepTaskPara: {
                    earthquakeName,
                    earthquakeDepthLevel,
                    earthquakeScaleLevel,
                },
            };
            if (time && time[1]) {
                params.endTime = time && time[1];
            }
            if (time && time[0]) {
                params.startTime = time && time[0];
            }
            this.getPost('post', 'getPage', params, '获取地震列表信息', (res) => {
                this.total = res.totalPageNum;
                this.tableData = res.earthquakeList;
                this.renderMapData();
            });
        },
        renderMapData(){
            const data = this.tableData.map((item) => {
                return {
                    name: item.earthquakeName,
                    earthquakeLevel:item.earthquakeLevel,
                    color:this.handlerPointColor(item.earthquakeLevel),
                    point:{
                        lat:Number(item.centerLat),
                        lng:Number(item.centerLon),
                    }
                };
            });
            this.$nextTick(() => {
                this.$refs.homeMap.addRippleLayer(data);
            });
        },
        handlerPointColor(num,key = 'color'){
            const colorList = this.legend;
            const list = colorList.filter((item) => {
                if(num >= Number(item.min) && num < Number(item.max)){
                    return item;
                }
            });
            return list[0] && list[0][key] || colorList[0][key];
        },
        getOverview() {
            this.getPost(
                'post',
                'getOverview',
                {
                    startTime: dayjs().subtract(1, 'month').format('YYYY-MM-DD 00:00:00'),
                    endTime: dayjs().format('YYYY-MM-DD 00:00:00'),
                },
                '获取平台概览信息',
                (res) => {
                    this.platformOverview.forEach((item) => {
                        item.count = res[item.props] || '-';
                    });
                }
            );
        },
        getStationAlertList() {
            this.getPost('post', 'getBaseStationAlarm', {
                regionLevel: 1,
            }, '获取基站退服告警列表信息', (res) => {
                this.alarmList = res.dataList || [];
            });
        },
        startTimer() {
            if (this.timer) clearInterval(this.timer);
            this.timer = setInterval(() => {
                this.getStationAlertList();
            }, 10 * 60 * 1000);
        },
    }
}
</script>

<style lang="less" scoped>
.home-wrapper{
    width:100%;
    height:100%;
    position:relative;
    &-left{
        position:absolute;
        top:26px;
        left:26px;
        display:flex;
        flex-direction:column;
        width: 327px;
        height: calc(100% - 36px);
        >img{
            width:512px;
            z-index:2;
        }
        .statistics-title{
            width:100%;
            height:80px;
            line-height:80px;
            font-size:14px;
            position:relative;
            padding-left:20px;
            color:#fff;
            z-index:2;
            &::after{
                position:absolute;
                content:'';
                top:35px;
                left:5px;
                width:8px;
                height:8px;
                background: #53FFFF;
                border-radius:50%;
            }
        }
        .overview-list{
            width:327px;
            height:100px;
            position:static;
            z-index:2;
            &-item{
                display:flex;
                align-items:center;
                background:url('../../../img/home-bar.png') no-repeat center center /100% 100%;
                width:100%;
                height:69px;
                padding-bottom:13px;
                img{
                    width:34px;
                    height:45px;
                }
                &-name{
                    font-weight: 500;
                    font-size: 16px;
                    color: #FFFFFF;
                    width:180px;
                    padding-left:15px;
                }
                &-value{
                    font-size: 24px;
                    color: #53FFFF;
                    font-weight: 600;
                }
            }
        }
        .alarm-list{
            width:300px;
            height:calc(100% - 464px);
            padding:20px 10px 10px;
            position: static;
            z-index: 2;
            .alarm{
                width:100%;
                height:100%;
                padding-right:10px;
            }
            &-item{
                height: 64px;
                display: flex;
                flex-direction: column;
                justify-content: space-evenly;
                width:100%;
                background: #1B293B;
                box-shadow: inset 0px -1px 0px 0px rgba(0,0,0,0.08);
                opacity: 0.7;
                padding-left:15px;
                color:#9AAFC1;
                font-size:16px;
            }
        }
        .alarm-list-item + .alarm-list-item{
            margin-top:3px;
        }
    }
    &-right{
        position:absolute;
        top:26px;
        right:26px;
        display:flex;
        flex-direction:column;
        width:692px;
        height:100%;
        z-index:2;
        .list{
            width:100%;
            height:calc(100% - 42px);
            display:flex;
            flex-direction:column;
            padding-top:15px;
        }
    }
    &-center{
        position:absolute;
        top:35px;
        left:calc(45% - 215px);
        display:flex;
        z-index:2;
        width:500px;
        .legend{
            padding-left:20px;
            padding-top:5px;
            .legend-item{
                width: 72px;
                height: 8px;
            }
            .text-item{
                width:70px;
                padding-top:10px;
            }
        }

    }
    .nanhai{
        position:absolute;
        right:730px;
        bottom:26px;
        width:135px;
        height:205px;
        background:url('../../../img/home/<USER>') no-repeat center center /100% 100%;
        z-index:2;
    }
}
.showExpand{
    font-size: 14px;
    color: #3871B3;
    cursor:pointer;
}
.data-table {
    width:100%;
    height:calc(100% - 100.55px);
    &.shrinkHeight{
        height:calc(100% - 145.1px);
    }
}
/deep/.el-button--primary{
    background-color:#3871B3;
}
.text{
    font-size: 15px;
    color: #FFFFFF;
    line-height: 22px;
    font-weight: bold;
    // text-shadow:0 0 2px #fff, 0 0 2px #fff, 0 0 2px #fff, 0 0 4px #3E88E9,0px 0px 2px rgba(62,136,233,0.64);
}
.row-list {
    display: flex;
    align-items: center;
    width: 90%;
    height: 20px;
    border-radius: 2px;
    padding:0 10Px;
    .circle{
        width: 6Px;
        height: 6Px;
        border-radius: 50%;
        margin-right: 10Px;
    }
    .earthquakeLevel-text{
        font-size:14Px;
    }
}
.title-text{
    position:absolute;
    left: 40px;
    top: 11px;
    color: #fff;
    font-size: 16px;
    font-weight: 600;
    text-shadow: 0px 0px 0.22222rem rgba(62, 136, 233, 0.64);
}
</style>