.gis-com {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
  /deep/ .pcGis > .buttonControl {
    right: 10px;
    bottom: auto;
    top: 10px;
  }
}
.gis-com__tools {
  display: flex;
  position: absolute;
  left: 10px;
  top: 10px;
  z-index: 10;
}

.btn-box {
  display: inline-block;
  padding: 0 10px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  color: #fff;
  font-weight: 500;
  font-size: 16px;
  cursor: pointer;
  background-color: #1F3459;
  img {
    width: 24px;
    height: 24px;
    display: inline-block;
    margin-right: 4px;
    vertical-align: middle;
  }
  .earth-icon {
    margin-right: 1px;
    vertical-align: text-top;
  }
}
.select-district {
  margin-left: 10px;
  width: 166px !important;
  /deep/ .el-input__inner {
    height: 40px;
    background: rgba(117, 163, 223, 0);
    box-shadow: inset 0px 0px 8px 0px #4984ff;
    border-radius: 2px;
    border: 1px solid #a8bfe8;
  }
}
.toolbar {
  display: flex;
  margin-left: 10px;
  border-radius: 4px;
}
.gis-tool {
  display: flex;
  margin-left: 10px;
  border-radius: 4px;
  cursor: pointer;
}
.radius-left {
  border-radius: 4px 0 0 4px;
}
.radius-right {
  border-radius: 0 4px 4px 0;
}
.custom-select {
  /deep/ .el-input__inner {
    padding: 0;
    width: 96px;
    font-weight: 500;
    font-size: 16px;
    border: none;
    color: #fff;
    cursor: pointer;
    background-color: transparent;
  }
  /deep/.el-input__suffix {
    height: auto;
    .el-select__caret {
      color: #eee;
    }
  }
}
