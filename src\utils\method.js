import _ from 'lodash';
import proj4 from 'proj4';
import { MD5 } from 'crypto-js';
import excel from '@/utils/excel.js';
import dayjs from 'dayjs';
import MTB64Coder from '@/utils/utilmtenc.js';
// 将扁平数据转化成树形
const formatAreas = (srcData = [], isSort = false) => {
    if (isSort) {
        srcData = _.orderBy(srcData, ['provinceCode', 'cityCode'], ['asc', 'asc']);
    }
    const result = [];
    let provinceObj = {};
    let cityObj = {};
    for (const { provinceName, provinceCode, districtName, districtCode, cityName, cityCode } of srcData) {
        // set province
        if (provinceObj.label !== provinceName) {
            provinceObj = {
                label: provinceName,
                value: provinceCode,
                children: [],
            };
            result.push(provinceObj);
        }
        // set city
        if (cityObj.label !== cityName) {
            cityObj = {
                label: cityName,
                value: cityCode,
                children: []
            };
            provinceObj.children.push(cityObj);
        }
        // set district
        cityObj.children.push({
            label: districtName,
            value: districtCode
        });
    }
    return result;
}
// 格式化地域字段
const formatField = (srcData = [], mapField = {
    typeCode: 'value',
    subTypeCode: 'value',
    classifyCode: 'value',
    typeName: 'label',
    subTypeName: 'label',
    classifyName: 'label',
    subTypeDetailList: 'children',
    classifyDetailList: 'children',
}) => {
    const res = [];
    for (const item of srcData) {
        const obj = {};
        Object.keys(item).forEach(key => {
            const needKey = mapField[key];
            if (needKey === 'children') {
                obj[needKey] = formatField(item[key]);
            } else {
                obj[needKey] = item[key];
            }
        })
        res.push(obj);
    }
    return res;
}
// 将坐标转化为 wkt
const toWKTCoordinates = (longitude, latitude) => {
    // 定义源坐标系和目标坐标系的EPSG代码
    const sourceEPSG = 'EPSG:4326'; // WGS84经纬度坐标系
    const targetEPSG = 'EPSG:3857'; // Web墨卡托投影坐标系
    // 定义源坐标系和目标坐标系的投影转换函数
    const sourceProjection = proj4(sourceEPSG);
    const targetProjection = proj4(targetEPSG);
    // 将经纬度坐标转换为目标坐标系的坐标
    const [x, y] = proj4(sourceProjection, targetProjection, [longitude, latitude]);
    // 构造WKT类型的坐标
    return `${x} ${y}`;
};
const getAreasToCity = (srcDistricts, isNeedAllSelect = false) => {
    const result = [];
    for (const { value, label, children } of srcDistricts) {
        const newItem = {
            label,
            value,
            children: isNeedAllSelect ? [{
                label: '全部',
                value: `${value}-all`
            }] : [],
        };
        result.push(newItem);
        for (const { value, label } of children) {
            newItem.children.push({
                label,
                value,
            });
        }
    }
    return result;
};
const getLabel = (curVal, tarList = []) => {
    const curItem = tarList.find(item => item.value == curVal);
    return curItem ? curItem.label : '';
}
const getLabels = (layerId = '', layers) => {
    const labels = [];
    layerId.split(',').forEach(id => {
        const curItem = layers.find(item => item.value == id);
        if (curItem) {
            labels.push(curItem.label);
        }
    })
    return labels.join('、');
}
const removeDuplicateLocations = (locations) => {
    const seen = new Map();
    const result = [];
    locations.forEach((location, index) => {
        const key = `${location.lng}-${location.lat}`;
        const seenLocation = seen.get(key);
        if (seenLocation) {
            seenLocation.repeatIndex.push(index);
            if (seenLocation.status !== 'added' && location.status === 'added') {
                seen.set(key, location);
                location.repeatIndex = seenLocation.repeatIndex;
                seenLocation.repeatIndex = [];
            }
        } else {
            location['repeatIndex'] = [index];
            seen.set(key, location);
        }
    });
    seen.forEach((value) => {
        result.push(value);
    });
    return result;
}
const removeDuplicateByCell = (locations) => {
    const seen = new Map();
    const result = [];
    locations.forEach((location) => {
        const key = `${location.lac}-${location.cell}`;
        const seenLocation = seen.get(key);
        if (seenLocation) {
            if (seenLocation.status !== 'added' && location.status === 'added') {
                seen.set(key, location);
            }
        } else {
            seen.set(key, location);
        }
    });
    seen.forEach((value) => {
        result.push(value);
    });
    return result;
}
// 求差集
const getDiffSet = (bigSet, smallSet, callback) => bigSet.filter(item => {
    const LACCELL = `${item.lac}-${item.cell}`;
    const isExclude = !smallSet.some(it => LACCELL === `${it.lac}-${it.cell}`);
    if (isExclude && callback) callback(item);
    return isExclude;
})

// 求交集
const getIntersectionSet = (bigSet, smallSet) => bigSet.filter(item => {
    const LACCELL = `${item.lac}-${item.cell}`;
    return smallSet.some(it => LACCELL === `${it.lac}-${it.cell}`);
})

//排序
const compareSort = (prop, sort = 'asce') => {
    return function (a, b) {
        if (sort === 'asce') {
            return a[prop] - b[prop];
        }
        return b[prop] - a[prop];
    };
};
const addLastStatus = (srcBaseStations, newBaseStations, callback) => {
    const baseStations = [];
    for (const item of newBaseStations) {
        const LACCELL = `${item.lac}-${item.cell}`;
        const tarItem = srcBaseStations.find(it => `${it.lac}-${it.cell}` === LACCELL);
        if (tarItem) {
            item.status = tarItem.status;
            item.onceAdded = tarItem.onceAdded;
            item.isArea = tarItem.isArea;
            callback && callback(item, tarItem);
        }
        baseStations.push(item);
    }
    return baseStations;
}
const addIncStations = (srcBaseStations, newBaseStations) => {
    const result = [];
    for (const item of srcBaseStations) {
        const lngLat = `${item.lng},${item.lat}`;
        const tarItem = newBaseStations.find(it => `${it.lng},${it.lat}` === lngLat);
        if (tarItem) {
            if (tarItem.status === 'added') {
                item.status = tarItem.status;
            }
        } else {
            result.push(tarItem);
        }
    }
    return result;
}
const toCoordinate = (regionCharts = '') => {
    return regionCharts.split(';')
        .map((item) => {
            const [lng, lat] = item.split(',');
            return lng && lat ? { lng: Number(lng), lat: Number(lat) } : null;
        })
        .filter(Boolean);
}
const toPointSequence = (regionCoors = [], delimiter = ';') => {
    return regionCoors.map(({ lat, lng }) => `${lng},${lat}`)
        .join(delimiter);
}
const formatBaseStations = (baseStations, status, callback) => {
    const result = [];
    const isArea = status === 'added' ? '是' : '否';
    const onceAdded = status === 'added';
    for (const item of baseStations) {
        const curItem = {
            ...item,
            lng: item.cellLongitude,
            lat: item.cellLatitude,
            dir: 0,
            ht: 20,
            size: 30,
            status,
            isArea,
            onceAdded,
        }
        callback && callback(curItem);
        result.push(curItem);
    }
    return result;
}
const arrayToString = (arr) => {
    return arr.map(subArr => subArr.join(','));
}

const getRegionLabels = (idData, treeData) => {
    const result = [];
    idData.forEach(id => {
        const findLabel = (data, path = []) => {
            for (let i = 0; i < data.length; i++) {
                const item = data[i];
                if (item.value === id) {
                    result.push([...path, item.label].join('/'));
                    break;
                } else if (item.children) {
                    findLabel(item.children, [...path, item.label]);
                }
            }
        };
        findLabel(treeData);
    });
    return result.join('、');
}
const isExistWithinCurArea = (curRegions, tarRegions, g) => {
    if (!curRegions || !tarRegions) return;
    const curPoints = toCoordinate(curRegions);
    const tarPoints = toCoordinate(tarRegions);
    const curPoints3 = curPoints.map(point => g.math.world2three(point));
    const tarPoints3 = tarPoints.map(point => g.math.world2three(point));
    const results = g.math.positionTest(curPoints3, tarPoints3);
    return results.length && results.every(Boolean);
}
const isExistWithinCurCircle = (curRegions, tarCircle, g) => {
    if (!curRegions || !tarCircle) return;
    let { centerLatitude, centerX, centerLongitude, centerY, radius } = tarCircle;
    let lat, lng;
    if (centerX && centerY) {
        lat = centerY;
        lng = centerX;
    } else {
        lat = centerLatitude;
        lng = centerLongitude;
    }
    const circlePoint = { lat, lng };
    const curPoints = toCoordinate(curRegions);
    const result = curPoints.every(point => radius >= g.math.worldDistance(point, circlePoint));
    return curPoints.length && result;
};
const addInxForHollowList = (regionList = [], holeList = [], g) => {
    const newHoleList = [];
    const copyHoleList = [...holeList];
    for (const [inx, region] of regionList.entries()) {
        let index = 0;
        for (const [holeInx, hole] of holeList.entries()) {
            const isExist = isExistWithinCurArea(hole.polygon, region.polygon, g);
            if (isExist) {
                newHoleList.push({
                    ...hole,
                    index: index++,
                    region: inx,
                });
                copyHoleList[holeInx] = null;
            }
        }
    }
    return {
        holeList: newHoleList,
        invalidHoleList: copyHoleList.filter(Boolean),
    }
}
const formatEnterAreas = (regionList, holeList, g, type = 1) => {
    const holeRegionCoors = [];
    const copyHoleList = [...holeList];
    const isExistWithinArea = type === 1 ? isExistWithinCurCircle : isExistWithinCurArea;
    for (const [inx, region] of regionList.entries()) {
        let index = 0;
        for (const [holeInx, hole] of copyHoleList.entries()) {
            const isExist = isExistWithinArea(hole, region, g);
            if (isExist) {
                holeRegionCoors.push({
                    polygon: hole,
                    areaType: 2,
                    index: index++,
                    region: inx,
                });
                copyHoleList[holeInx] = null;
            }
        }
    }
    return {
        holeRegionCoors,
        invalidHoleList: copyHoleList.filter(Boolean),
    };
}
const isImportCreated = (holeList = []) => {
    const firstItem = holeList[0] || {};
    return !('region' in firstItem) || !('index' in firstItem);
}
const positionCircleTest = (tarPoints, circle, g) => {
    let { centerLatitude, centerLongitude, radius = 0 } = circle;
    radius = radius / 1000;
    return tarPoints.map(tarPoint => {
        return radius >= g.math.worldDistance(tarPoint, { lat: centerLatitude, lng: centerLongitude });
    })
}
const getRegions = (baseInfo, shapeType) => {
    const isPolygon = shapeType === 2;
    const isLine = shapeType === 4;
    const isLocatePoint = shapeType === 5;
    const { multiPolygonList, regionCoors, circle = {} } = baseInfo;
    if (isLocatePoint) { // 位置点
        const regionList = regionCoors.split('|');
        return regionList.map(item => {
            const [lng, lat] = item.split(',');
            return { lng, lat };
        })
    } else if (isLine) {
        return (regionCoors || '').split('|');
    } else if (multiPolygonList && multiPolygonList.length) {
        return multiPolygonList.map((it) => {
            return isPolygon
                ? it.polygon
                : {
                    centerLatitude: it.centerY,
                    centerLongitude: it.centerX,
                    radius: it.radius,
                };
        });
    } else {
        return isPolygon ? (regionCoors || '').split('|') : [circle];
    }
}
const getRegionGroupInfo = (tableData) => {
    const hollowList = [];
    const multiList = [];
    for (const item of tableData) {
        const { hollowPolygonList = [], multiPolygonList = [], circle = {}, regionCoors = '', shapeType } = item;
        const isPolygon = shapeType === 2;
        const isLine = shapeType === 4;
        const isLocatePoint = shapeType === 5;
        hollowList.push(...hollowPolygonList.map(item => item.polygon));
        if (isLocatePoint || isLine) { // 位置点
            const regionList = regionCoors.split('|');
            regionList.forEach(item => {
                const [lng, lat] = item.split(',');
                multiList.push({ lng, lat });
            })
        } else if (multiPolygonList && multiPolygonList.length) {
            if (isPolygon) {
                multiList.push(...multiPolygonList.map(item => item.polygon));
            } else {
                multiList.push(...multiPolygonList.map(it => ({
                    centerLatitude: it.centerY,
                    centerLongitude: it.centerX,
                    radius: it.radius,
                })));
            }
        } else {
            multiList.push(isPolygon ? regionCoors : circle);
        }
    }
    return {
        hollowList,
        multiList,
    };
}
const getCoordinateExtremum = (coordinates = []) => {
    if (coordinates.length <= 4) {
        return coordinates;
    }
    let minLat, minLng, maxLat, maxLng;
    for (const item of coordinates) {
        if (!minLat || minLat.lat < item.lat) {
            minLat = item;
        }
        if (!maxLat || maxLat.lat > item.lat) {
            maxLat = item;
        }
        if (!minLng || minLng.lng < item.lng) {
            minLng = item;
        }
        if (!maxLng || maxLng.lng > item.lng) {
            maxLng = item;
        }
    }
    return [minLat, maxLat, minLng, maxLng];
}
// 获取起止日期的开区间
const getMonthsInRange = (startDate, endDate) => {
    if (!startDate || !endDate) return [];
    let start = new Date(startDate);
    let end = new Date(endDate);
    let months = [];
    let year, month;

    while (start <= end) {
        year = start.getFullYear();
        month = start.getMonth() + 1; // JavaScript months are 0-indexed
        months.push(`${year}-${month.toString().padStart(2, '0')}`); // Format as "yyyy-MM"
        start.setMonth(start.getMonth() + 1);
    }
    return months;
}
// 获取起止季度的开区间
const getQuartersInRange = (startQuarter, endQuarter) => {
    if (!startQuarter || !endQuarter) return [];
    let startYear = Number(new Date(startQuarter).format('yyyy'));
    let endYear = Number(new Date(endQuarter).format('yyyy'));
    let startMonth = Number(new Date(startQuarter).format('MM'));
    let endMonth = Number(new Date(endQuarter).format('MM'));
    let quarters = [];

    while (startYear < endYear || (startYear === endYear && startMonth <= endMonth)) {
        quarters.push(`${startYear}-${startMonth.toString().padStart(2, 'Q')}`);
        if (startMonth === 4) {
            startYear++;
            startMonth = 1;
        } else {
            startMonth += 1;
        }
    }
    return quarters;
}
const exportExcel = ({ tableData = [], filename, wsConfig, columns = [] }, callback) => {
    let excelData = [];

    if (typeof callback !== 'function') {
        const mapKeyToName = {};
        columns.forEach(({ prop, label }) => {
            mapKeyToName[prop] = label;
        })
        const tableString = JSON.stringify(tableData).replace(/"(\w+)":/g, (match, key) => {
            return `"${mapKeyToName[key] || key}":`;
        });
        excelData = JSON.parse(tableString);
    } else {
        excelData = tableData.map(item => callback(item));
    }

    const config = wsConfig || [
        {
            key: '!cols',
            value: new Array(columns.length).fill({ wch: 40 }),
        },
    ]

    excel.exportExcel(excelData, filename, config);
}
// 日期转化成对应季度
const toQuarter = (date, { formatSet = ['01', '02', '03', '04'], Splitter = '-' } = {}) => {
    const curDate = date || new Date();
    const year = Number(new Date(curDate).format('yyyy'));
    const month = Number(new Date(curDate).format('M'));
    let quarter = '00';
    if (month <= 3) {
        quarter = formatSet[0];
    } else if (month <= 6) {
        quarter = formatSet[1];
    } else if (month <= 9) {
        quarter = formatSet[2];
    } else if (month <= 12) {
        quarter = formatSet[3];
    }
    return `${year}${Splitter}${quarter}`;
}
// 转化成百分比
const toPercentage = (value, { decimalPlaces = 2, isNeedUnit = true } = {}) => {
    const newVal = Number(value || 0) * 100;
    const rate = Number.isInteger(newVal) ? newVal : Number(newVal.toFixed(decimalPlaces));
    return isNeedUnit ? `${rate}%` : rate;
}

const generateRandomString = (chars, len = 28) => {
    const hash = MD5(chars).toString(); // 假设CryptoJS已经被引入并可用  
    const shareCode = hash.substring(0, len);
    return shareCode;
};
// 获取最近12个月
const getTheLatestNMonths = (n) => {
    const months = [];
    for (let i = 0; i < n; i++) {
        months.push(dayjs().subtract(i, 'month').format('YYYY-MM'));
    }
    return months.reverse();
};
// 将大数转化为千分位
const toThousandthPercent = (number) => {
    let formatter = new Intl.NumberFormat('en-US'); // 使用美国英语的环境  
    return formatter.format(number);
};
// 排序，自定义项排前面，其他后置
const sortedHead = (list, sort) => {
    const [heads, others] = list.reduce(
        ([heads, others], item) => {
            const isHead = sort && sort(item);
            if (isHead) {
                heads.push(item);
            } else {
                others.push(item);
            }
            return [heads, others];
        },
        [[], []]
    );
    return [...heads, ...others];
};
const objectToUrlParams = (obj)=> {
    if (!obj) return '';
    const params = Object.entries(obj)
        .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
        .join('&');
    return params ? `?${params}` : '';
}
const generateUUID = () => {
    var d = new Date().getTime(); //Timestamp
    var d2 = (performance && performance.now && performance.now() * 1000) || 0; //Time in microseconds since page-load or 0 if unsupported
    return 'xxxxxxxx-xxxx-xxxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        var r = Math.random() * 16; //random number between 0 and 16
        if (d > 0) {
            //Use timestamp until depleted
            r = (d + r) % 16 | 0;
            d = Math.floor(d / 16);
        } else {
            //Use microseconds since page-load if supported
            r = (d2 + r) % 16 | 0;
            d2 = Math.floor(d2 / 16);
        }
        return (c === 'x' ? r : (r & 0x3) | 0x8).toString(16);
    });
};

const getNonce = () => {
    return new MTB64Coder().encodeM2(generateUUID() + '_' + new Date().getTime());
};

/**
 * 将GeoJSON坐标格式转换为{lng, lat}格式的点列表
 * @param {Array<Array<Array<number>>>} coordinates - GeoJSON格式的坐标数组
 * @returns {Array<Array<{lng: number, lat: number}>>} - 转换后的点列表，每个多边形由经纬度点组成
 */
const fromCoordinate = coordinates => {
    const pointsList = []
    for (const polygon of coordinates) {
        const points = []
        for (const coord of polygon) {
            // 跳过最后一个点，因为它通常是闭合多边形的重复点
            if (points.length > 0 && points[0].lng === coord[0] && points[0].lat === coord[1]) {
                continue
            }
            points.push({ lng: coord[0], lat: coord[1] })
        }
        pointsList.push(points)
    }
    return pointsList
}

export {
    formatAreas,
    formatField,
    toWKTCoordinates,
    getAreasToCity,
    getLabel,
    getLabels,
    removeDuplicateLocations,
    removeDuplicateByCell,
    addLastStatus,
    toCoordinate,
    toPointSequence,
    formatBaseStations,
    addIncStations,
    arrayToString,
    getRegionLabels,
    getDiffSet,
    getIntersectionSet,
    compareSort,
    isExistWithinCurArea,
    addInxForHollowList,
    isImportCreated,
    formatEnterAreas,
    positionCircleTest,
    isExistWithinCurCircle,
    getRegions,
    getRegionGroupInfo,
    getCoordinateExtremum,
    getMonthsInRange,
    exportExcel,
    toQuarter,
    getQuartersInRange,
    toPercentage,
    generateRandomString,
    getTheLatestNMonths,
    toThousandthPercent,
    sortedHead,
    objectToUrlParams,
    generateUUID,
    getNonce,
    fromCoordinate
};