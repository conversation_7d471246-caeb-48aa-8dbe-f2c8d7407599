import Typed from 'typed.js';
import Speech from 'speak-tts';
import marked from 'marked';
import { EventBus } from '../common/eventBus.js';
export default {
    data() {
        return {
            isComplete: false
        };
    },
    created() {
        this.initSpeech();
    },
    watch: {
        isComplete: {
            handler(newV) {
                if (newV) {
                    EventBus.$emit('isTypeComplete', true);
                }
            }
        }
    },
    methods: {
        initSpeech() {
            this.speech = new Speech();
            this.speech.setLanguage('zh-CN');
            this.speech
                .init({
                    rate: 2,
                    volume: 1, // 音量
                    pitch: 1, // 音调
                    splitSentences: true // 在句子结束时暂停
                })
                .then((data) => {
                    console.warn('语音已准备好，声音可用', data);
                })
                .catch((e) => {
                    console.error('初始化存在错误：', e);
                });
        },
        stopSpeech() {
            if (this.speech) {
                this.speech.cancel();
            }
        },
        // 语音控制
        setVoice(text, listeners = {}) {
            this.stopSpeech();
            this.speech.speak({
                text: text,
                queue: false,
                listeners: listeners
            });
        },
        // 打字播放控制
        setPlay(content, index) {
            const htmlContent = marked(content);
            this.talkRecordList[index].content = htmlContent;
            this.talkRecordList[index].isComplete = true;
            this.$nextTick(() => {
                // 打字完成后滚动条定位
                const robotEle = this.$refs.content;
                robotEle.scrollTop = robotEle.scrollHeight;
            });
            // this.clearScrollTimer();
            /* const el = this.$el.querySelector(`.talk-item:nth-child(${index + 1}) .text`);
            this.typewriter(content, el)
            .then(() => {
                console.log(this.talkRecordList[index]);
                this.talkRecordList[index].content = content;
                this.talkRecordList[index].isComplete = true;
                this.$nextTick(() => {
                    // 打字完成后滚动条定位
                    const robotEle = this.$refs.content;
                    robotEle.scrollTop = robotEle.scrollHeight;
                });
            }); */
        },
        destroyType() {
            this.typed && this.typed.destroy();
        },
        /**
         *
         * @param {*} text 打字文本
         * @param {*} container 打字机容器
         * @param {*} loop 是否循环
         * @returns
         */
        typewriter(text, container, loop = false) {
            // 渲染
            const htmlContent = marked(text);
            // this.destroyType();
            this.isComplete = false;
            return new Promise((resolve, reject) => {
                try {
                    this.typed = new Typed(container, {
                        strings: [htmlContent],
                        typeSpeed: this.speed,
                        showCursor: false,
                        cursorChar: '',
                        loop,
                        startDelay: 0,
                        onComplete: (self) => {
                            self.el.innerHTML = self.strings[0]; // 保留最终文本
                            self.destroy(); // 销毁实例但不清空内容
                            this.isComplete = true;
                            this.clearScrollTimer();
                            resolve();
                        }
                    });
                } catch (error) {
                    reject(error);
                }
            });
        },
        clearScrollTimer() {
            this.scrollTimer && clearInterval(this.scrollTimer);
        },
        setScroll() {
            this.clearScrollTimer();
            const robotEle = this.$refs.content;
            if (!robotEle) {
                return;
            }
            this.scrollTimer = setInterval(() => {
                robotEle.scrollTop = robotEle.scrollHeight;
            }, 500);
        }
    }
};
