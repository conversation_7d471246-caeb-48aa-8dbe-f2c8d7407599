<template>
    <div class="gis-map">
        <div id="home-gis" class="earth-h-full" ref="mapRef"></div>
        <div class="tip">已获得高德地图的地图服务使用授权</div>
    </div>
</template>

<script>
const { GIS } = window.MTGIS;
import { gisOptions, cityOption } from '_const/gis.js';
import {Decrypt} from '@/utils/encrypted.js';
import nationalMap from '../../constant/china.json';
import {setFitView} from '@/utils/utils.js';
export default {
    name:'homeMap',
    mounted() {
        this.initGis();
        window.addEventListener('resize',  this.resizeGis);
        window.mouseenter = val =>{
            this.mouseenter(val);
        };
        window.mouseleave = val =>{
            this.mouseleave(val)
        };
    },
    methods:{
        initGis(){
            const g = new GIS({
                dom: $('#home-gis'),
                city:{
                    lng: 116.2529,
                    lat: 39.5420,
                    cityID: -1,
                    cityName: '北京',
                },
                initialZoom: 4.5,
                options: gisOptions,
            });
            g.gis.color = 0x081422;
            const serverPath = Decrypt(window.baseMapUrl).replace(/\{/g, '${');
            g.baseMapConfig.setBaseMapConfig({
                name: "底图图层",
                type: "gcj02",
                serverPath: serverPath,
                option: {
                    colorControl: true
                }
            });
            g.tileLayerList['高德底图'].colorChangeDate.light.setHex(0x081422);
            g.tileLayerList['高德底图'].colorChangeDate.dark.setHex(0x4875A5);
            // 视口范围变化监听
            // g.cameraControl.onMouseUp.addEvent((e) => {
            //     this.computedViewPoint(g);
            // });
            this.g = g;
            this.$emit('loaded', g);
            this.dealMask(g);
        },
        addRippleLayer(list){
            this.rippleList = list;
            const g = this.g;
            const that = this;
            this.removeClickEvent();
            g.layerList.divLayer.removeAll();
            list.forEach((item) => {
                let data = {
                    dom: `<div class="ripple">
                          <div class="dot wave2" style="z-index: 5;background:${item.color}"></div>
                          <div class="dot wave1 dot_left" style="z-index: 8;background:${item.color}"></div>
                          <div class="dot wave0 dot_left" style="z-index: 9;background:${item.color}"></div>
                          <div class="dot dot_left" style="z-index: 10; transform:scale(1);background:${item.color}"></div>
                      </div>`,
                    point: item.point,
                }
                data.autoScale = true;
                g.layerList.divLayer.addDiv(data);
            });
            this.divLayerEvent();
        },
        divLayerEvent(){
            const g = this.g;
            var ripple = document.getElementsByClassName('ripple');
            const that = this;
            for(let i = 0; i<ripple.length;i++){
                ripple[i].addEventListener('click', function() {
                    that.divLayer&&that.divLayer.remove();
                    const item = that.rippleList[i];
                    let data = {
                        dom: `<div class="tips" style="padding:.63rem;background:rgba(0,0,0,0.75);
                                box-shadow: 0px 9px 28px 8px rgba(0,0,0,0.05), 0px 6px 16px 0px rgba(0,0,0,0.08), 0px 3px 6px -4px rgba(0,0,0,0.12);
                                border-radius: 2px;
                                width:  fit-content;
                                white-space: nowrap;
                                position: relative;
                                transform: translate(-50%, -160%);
                            ">
                                <span style="color:#fff;font-size:14px;line-height:18px;">${item.name}</span></br>
                                <span style="color:#fff;font-size:14px;line-height:18px;">震级：${item.earthquakeLevel}</span>
                            </div>`,
                        point: item.point,
                    };
                    data.autoScale = true;
                    that.divLayer = g.layerList.divLayer.addDiv(data);
                });
            }
        },
        removeClickEvent(){
            var ripple = document.getElementsByClassName('ripple');
            for(let i = 0; i<ripple.length;i++){
                ripple[i].removeEventListener('click', function() {
                });
            }
        },
        computedViewPoint(gis){
            const range = gis.math.getViewPointsLatLng();
            this.gisViewPoint ={
                maxLat:range.rightTop.lat,
                maxLng:range.rightTop.lng,
                minLat:range.leftDown.lat,
                minLng:range.leftDown.lng,
                center:range.mid
            };
            console.warn('1111',this.gisViewPoint)
        },
        resizeGis(){
            this.g && this.g.gis.reSize();
        },
        dealMask(g){
            let chinaData  =  nationalMap;
            //将各自轮廓组数据提取组织成经纬度对象数组（{lat,lng}）
            //将轮廓点数量排序，方便找到需要的轮廓
            let datas = [];
            const mapData = chinaData.features;
            for(let j of mapData){
                const list = j.geometry.coordinates;
                for(const i of list){
                    let data = [];
                    i.forEach(item => {
                        item.forEach((val) => {
                            data.push({lat: val[1], lng: val[0]});
                        });
                    });
                    datas.push(data);
                }
            }
            datas.discard = true;
            g.tileLayerList['高德底图'].setMaskData(datas, 'rgba(0,0,0,0.6)');
            g.cameraControl.move({lat:36.1033742225742,lng:121.63839804687498});
            const outLine = mapData[0].geometry.coordinates.flat(1);
            this.renderOutline(outLine)
        },
        renderOutline(outline) {
            const g = this.g;
            if (!this.outlineLayer) {
                this.outlineLayer = new g.layer();
                g.gis.scene.add(this.outlineLayer);
                this.outlineLayer.visible = true;
            } else {
                this.outlineLayer.removeAll();
            }
            let  data = [];
            outline.forEach((item) => {
                const obj = {
                    ls: g.math.lsRegular(item),
                    ht: 0 * g.HM,
                    layers: [{ maxH: 0.02, color: 0x4C79AB }],
                };
                data.push(obj)
            })
            data.needFrame = true;
            //控制边框颜色
            data.frameWidth = 2;
            data.frameColor = 0x86BCED;
            //透明度是整体设置的,设置后所有的plane都使用这个透明度
            g.meshList.plane.opacity = 0;
            //传入数据创建平面
            let plane = g.meshList.plane.create(data);
            this.outlineLayer.renderOrder = true;
            this.outlineLayer.renderOrderIndex = 5;
            //图层添加模型
            this.outlineLayer.add(plane);
            //GIS更新
            g.gis.needUpdate = true;
        },
    }
}
</script>

<style lang="less" scoped>
.gis-map{
    width:100%;
    height:100%;
    #home-gis{
        width:100%;
        height:100%;
        position:relative;
        z-index:1;
        &::before {
            position: absolute;
            content: '';
            width: 100%;
            height: 100%;
            z-index: 2;
            background: radial-gradient( 25% 20% at 40% 45%, #78B8FF90 0%, rgba(5, 9, 10, 0) 100%);
            pointer-events: none;
        }
    }
}
.tip{
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
  color: #aaa;
  font-size: 12px;
  z-index: 999;
}
/deep/.pcGis .showInfo .checkedBox,/deep/.pcGis .showInfo .showBox{
    visibility:hidden;
}
@keyframes scale0 {
    from {
        transform: scale(0.2);
        opacity: 0.8;
    }
    to {
        transform: scale(1.6);
        opacity: 0.5;
    }
}
@keyframes scale1 {
    from {
        transform: scale(1.6);
        opacity: 0.5;
    }
    to {
        transform: scale(1.9);
        opacity: 0.3;
    }
}
@keyframes scale2 {
    from {
        transform: scale(1.9);
        opacity: 0.3;
    }
    to {
        transform: scale(2.2);
        opacity: 0;
    }
}
/deep/.ripple {
    // border-radius:50%;
    position: relative;
    top: -0.835rem;
    left:-0.47rem;
    .wave0 {
        animation: scale0 1s;
        -webkit-animation: scale0 1s;
        animation-iteration-count: infinite;
        -webkit-animation-iteration-count: infinite;
        animation-timing-function: linear;
        -webkit-animation-timing-function: linear; /* Safari and Chrome */
    }
    .wave1 {
        animation: scale1 1s;
        -webkit-animation: scale1 1s;
        animation-iteration-count: infinite;
        -webkit-animation-iteration-count: infinite;
        animation-timing-function: linear;
        -webkit-animation-timing-function: linear; /* Safari and Chrome */
    }
    .wave2 {
        animation: scale2 1s;
        -webkit-animation: scale2 1s;
        animation-iteration-count: infinite;
        -webkit-animation-iteration-count: infinite;
        animation-timing-function: linear;
        -webkit-animation-timing-function: linear; /* Safari and Chrome */
    }
    .dot {
        float: left;
        width: .94rem;
        height: .94rem;
        border-radius: 50%;
        position: absolute;
        opacity:0.7
    }
    .dot_left {
        margin-left: -50%;
    }
}
/deep/.tips::after{
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -10px;
    border-width: 10px;
    border-style: solid;
    border-color: rgba(0, 0, 0, 0.75) transparent transparent transparent;
    border-top-width: 10px;
    border-right-width: 10px;
    border-bottom-width: 10px;
    border-left-width: 10px;
}
</style>