<template>
    <div class="temp-task">
        <el-form :model="formData" label-width="136px" class="task-form">
            <el-form-item label="【任务名称】">
                <el-input v-model="formData.taskName" placeholder="请输入任务名称"></el-input>
            </el-form-item>

            <el-form-item label="【地震选择】">
                <el-input v-model="formData.earthquake" placeholder="请选择地震"></el-input>
            </el-form-item>

            <el-form-item label="【时间粒度】">
                <el-select style="width: 100%;" v-model="formData.timeGranularity" placeholder="请选择时间粒度">
                    <el-option label="小时" value="hour"></el-option>
                    <el-option label="半小时" value="halfHour"></el-option>
                    <el-option label="15分钟" value="quarter"></el-option>
                </el-select>
            </el-form-item>

            <el-form-item label="【时间选择】">
                <el-date-picker
                    class="task-form__time-picker"
                    popper-class="time-picker-popper"
                    v-model="formData.timeRange"
                    type="datetimerange"
                    range-separator="至"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    format="yyyy-MM-dd HH:mm"
                    value-format="yyyy-MM-dd HH:mm"
                >
                </el-date-picker>
            </el-form-item>

            <el-form-item label="【分析半径】">
                <div class="radius-input-container">
                    <el-input v-model.number="formData.radius" placeholder="请输入半径"> </el-input>
                    <div class="radius-controls">
                        <el-button
                            icon="el-icon-plus"
                            size="mini"
                            type="text"
                            @click="increaseRadius"
                        ></el-button>
                        <el-button
                            icon="el-icon-minus"
                            size="mini"
                            type="text"
                            @click="decreaseRadius"
                        ></el-button>
                    </div>
                </div>
            </el-form-item>

            <el-form-item label="【对象选择】">
                <el-checkbox-group v-model="formData.targetGroups">
                    <el-checkbox label="受影响人群"></el-checkbox>
                    <el-checkbox label="疑似失联人群"></el-checkbox>
                    <el-checkbox label="压埋人群"></el-checkbox>
                </el-checkbox-group>
            </el-form-item>

            <el-form-item label="【是否持续分析】">
                <el-select style="width: 100%;" v-model="formData.continuousAnalysis" placeholder="请选择">
                    <el-option label="是" value="yes"></el-option>
                    <el-option label="否" value="no"></el-option>
                </el-select>
            </el-form-item>
        </el-form>

        <div class="notes-container">
            <div class="notes">
                <p>注：</p>
                <p>1、时间粒度可选择天、小时、半小时、15分钟；</p>
                <p>2、分析时间可选择灾害发生前后三天内任意时间段；</p>
                <p>3、分析半径可输入(0, 1000000)米范围；</p>
                <p>4、分析对象可选择受影响人群、疑似失联人群、压埋人群；</p>
                <p>5、持续分析是否当分析时间范围超过系统时间，需创建持续分析任务，可选是。</p>
            </div>
            <!-- <el-button class="confirm-button" type="primary" @click="submitForm" size="mini"
                >确定</el-button
            > -->
            <div class="confirm-button" @click="submitForm">确定</div>
        </div>
        <new-earthquake :visible.sync="visible"></new-earthquake>
    </div>
</template>

<script>
import newEarthquake from './newEarthquake.vue';
export default {
    name: 'tempTask',
    components: {
        newEarthquake,
    },
    inheritAttrs: false,
    props: {
        outData: {
            type: Object,
            default: () => ({}),
        },
    },
    data() {
        return {
            visible: false,
            formData: {
                taskName: '云南大理州洱源县地震 分析任务',
                earthquake: '云南大理州洱源县地震',
                timeGranularity: '小时',
                timeRange: ['2025-06-08 10:00', '2025-06-09 10:00'],
                radius: 5000,
                targetGroups: [],
                continuousAnalysis: '否',
            },
        };
    },
    mounted() {
        // test loading
        setTimeout(() => {
            this.$emit('changeStatus', 'success');
        }, 2000);
    },
    methods: {
        initData() {
            this.formData = {
                ...this.formData,
                ...this.outData,
            };
        },
        increaseRadius() {
            this.formData.radius = parseInt(this.formData.radius) + 10 || 10;
        },
        decreaseRadius() {
            const currentRadius = parseInt(this.formData.radius) || 0;
            if (currentRadius > 1000) {
                this.formData.radius = currentRadius - 10;
            }
        },
        submitForm() {
            // Validate radius is within acceptable range
            const radius = parseInt(this.formData.radius);
            if (isNaN(radius) || radius <= 0 || radius >= 1000000) {
                this.$message.error('分析半径必须在(0, 1000000)米范围内');
                return;
            }

            this.$emit('submit', this.formData);
        },
    },
};
</script>

<style lang="less" scoped>
.temp-task {
  color: white;
  display: flex;
  flex-direction: column;
  gap: 10px;

    .task-form {
        border: 1px solid #75A2DD;
        padding: 0;

        /deep/ .el-form-item {
            margin-bottom: 0;
            border-bottom: 1px solid #75A2DD;
            padding: 0;
            display: flex;
            min-height: 50px;

            &:last-child {
                border-bottom: none;
            }
        }

        /deep/ .el-form-item__label {
            color: white;
            border-right: 1px solid #75A2DD;
            height: auto;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0;
        }

        /deep/ .el-form-item__content {
            margin-left: 12px !important;
            display: flex;
            align-items: center;
            min-width: 0;
            flex: 1;
        }

        /deep/ .el-input__inner,
        /deep/ .el-select .el-input__inner,
        /deep/ .el-date-editor {
            background-color: transparent;
            border: none;
            color: white;
        }

        /deep/ .el-checkbox__inner {
            background-color: transparent;
            border: 1px solid #75A2DD;
            color: white;
        }

        /deep/ .el-input-number__decrease,
        /deep/ .el-input-number__increase {
            background-color: transparent;
            border: none;
            color: white;
        }

        /deep/ .el-checkbox__label {
            color: white;
        }
        /deep/ .task-form__time-picker {
            padding: 0;
            transform: translateX(-6px);
            .el-range-input {
                background-color: transparent;
                color: white;
            }
            .el-range-separator {
                color: white;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            .el-input__icon.el-range__icon {
                display: none;
            }
        }
    }

    .radius-input-container {
        display: flex;
        align-items: center;
        width: 100%;

        .el-input {
            min-width: 120px;
            flex: 1;
            margin-right: 10px;
        }
    }

    .radius-controls {
        display: inline-flex;
        align-items: center;
        margin-right: 10px;

        .el-button {
            margin: 0 3px;
            color: white;

            &:hover {
                color: #409eff;
            }
        }
    }

    .notes-container {
        margin-top: 20px;
        position: relative;

        .notes {
            color: #8c8c8c;
            font-size: 12px;
            padding-right: 48px;

            p {
                margin: 5px 0;
            }
        }

        .confirm-button {
            position: absolute;
            top: 0;
            right: 0;
            user-select: none;
            cursor: pointer;
            width: 88px;
            height: 28px;
            background: rgba(56, 113, 179, 0.7);
            border-radius: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 400;
            font-size: 15px;
            color: #ffffff;
        }
    }
}
</style>
<style lang="less">
.time-picker-popper {
    background: #040A1E;
    box-shadow: inset 0px 0px 8px 0px #4984FF;
    border-radius: 2px;
    border: 1px solid #A8BFE8;
    color:#fff;
    .el-button {
        &:not(.el-button--text) {
            background-color: rgba(56, 113, 179, 0.7);
            color: #fff;
        }
        &--text {
            display: none !important;
        }
        &.is-disabled.is-plain {
            background-color: transparent;
        }
        &:hover {
            background-color: rgba(56, 113, 179, 1);
        }
    }
    .el-input__inner {
        background-color: transparent !important;
        border: 1px solid #A8BFE8 !important;
        color: white !important;
        &::placeholder {
            color: rgba(255, 255, 255, 0.45);
        }
        .el-range-separator {
            color: white !important;
        }
    }
    .el-picker-panel {
        color: #fff;
        &__shortcut {
            color: white;
            &:hover {
                color: #4984FF;
            }
        }
        &__icon-btn {
            color: white;
        }
        &__sidebar {
            background-color: transparent;
            border-right: 1px solid #A8BFE8;
        }
        &__footer {
            background-color: transparent;
            border-top: 1px solid #A8BFE8;
        }
    }
    .el-date-range-picker {
        &__time-header {
            border-bottom: 1px solid #A8BFE8;
        }
        &__content.is-left {
            border-right: 1px solid #A8BFE8;
        }
    }
    .el-date-table {
        th {
            border-bottom: 1px solid #A8BFE8;
        }
        td {
            &.in-range div {
                background: rgba(56, 113, 179, 0.7);
                &:hover {
                    background: rgba(56, 113, 179, 1);
                }
            }
            &.disabled div {
                background-color: rgba(56, 113, 179, 1);
                opacity: 0.5;
            }
        }
    }
    .el-time-panel {
        background: #040A1E !important;
        box-shadow: inset 0px 0px 8px 0px #4984FF;
        border-radius: 2px;
        border: 1px solid #A8BFE8;
        &[x-placement^='top'] .popper__arrow {
            border-top-color: #ebeef522 !important;
            &::after {
                border-top-color: #4984FF !important;
            }
        }
        &[x-placement^='bottom'] .popper__arrow {
            border-bottom-color: #ebeef522 !important;
            &::after {
                border-bottom-color: #4984FF !important;
            }
        }
        &[x-placement^='left'] .popper__arrow {
            border-left-color: #ebeef522 !important;
            &::after {
                border-left-color: #4984FF !important;
            }
        }
        &[x-placement^='right'] .popper__arrow {
            border-right-color: #ebeef522 !important;
            &::after {
                border-right-color: #4984FF !important;
            }
        }
        &__content {
            &::before {
                border-top: 1px solid #A8BFE8;
                border-bottom: 1px solid #A8BFE8;
            }
            &::after {
                border-top: 1px solid #A8BFE8;
                border-bottom: 1px solid #A8BFE8;
            }
        }
        &__footer {
            border-top: 1px solid #A8BFE8;
        }
        &__btn {
            color: white;
        }
        .el-time-spinner__item {
            color: white;
            &.active {
                color: #4984FF;
            }
            &:hover {
                background-color: transparent !important;
            }
        }
        .el-scrollbar__wrap {
            overflow-x: hidden; //将横向滚动条隐藏
        }
    }
    .el-date-picker__header-label {
        color: #fff;
    }
    .el-year-table td .cell {
        color: #fff;
    }
    .el-month-table td .cell {
        color: #fff;
    }
    .el-date-table th {
        color: #999;
        text-align: center;
    }
    .time-select-item {
        &.selected:not(.disabled) {
            color: #4984FF !important;
        }
        &:hover {
            background: #A8BFE8 !important;
            font-weight: normal !important;
        }
    }
    // 暗色时滚动条调成透明
    .el-scrollbar__wrap {
        &::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }
        &::-webkit-scrollbar-track {
            box-shadow: inset 0 0 5px transparent;
            border-radius: 10px;
            background: transparent;
        }
    }
}
</style>
