<template>
    <div class="card1">
        <img class="card1-title" :src="require('../../../img/analyzeDetails/analysisOverview.png')" />
        <span class="card1-text">{{title}}</span>
        <div class="card1-content">
            <slot></slot>
        </div>
    </div>
</template>

<script>
export default {
    name:'card1',
    props:{
        title:{
            type:String,
            default:''
        }
    }
}
</script>

<style lang="less" scoped>
.card1{
    background: transparent;
    padding:0 6px;
    position:relative;
    &-title{
        width:100%;
        height:22px;
    }
    &-text{
        position:absolute;
        left: 30px;
        top: 0px;
        color: #fff;
        font-size: 15px;
        font-weight: 600;
        text-shadow: 0px 0px 0.22222rem rgba(62, 136, 233, 0.64);
    }
    &-content{
        width:100%;
        padding: 5px 0px;
    }
}
</style>