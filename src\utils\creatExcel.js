const ExcelJS = require('exceljs');
export const exportExcel = (workbook,name) => {
    // 导出表格
    workbook.xlsx.writeBuffer().then((buffer) => {
        const blob = new Blob([buffer], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        })
        const link = document.createElement('a')
        link.href = URL.createObjectURL(blob)
        link.download = name + '.xlsx'
        link.click()
        URL.revokeObjectURL(link.href) // 下载完成释放掉blob对象
    })
};

export const mergeCells = (worksheet,cells) => {
    for(let i = 0;i< cells.length;i++){
        const data = cells[i];
        // 添加合并单元格
        worksheet.mergeCells(data);
    }
};

export const addRow  = (worksheet,rowData) =>{
    for(let i = 0;i< rowData.length;i++){
        const data = rowData[i];
        // 添加合并单元格
        worksheet.addRow(data);
    }
}

export const createExcel = (name,options,view = [])=>{
    // 创建工作簿
    const workbook = new ExcelJS.Workbook();
    workbook.view = view;//配置表格冻结列行等操作
    for(let i = 0;i< options.length;i++){
        const data = options[i];
        // 添加工作表
        const worksheet = workbook.addWorksheet(data.name);
        // 初始合并单元格
        if(data.mergeCells){
            mergeCells(worksheet,data.mergeCells);
        }
        if(data.columns){
            worksheet.columns = data.columns;
        }
        if(data.setCell){
            const cells = data.setCell;
            // 设置单元格数据或样式
            Object.keys(cells).forEach((item) => {
                const cell = worksheet.getCell(item);
                Object.assign(cell,cells[item]);
            })
        }
        if(data.addRow){
            addRow(worksheet,data.addRow);
        }
    }

    // 导出
    exportExcel(workbook,name);
}