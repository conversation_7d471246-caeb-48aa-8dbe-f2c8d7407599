import {getExternalSquare,setFitView} from '@/utils/utils.js';
export default (g, options = {}) => {
    class circular{
        static removeAll(){
            g.layerList.圈选.removeAll();
        }
        static createCircular(option,hasCenterData){
            circular.removeAll();
            g.layerList.圈选.create(option);
            circular.toMoveCenter(option.startPoint,option.radius);
            hasCenterData && circular.addCenter(hasCenterData);
        }
        static toMoveCenter(startPoint,analysisRadius){
            const points = getExternalSquare(startPoint, Number(analysisRadius));
            setFitView(points,g);
        }
        static addCenter(data){
            g.layerList.divLayer.removeAll();
            data.autoScale = true;
            g.layerList.divLayer.addDiv(data);
        }
    }
    return circular;
};