export default {
  props: {
    outsideRow: {
      type: Object,
      default: () => ({}),
    },
    baseInfo: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      curRow: {},
      isArea: null,
      cellBoundary: null,
      tableData: [],
      allTableData: [],
      total: 0,
    };
  },
  computed: {
    userInfo() {
      return {
        id: 1,
        name: 'admin',
        describe: '系统管理员',
      };
    },
  },
  watch: {
    isArea() {
      this.setTableData();
    },
    cellBoundary() {
      this.setTableData();
    },
  },
  created() {
    this.search();
  },
  deactivated() {
    this.curRow = {};
  },
  methods: {
    activeRowClassName({ row }) {
      if (row === this.curRow) {
        return 'success-row';
      }
      return '';
    },
    renderHeader(h, { column, menu }) {
      const prop = column.property;
      return h(
        'el-select',
        {
          class: 'custom-select', // 添加的class类名
          props: {
            value: this[prop], // 这里是你的变量 this[prop]
            size: 'mini', // 设置el-select的size属性为mini
          },
          on: {
            change: (command) => {
              this[prop] = command; // 更新你的变量
            },
          },
        },
        menu.map((it) =>
          h(
            'el-option',
            { props: it, key: it.value },
            it.label
          )
        )
      );
    },
    getBoundary(cellBoundary) {
      if (Number.isFinite(cellBoundary)) {
        return (cellBoundary / 1000).toFixed(2);
      }
      return '-';
    },
    setTableData(paginationData = {}) {
      // 条件过滤
      const allTableData = this.allTableData.filter((item) => {
        const isArea = this.isArea ? item.isArea === this.isArea : true;
        const cellBoundary = this.cellBoundary ? Number(this.getBoundary(item.cellBoundary)) : true;
        let isCellBoundary;
        if (!this.cellBoundary) {
          isCellBoundary = true;
        } else if (this.cellBoundary === 10) {
          isCellBoundary = cellBoundary > this.cellBoundary;
        } else {
          isCellBoundary = cellBoundary < this.cellBoundary;
        }
        return isArea && isCellBoundary;
      });
      // 设置分页
      this.total = allTableData.length;
      const { curPage = 1, pageSize = 10 } = paginationData;
      let startIndex = Math.max(0, (curPage - 1) * pageSize);
      let endIndex = Math.min(this.total, curPage * pageSize);
      this.tableData = allTableData.slice(startIndex, endIndex);
    },
  },
};
