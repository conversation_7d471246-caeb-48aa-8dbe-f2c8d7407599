@font-face {
    font-family: 'iconfont'; /* Project id 4964108 */
    src: url('iconfont.woff2?t=1751445469910') format('woff2'),
        url('iconfont.woff?t=1751445469910') format('woff'),
        url('iconfont.ttf?t=1751445469910') format('truetype'),
        url('iconfont.svg?t=1751445469910#iconfont') format('svg');
}

.iconfont,
[class^='copilot-icon-'],
[class*='copilot-icon-'] {
    font-family: 'iconfont' !important;
    font-size: 16px;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.copilot-icon-frameReduce:before {
    content: "\e618";
    color: var(--baseBorderColor);
    font-size:17px;
}

.copilot-icon-adsorption:before {
    content: "\e617";
    color: var(--baseBorderColor);
    font-size:17px;
}

.copilot-icon-smallScreen:before {
    content: "\e616";
    color: var(--baseBorderColor);
    font-size:17px;
}

.copilot-icon-slideDownToTheBottom:before {
    content: '\e615';
    color: var(--baseBorderColor);
}

.copilot-icon-stopRecording:before {
    content: '\e614';
    font-size: 36px;
    color: var(--baseBorderColor);
}

.copilot-icon-mute:before {
    content: '\e610';
    font-size: 36px;
    color: var(--baseBorderColor);
}

.copilot-icon-setting:before {
    content: '\e60d';
    color: var(--baseBorderColor);
    font-size:17px;
}

.copilot-icon-refresh:before {
    content: '\e60c';
    color: var(--baseBorderColor);
    font-size:16px;

}

.copilot-icon-download:before {
    content: '\e60b';
    color: var(--baseBorderColor);
    font-size:16px;
}

.copilot-icon-copy:before {
    content: '\e60a';
    color: var(--baseBorderColor);
    font-size:16px;
}

.copilot-icon-voice:before {
    content: '\e60f';
    font-size: 36px;
    color: var(--baseBorderColor);
}

.copilot-icon-fullScreen:before {
    content: '\e60e';
    color: var(--baseBorderColor);
    font-size:17px;
}

.copilot-icon-volume:before {
    content: '\e609';
    position: absolute;
    margin-top: 8px;
    font-size: 28px;
    color: var(--baseBorderColor);
}

.copilot-icon-stop:before {
    content: '\e608';
    font-size: 36px;
    color: var(--baseBorderColor);
}

.copilot-icon-send:before {
    content: '\e607';
    font-size: 36px;
    color: var(--baseBorderColor);
}
