import Vue from 'vue';
class TalkRecord {
    constructor(role) {
        this.role = role;
        this.text = '';
        this.textStatus = 'loading';
        this.component = null;
        this.status = 'loading';
        this.data = {};
    }
    static createTalkRecord(role, text) {
        const talkRecord = new TalkRecord(role, text);
        return Vue.observable(talkRecord);
    }
    setText(text) {
        this.text = text;
    }
    setTextStatus(textStatus) {
        this.textStatus = textStatus;
    }
    setStatus(status) {
        this.status = status;
    }
    setData(data) {
        this.data = data;
    }
    setComponent(component, data, event) {
        const comp = {
            name: component,
            bind: {
                params: data,
            },
            on: event,
        };
        return (this.component = comp);
    }
}
export default TalkRecord;
