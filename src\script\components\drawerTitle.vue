<!-- 抽屉弹框标题 -->
<template functional>
  <div class="drawer-title">
    <img class="img" :src="props.titleInfo.icon" alt="" />
    <span class="text">{{ props.titleInfo.title }}</span>
    <slot name="btn"></slot>
    <span v-if="props.isShowId" class="id">ID：{{ props.titleInfo.id }}</span>
  </div>
</template>
<script>
export default {
  name: 'drawer-title',
  props: {
    titleInfo: {
      type: Object,
      default: () => ({}),
    },
    isShowId: {
      type: Boolean,
      default: false,
    },
  },
};
</script>
<style lang="less" scoped>
.drawer-title {
  position: relative;
  display: flex;
  align-items: center;
  padding-left: 48px;
  height: 48px;
  font-weight: bold;
  border-radius: 6px 6px 0px 0px;
  box-shadow: inset 0px -1px 0px 0px rgba(0, 0, 0, 0.07);
  background: linear-gradient(290deg, rgba(235, 246, 255, 0.67) 0%, #d4ebff 100%);
  .img {
    position: absolute;
    left: 0;
    top: 0;
  }
  .text {
    margin-right: 10px;
    font-size: 16px;
    color: rgba(0, 0, 0, 0.85);
  }
  .id {
    height: 20px;
    font-size: 14px;
    font-weight: 500;
    color: #0091ff;
  }
}
</style>
