// func (F): 要进行防抖处理的函数
// debounceMs (number): 延迟执行的毫秒数
// options (DebounceOptions, 可选): 一个选项对象
function debounce(func, debounceMs) {
    let timeoutId = null;

    const debounced = function(...args) {
        if (timeoutId !== null) {
            clearTimeout(timeoutId);
        }

        timeoutId = setTimeout(() => {
            func.bind(this, ...args);
            timeoutId = null;
        }, debounceMs);
    };
    return debounced;
}
const throttle = function(func, throttleMs) {
    let lastExecTime = 0;
    return function(...args) {
        const currentTime = Date.now();
        if (currentTime - lastExecTime >= throttleMs) {
            func.apply(this, args);
            lastExecTime = currentTime;
        }
    };
};
// 获取坐标群里的各个方向的极值点
const getCoordinateExtremum = (coordinates = []) => {
    if (coordinates.length <= 4) {
        return coordinates;
    }
    let minLat, minLng, maxLat, maxLng;
    for (const item of coordinates) {
        if (!minLat || item.lat < minLat.lat) {
            minLat = item;
        }
        if (!maxLat || item.lat > maxLat.lat) {
            maxLat = item;
        }
        if (!minLng || item.lng < minLng.lng) {
            minLng = item;
        }
        if (!maxLng || item.lng > maxLng.lng) {
            maxLng = item;
        }
    }
    return [minLat, maxLat, minLng, maxLng];
};
// 计算中心点
const calculateCenterPoint = (minLng, maxLng, minLat, maxLat) => {
    // 将经纬度转换为弧度
    const dLon = ((maxLng.lng - minLng.lng) * Math.PI) / 180;
    const minLatRad = (minLat.lat * Math.PI) / 180;
    const maxLatRad = (maxLat.lat * Math.PI) / 180;
    const minLngRad = (minLng.lng * Math.PI) / 180;

    const Bx = Math.cos(maxLatRad) * Math.cos(dLon);
    const By = Math.cos(maxLatRad) * Math.sin(dLon);
    const lat = Math.atan2(
        Math.sin(minLatRad) + Math.sin(maxLatRad),
        Math.sqrt(
            (Math.cos(minLatRad) + Bx) * (Math.cos(minLatRad) + Bx) + By * By
        )
    );
    const lng = minLngRad + Math.atan2(By, Math.cos(minLatRad) + Bx);

    // 将弧度转回度数
    return {
        lat: (lat * 180) / Math.PI,
        lng: (lng * 180) / Math.PI
    };
};
// 根据中心坐标获取近似中心点
const getNearestPoint = (points, centerPoint, cb) => {
    const obj = {};
    let minLen = 0;
    for (const point of points) {
        const curDistance = cb
            ? cb(point, centerPoint)
            : Math.abs(point.lat - centerPoint.lat) +
              Math.abs(point.lng - centerPoint.lng);
        if (!minLen || curDistance < minLen) {
            minLen = curDistance;
        }
        obj[curDistance] = point;
    }
    console.log("obj", obj);
    if (minLen) {
        return obj[minLen];
    }
};
// 栅格需求分数
const getColorByScore = score => {
    score = Number(score);
    if (score <= 25) {
        return 0x47b752;
    } else if (score <= 50) {
        return 0xfed152;
    } else if (score <= 75) {
        return 0xed7d00;
    }
    return 0xf34235;
};

const setFitView = (data,g,size)  =>{
    let GIS = g;
    //得到最大最小经纬度
    let points = data;
    //计时2s后执行
    const timer = setTimeout(() => {
        //根据最大最小值计算GIS渲染的半径以及中心点；
        let easy = GIS.cameraControl.computeZoomByPoints(points, size ||1);

        //返回的数据中已经存在radius（半径）以及target（中心点），因为缩放需要的字段名称是targetPoint，这里赋值一下；
        easy.targetPoint = easy.target;

        //根据半径中心点进行缩放（可能是放大，可能是缩小，可用于制作钻取效果）
        GIS.cameraControl.gradualChangeRadius(easy);

        clearTimeout(timer);
    }, 1000);
};

const getExternalSquare = (centerPoint, radius) => {
    const LAT_PIECE = 0.00001;
    const LNG_PIECE = 0.000009;
    const { lat, lng } = centerPoint;
    return [
        { lat: lat + radius * LAT_PIECE, lng: lng - radius * LNG_PIECE },
        { lat: lat + radius * LAT_PIECE, lng: lng + radius * LNG_PIECE },
        { lat: lat - radius * LAT_PIECE, lng: lng + radius * LNG_PIECE },
        { lat: lat - radius * LAT_PIECE, lng: lng - radius * LNG_PIECE },
    ];
};
export function getUrlKey(name) {
    return (
      decodeURIComponent(
        (new RegExp("[?|&]" + name + "=" + "([^&;]+?)(&|#|;|$)").exec(
          location.href
        ) || [, ""])[1].replace(/\+/g, "%20")
      ) || null
    )
};
export const fontSizeRem  = (size) =>{
    const clientWidth = window.innerWidth || document.documentElement.clientWidth ||
    document.body.clientWidth;
    if (!clientWidth) return;
    let fontSize = clientWidth / 1920;//尺寸大小
    return size* fontSize;
}


export {
    debounce,
    throttle,
    getCoordinateExtremum,
    getNearestPoint,
    getColorByScore,
    calculateCenterPoint,
    setFitView,
    getExternalSquare,
};
