<template>
    <div class="com-title">
        <img class="title-img" :src="img" />
        <span class="title-text">{{title}}</span>
        <div class="title-right"> <slot name="right"></slot></div>
    </div>
</template>
<script>
export default {
    name: 'titleBar',
    props: {
        img:{
            type:String,
            default:'keyAreas'
        },
        title:{
            type:String,
            default:''
        }
    },
    data() {
        return {};
    },
    methods: {},
};
</script>
<style lang="less" scoped>
.com-title {
    width:100%;
    height:42px;
    position:relative;
    z-index: 5;
    .title-img{
        width:100%;
        height:100%;
    }
    .title-text{
        position:absolute;
        left: 40px;
        top: 8px;
        color: #fff;
        font-size: 16px;
        font-weight: 600;
        text-shadow: 0px 0px 0.22222rem rgba(62, 136, 233, 0.64);
    }

    .title-right{
        position:absolute;
        right: 16px;
        top: 8px;
    }
}
</style>
