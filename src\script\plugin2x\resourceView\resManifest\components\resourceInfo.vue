<template>
    <div class="resource-list">
        <div class="resource-list__table">
            <el-table
                :key="bulkType"
                ref="resourceRef"
                :data="tableData"
                style="width: 100%"
                :header-cell-style="headerStyle"
                :row-key="getRowKey"
                :row-class-name="activeRowClassName"
                :header-cell-class-name="headerClassName"
                size="small"
                @selection-change="handleSelectChange"
                @expand-change="handleExpand"
            >
                <el-table-column
                    v-if="!isResPackage && bulkType && !isBulkCheck"
                    type="selection"
                    class-name="custom-cell"
                    width="30"
                    reserve-selection
                />
                <el-table-column
                    :prop="resourceName"
                    label="资源名称"
                    :class-name="`${'custom-cell'} hide-expandIcon`"
                >
                    <template slot="header">
                        <el-select
                            v-if="!isResPackage"
                            v-model="bulkType"
                            class="custom-select"
                            size="mini"
                        >
                            <el-option v-for="it in bulkOptions" :key="it.value" v-bind="it" />
                        </el-select>
                        <span v-else>资源名称</span>
                    </template>
                    <template slot-scope="{ row }">
                        <div class="regionName">
                            <div :class="`icon ${isBulkCheck ? 'expand-item' : ''}`" style="width: auto;">
                                <el-checkbox
                                    v-if="isBulkCheck"
                                    class="icon mb-0"
                                    v-model="row.isChecked"
                                    @change="(status) => handleChecked(status, row)"
                                ></el-checkbox>
                                <!-- <img :src="getIcon(row.type)" @click="handleRowClk(row)" /> -->
                            </div>
                            <div class="content" @click="handleRowClk(row)">
                                <span class="name" :title="row[resourceName]">
                                    <span class="text">{{ row[resourceName] }}</span>
                                    <img
                                        v-if="row.isImport"
                                        class="areaIcon"
                                        src="@/img/space/icons/focusTag.png"
                                    />
                                </span>
                                <span class="type">{{ row.layerNames }}</span>
                            </div>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column
                    v-if="!bulkType"
                    label="操作"
                    prop="tool"
                    :width="
                        isPublicRes ? 70 : 100
                    "
                    :class-name="`${isResPackage ? '' : 'custom-cell'}`"
                    v-slot="{ row }"
                >
                    <el-button v-if="!isPublicRes && row.type === 1" class="green" type="text" size="mini">
                        <span @click.stop="edit(row)">编辑</span>
                    </el-button>
                    <el-button type="text" size="mini" class="green">
                        <span @click.stop="createTask(row)">创建</span>
                    </el-button>
                </el-table-column>
            </el-table>
            <el-pagination
                v-if="isShowPagination"
                class="pagination"
                :current-page="paginationData.pageIndex"
                :page-size="paginationData.rowsPage"
                :page-sizes="[15, 20, 30, 50]"
                :total="paginationData.total"
                :pager-count="3"
                layout="prev, pager, next, sizes, total"
                background
                small
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>

        <div v-if="bulkType" class="resource-list__tools">
            <el-button
                class="earth-btn-purple"
                icon="el-icon-back"
                size="mini"
                @click="bulkType = ''"
            >
                返回
            </el-button>
            <el-button
                class="earth-btn-dark"
                v-if="!isBulkCheck"
                :type="operateType"
                size="mini"
                @click="handleOperator"
            >
                {{ bulkType }}
            </el-button>
        </div>
        <!-- 批量编辑弹框 -->
        <bulkEdit
            v-if="visible"
            :visible.sync="visible"
            :selectList="selectList"
            @updateTable="updateTable"
        />
    </div>
</template>
<script>
import posImg from '@/img/space/icons/pos.png';
import regionGroupImg from '@/img/space/icons/regionGroup.png';
import packageImg from '@/img/space/icons/package.png';
import plusImg from '@/img/space/icons/plus.png';
import goodReviewActive from '@/img/space/icons/goodReviewActive.png';
import goodReview from '@/img/space/icons/goodReview.png';
import badReviewActive from '@/img/space/icons/badReviewActive.png';
import badReview from '@/img/space/icons/badReview.png';
import blurAreaIcon from '@/img/space/icons/blurArea.png';
import focusAreaIcon from '@/img/space/icons/focusArea.png';
import excel from '@/utils/excel.js';
import { excelWidths, mapRequest, bulkOpts } from '@/script/constant/resourceManifest.js';
import { mapFields } from '@/script/constant/common.js';
export default {
    name: 'resource-list',
    components: {
        bulkEdit: () => import('./bulkEdit.vue'),
    },
    props: {
        tableData: {
            type: Array,
            default: () => [],
        },
        paginationData: {
            type: Object,
        },
        isResPackage: {
            type: Boolean,
            default: false,
        },
        isPublicRes: {
            type: Boolean,
            default: false,
        },
        isRegionGroup: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            curRow: null,
            selectList: [],
            headerStyle: {
                background: '#1B293B',
                color: '#9AAFC1',
                fontWeight: 'bold',
                boxShadow: 'inset 0px -1px 0px 0px rgba(0,0,0,0.08)',
                borderRadius: '2px 2px 0px 0px',
            },
            cellStyle: {
                lineHeight: '20px',
            },
            mapResourceIcon: {
                1: posImg,
                2: regionGroupImg,
            },
            exportLoading: false,
            curEvaluateType: 'good',
            plusImg,
            goodReview,
            goodReviewActive,
            badReview,
            badReviewActive,
            expandedRow: null,
            bulkType: '',
            visible: '',
        };
    },
    computed: {
        isShowPagination() {
            return this.paginationData.total > 15;
        },
        resourceName() {
            return this.isResPackage ? 'resourcePackageName' : 'resourceName';
        },
        userInfo() {
            return {
                id: 1,
                name: 'admin',
                describe: '系统管理员',
            };
        },
        expandClassName() {
            if (this.isPublicRes) {
                return 'custom-cell custom-hide';
            } else if (this.isResPackage) {
                return '';
            }
            return 'custom-cell';
        },
        bulkOptions() {
            if (this.isPublicRes) {
                return bulkOpts.filter((item) => !['批量删除', '批量编辑'].includes(item.value));
            } else if (this.isRegionGroup) {
                return bulkOpts.filter((item) => !['批量查看', '批量编辑'].includes(item.value));
            }
            return bulkOpts;
        },
        isBulkCheck() {
            return this.bulkType === '批量查看';
        },
        operateType() {
            if (this.bulkType === '批量删除') {
                return 'danger';
            }
            return 'primary';
        },
        isPermission() {
            return ['位置运营', '位置监控'].includes(window.sourceSystemName);
        },
    },
    watch: {
        bulkType(newVal) {
            this.$emit('updateBulkType', newVal);
            this.curRow = {};
            this.selectList = [];
        },
        isRegionGroup() {
            this.bulkType = '';
        },
    },
    methods: {
        handleRowClk(row) {
            if (this.bulkType && !this.isBulkCheck) return;
            this.curRow = row;
            this.$emit('clickRow', row);
        },
        handleSelectChange(selection) {
            this.selectList = selection;
        },
        async bulkDel() {
            if (!this.selectList.length) {
                this.$message.warning('请选择要删除的资源');
                return;
            }
            const { id, name } = this.userInfo;
            const type = this.selectList[0].type;
            const { regionKey, regionVal, method } = mapRequest[type] || {};
            const regionList = this.selectList.map((item) => item[regionVal]);
            const res = await this.$post(
                'queryIsSubscribed',
                {
                    userId: id,
                    userName: name,
                    resourceList: regionList,
                    resourceType: 3,
                },
                true
            );
            let validTip;
            if (res.data) {
                validTip = '资源已被订阅';
            }
            const tip = validTip
                ? `${validTip}，无法删除，需撤销所有使用任务才可删除`
                : '';
            this.confirm(tip, '确认删除')
                .then(() => {
                    if (validTip) return;
                    this.$post(method, {
                        userId: id,
                        userName: name,
                        [regionKey]: regionList,
                    }).then((res) => {
                        console.log(res);
                        this.$message.success('删除成功');
                        this.$emit('updateTable', { pageIndex: 1 });
                        this.selectList = [];
                    });
                })
                .catch((err) => {
                    console.log(err);
                });
        },
        async exportData() {
            this.exportLoading = true;

            let list = [];
            if (this.selectList.length) {
                list = this.selectList;
            } else {
                list = this.tableData;
            }

            const excelData = list.map((item) => {
                let regionCoors;
                const isPolygon = item.shapeType === 2;
                const isLine = item.shapeType === 4;
                if (item.shapeType === 5) {
                    regionCoors = item.regionCoors;
                } else if ((item.multiPolygonList || []).length) {
                    regionCoors = item.multiPolygonList
                        .map((it) => {
                            return isPolygon
                                ? it.polygon
                                : `${it.centerX},${it.centerY};${it.radius}`;
                        })
                        .join('|');
                } else {
                    const { centerLatitude, centerLongitude, radius } = item.circle;
                    regionCoors =
                        isPolygon || isLine
                            ? item.regionCoors
                            : `${centerLongitude},${centerLatitude};${radius}`;
                }
                return {
                    资源ID: item.resourceId,
                    资源名称: item.resourceName,
                    轮廓类型: mapFields.shapeType[item.shapeType],
                    图层类型: item.layerNames,
                    地域类型: item.regionTypeNames,
                    开放状态: mapFields.layerStatus[item.layerStatus],
                    省: item.provinceName,
                    市: item.cityName,
                    区: item.districtName,
                    是否起效: mapFields.isValid[item.isValid],
                    状态: mapFields.layerStatus[item.layerStatus],
                    区域外扩算法: mapFields.shapeMethod[item.shapeMethod],
                    资源区域轮廓经纬度: regionCoors,
                    空洞经纬度: (item.hollowPolygonList || []).map((it) => it.polygon).join('|'),
                    区域描述: item.describe,
                    创建时间: (item.createTime || '').substr(0, 19),
                    更新时间: (item.lastUpdateTime || '').substr(0, 19),
                };
            });
            const wsConfig = [
                {
                    key: '!cols',
                    value: excelWidths,
                },
            ];
            excel.exportExcel(excelData, '资源清单列表', wsConfig);
            this.$refs.resourceRef.clearSelection();
            this.exportLoading = false;
        },
        bulkEdit() {
            this.visible = true;
        },
        handleOperator() {
            switch (this.bulkType) {
                case '批量导出':
                    this.exportData();
                    break;
                case '批量编辑':
                    this.bulkEdit();
                    break;
                case '批量删除':
                    this.bulkDel();
                    break;
            }
        },
        handleSizeChange(rowsPage) {
            this.$emit('updateTable', {
                rowsPage,
                pageIndex: 1,
            });
        },
        handleCurrentChange(pageIndex) {
            this.$emit('updateTable', { pageIndex });
        },
        activeRowClassName({ row }) {
            if (row === this.curRow) {
                return 'success-row';
            }
            return '';
        },
        async handleExpand(row, expand) {
            if (!expand.length || this.isPublicRes) return;
            if (this.expandedRow && this.expandedRow !== row) {
                this.$refs.resourceRef.toggleRowExpansion(this.expandedRow, false);
            }

            this.expandedRow = row;

            if ((row.children || []).length) return;

            const { id, name } = this.userInfo;
            const res = await this.$post('queryResPackageDetailList', {
                userId: id,
                userName: name,
                resourcePackageId: row.resourcePackageId,
                pageNum: 1,
                pageSize: null,
            });
            row.children = res.regionList.map((item) => ({
                ...item,
                type: item.regionType,
                parentId: row.resourcePackageId,
                isChecked: false,
            }));
        },
        handleChecked(status, row) {
            if (status) {
                this.curRow = row;
            }
            this.$emit('toggleChecked', status, row);
        },
        getIcon(resType) {
            if (this.isResPackage) {
                return packageImg;
            }
            return this.mapResourceIcon[resType];
        },
        edit(row, cb) {
            const tip = cb ? '创建' : '编辑';
            this.confirm('', `确认${tip}？`)
                .then(async () => {
                    const { id, name } = this.userInfo;
                    const res = await this.$post('queryOneRegion', {
                        userId: id,
                        userName: name,
                        regionId: row.resourceId,
                    });
                    if (cb && typeof cb === 'function') {
                        return cb(res);
                    }
                    this.$emit('setCurComp', 'resourceEdit', {
                        baseInfo: {
                            ...res,
                            ...res.attributes,
                            type: 1, // 单个资源
                            resourceName: res.regionName,
                            createTime: (res.createTime || '').substr(0, 19),
                            lastUpdateTime: (res.lastUpdateTime || '').substr(0, 19),
                        },
                        curRow: row,
                        isShowBaseStation: !row.isSub,
                    });
                })
                .catch(() => {});
        },
        createTask(row) {
            this.edit(row, (res) => {
                this.$router.push({
                    name: 'presetCreation',
                    query: {
                        row: JSON.stringify(res || {}),
                    },
                });
            });
        },
        getRowKey(row) {
            if (row.evaluateType) {
                return `${row.resourceId}-${row.evaluateType}`;
            } else if (row.resourceId) {
                return `${row.resourceId}-single`;
            } else if (row.resourcePackageId) {
                return `${row.resourcePackageId}-resPackage`;
            }
            return `${row.regionId}-resPackage-detail`;
        },
        handleGoodReview(row) {
            if (row.good) {
                this.curEvaluateType = 'good';
            }
            this.$refs.resourceRef.toggleRowExpansion(row, row.good);
        },
        handleBadReview(row) {
            if (row.bad) {
                this.curEvaluateType = 'bad';
            }
            this.$refs.resourceRef.toggleRowExpansion(row, row.bad);
        },
        handleClose(row) {
            if (this.curEvaluateType) {
                row[this.curEvaluateType] = false;
            }
            this.$refs.resourceRef.toggleRowExpansion(row, false);
        },
        handleSubmit(row, evaluateInfo) {
            const { id, name } = this.userInfo;
            const { checkTypes, evaluation, isShowComment } = evaluateInfo;
            this.$post(
                'feedback',
                {
                    userId: id,
                    userName: name,
                    regionId: row.resourceId,
                    evaluateType: this.curEvaluateType === 'good' ? 1 : 2,
                    feedbackType: checkTypes,
                    feedbackDescribe: isShowComment ? evaluation : undefined,
                },
                true
            ).then((res) => {
                this.$message.success(res.returnMsg);
                this.$emit('updateTable', this.paginationData);
            });
            this.$refs.resourceRef.toggleRowExpansion(row, false);
        },
        setDisabled(row, type) {
            if (type === 'good') {
                return row.evaluateType === 1;
            }
            return row.evaluateType === 2;
        },
        confirm(explain, tip, className = '') {
            return this.$confirm(explain, tip, {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
                center: true,
                closeOnClickModal: false,
                customClass: `custom-confirm-dialog ${className}`,
            });
        },
        handleCollect(row) {
            const { id, name } = this.userInfo;
            const isResPackage = row.type === 'resPackage';
            this.$post(
                'collectResource',
                {
                    userId: id,
                    userName: name,
                    collectType: row.isCollect ? 1 : 0,
                    resourceType: isResPackage ? 3 : row.type,
                    resourceId: isResPackage ? row.resourcePackageId : row.resourceId,
                },
                true
            ).then((res) => {
                if (res.serviceFlag === 'TRUE') {
                    this.$message.success(res.returnMsg);
                } else {
                    this.$message.error(res.returnMsg);
                }
            });
        },
        headerClassName({ column }) {
            if (column.property === 'resourceName') {
                return 'custom-header';
            } else if (column.property === 'tool') {
                return 'custom-tool';
            }
            return '';
        },
        updateTable() {
            this.$emit('updateTable');
            this.$refs.resourceRef.clearSelection();
            this.selectList = [];
        },
        getAreaImg(status) {
            status = Boolean(status);
            return status ? focusAreaIcon : blurAreaIcon;
        },
        getAreaTip(status) {
            status = Boolean(status);
            return status ? '取消重点区域' : '标注重点区域';
        },
    },
};
</script>
<style lang="less" scoped>
.resource-list {
    display: flex;
    flex-direction: column;
    height: 100%;
    &__table {
        padding: 0 12px;
        flex: 1;
        height: 0;
        display: flex;
        flex-flow: column;
        background: rgba(23, 42, 65, 0.6);
        box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.65);
        backdrop-filter: blur(8px);
        /deep/ .el-table__header-wrapper .gutter {
            background-color: #f6f7fa;
            .custom-cell {
                border-top: none !important;
                border-bottom: none !important;
            }
        }
        .el-table {
            flex: 1;
            width: 100%;
            background-color: transparent;
            &::before {
                display: none;
            }
            /deep/.el-table__body-wrapper {
                height: calc(100% - 37px);
                overflow-y: auto;
                &::-webkit-scrollbar {
                    width: 3px;
                    height: 3px;
                }
                &::-webkit-scrollbar-thumb {
                    border-radius: 10px;
                    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
                    background: #5c6f92;
                }
                &::-webkit-scrollbar-track {
                    /* 滚动条里面轨道 */
                    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
                    border-radius: 10px;
                    background: transparent;
                }
                &::-webkit-scrollbar-corner {
                    background: rgba(0, 0, 0, 0);
                }
            }
            /deep/ .el-table__body {
                width: 100% !important;
            }
            /deep/ .el-table__row {
                box-shadow: inset 0px -1px 0px 0px rgba(0, 0, 0, 0.08);
                opacity: 0.7;
                &:nth-child(odd) > td {
                    background: #101d33;
                }
                &:nth-child(even) > td {
                    background: #1b293b;
                }
                &:hover > td {
                    background-color: #040a1e !important;
                    cursor: pointer;
                }
            }
            /deep/ .success-row {
                .el-table__cell {
                    background-color: #040a1e !important;
                }
            }
            /deep/ .custom-cell {
                padding: 4px 0;
                .cell {
                    padding-right: 4px;
                    line-height: 20px;
                    .el-checkbox {
                        margin-bottom: 0;
                    }
                }
            }
            /deep/ .el-table__row td.el-table__cell,
            /deep/  th.el-table__cell {
                border-top: none !important;
                border-bottom: none !important;
            }
            /deep/ .hide-expandIcon {
                .cell .el-table__expand-icon {
                    display: none;
                }
            }
            /deep/ .custom-hide {
                .cell {
                    display: none;
                }
            }
            /deep/ .el-table__expanded-cell {
                padding: 0;
                .expandCell {
                    padding: 7px 0 7px 34px;
                    border-bottom: 1px solid #ebeef5;
                    background-color: #f7f7f7;
                    cursor: pointer;
                    &.success-row {
                        background-color: #000;
                    }
                }
            }
            /deep/ .custom-header {
                border-radius: 0;
                .cell {
                    padding-left: 0;
                }
            }
            /deep/ .custom-tool {
                padding-left: 1px;
            }
            .regionName {
                display: flex;
                .icon {
                    color: #fff;
                    width: 24px;
                    align-self: center;
                }
                .expand-item {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding-right: 3px;
                    width: 50px;
                }
                .content {
                    display: flex;
                    flex-direction: column;
                    width: calc(100% - 24px);
                    .name {
                        display: flex;
                        width: 100%;
                        .text {
                            max-width: calc(100% - 17px);
                            overflow: hidden;
                            text-overflow: ellipsis;
                            white-space: nowrap;
                            color: #fff;
                        }
                        .areaIcon {
                            margin-left: 3px;
                        }
                    }
                    .type {
                        color: #aaa;
                    }
                }
            }
            .focusArea {
                position: relative;
                vertical-align: text-bottom;
                transform: translateY(1.5px);
                height: 18px;
            }
            .custom-checkbox {
                color: #fff;
                margin-bottom: 0;
                .el-icon-star-on,
                .el-icon-star-off {
                    vertical-align: middle;
                    color: #fff;
                }
                .el-icon-star-on {
                    font-size: 22px;
                }
                .el-icon-star-off {
                    font-size: 18px;
                }
                /deep/ .el-checkbox__input {
                    display: none;
                }
                /deep/ .el-checkbox__label {
                    padding-left: 0;
                    line-break: 14px;
                    font-size: 20px;
                }
            }
            .custom-select {
                width: 82px;
                /deep/ .el-input__inner {
                    padding-left: 8px;
                    padding-right: 23px;
                    border: none;
                    color: #0091ff;
                    background-color: transparent;
                }
                /deep/ .el-input__suffix {
                    right: 0;
                    .el-select__caret {
                        color: #0091ff;
                    }
                }
            }
        }
        .pagination {
            margin: 12px 0;
            padding-left: 0;
            overflow-x: auto;
            &::-webkit-scrollbar {
                height: 3px;
            }
            &::-webkit-scrollbar-thumb {
                background: #5c6f92;
                border-radius: 3px;
                height: 3px;
                width: 3px;
            }
            /deep/.btn-prev,
            /deep/ .btn-next {
                color: #fff;
                background: unset;
                border: none;
            }
            /deep/ .el-pager {
                background-color: transparent;
                .number,
                .el-icon {
                    color: #ffffff;
                    background: unset;
                    font-weight: 500;
                    &.active {
                        background: rgba(117, 163, 223, 0);
                        border-radius: 3px;
                        border: 1px solid #a8bfe8;
                    }
                }
            }

            /deep/ .el-pagination__jump {
                margin-left: 0;
            }
            /deep/ .el-pagination__total {
                color: #fff;
            }
            /deep/ .el-pagination__sizes {
                margin-right: 3px;
                .el-input {
                    width: 81px;
                    .el-input__inner {
                        height: 22px;
                        line-height: 22px;
                        font-size: 12px;
                        color: #fff;
                        background: #1f3459;
                        box-shadow: inset 0px 0px 7px 0px rgba(73, 132, 255, 0.5);
                        border-radius: 3px;
                        border: 1px solid #a8bfe8;
                    }
                    .el-input__suffix {
                        top: 1px;
                    }
                }
            }
        }
    }

    &__tools {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 58px;
        text-align: right;
        background: rgba(23, 42, 65, 0.6);
        box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.65);
        backdrop-filter: blur(8px);
        border-radius: 0px 0px 0px 8px;
    }
    .refresh {
        vertical-align: text-top;
        font-size: 17px;
    }
}
.mr-2 {
    margin-right: 2px;
}
.mb-0 {
    margin-bottom: 0;
}
.w-80 {
    color: #fff;
    background-color: #3871b3;
    border: none;
    width: 80px;
}
.green {
    color: #53FFFF;
    &:hover {
        color: #53FFFF;
    }
}
</style>
<style lang="less">
.custom-confirm-dialog {
    border-radius: 10px;
    // background: linear-gradient(180deg, #fff6ec 0%, #ffffff 100%);
    background: rgba(23, 42, 65, 0.6);
    box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.65);
    border-radius: 12px;
    backdrop-filter: blur(8px);
    border-color: #ccc;
    .el-message-box__header {
        padding-bottom: 0;
        .el-message-box__title {
            flex-direction: column;
            font-weight: bold;
            color: #fff;
            .el-message-box__status {
                margin-bottom: 24px;
                font-size: 56px !important;
            }
        }
    }
    .el-message-box__content {
        .el-message-box__message {
            font-size: 16px;
            color: rgba(255, 255, 255, 0.65);
        }
    }
    .el-message-box__btns {
        .el-button {
            width: 25%;
        }
    }
    &.custom-red {
        .el-message-box__btns {
            .el-button:last-of-type {
                background-color: #ee0a24;
                border-color: #ee0a24;
            }
        }
    }
}
.el-select-dropdown {
    background-color: #101d34;
    border-color: #a8bfe8;
    .el-select-dropdown__item {
        color: #fff;
    }
    .el-select-dropdown__item:hover {
        color: #409eff;
    }
    .el-select-dropdown__item.hover {
        color: #409eff;
    }
    &.is-multiple .el-select-dropdown__item.selected {
        background: rgba(0, 0, 0, 0.5);
    }
}
</style>
