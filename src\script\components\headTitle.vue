<template functional>
    <div class="head-title space-theme">
        <!-- 路由 -->
        <div v-if="props.paths.length > 1" class="route">
            <el-link v-if="!props.isNotBack" class="bold" type="primary" @click="props.back">
                <i class="el-icon-back"></i>
                返回
            </el-link>
            <span v-if="!props.isNotBack" class="line">|</span>
            <span v-for="(path, i) in props.paths" class="path" :key="path">
                <span v-if="i !== 0" class="arrow">></span>
                <span class="curLayer">{{ path }}</span>
            </span>
        </div>
        <!-- 标题 -->
        <div class="title">
            <span class="dot"></span>
            <slot
                name="title"
                :titleName="props.titleName"
                :pathname="props.paths[props.paths.length - 1]"
            >
                {{ props.titleName || props.paths[props.paths.length - 1] }}
            </slot>
            <!-- 右侧 slot-->
            <div class="tool">
                <slot name="tool"></slot>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'head-title',
    props: {
        isShowRoute: {
            type: Boolean,
            default: false,
        },
        titleName: {
            type: String,
            default: '',
        },
        paths: {
            type: Array,
            default: () => [],
        },
        back: {
            type: Function,
            default: () => {},
        },
        isNotBack: {
            type: Boolean,
            default: false,
        },
    },
};
</script>
<style lang="less" scoped>
.head-title {
    position: relative;
    display: flex;
    flex-direction: column;

    &.space-theme {
        padding: 10px 12px;
        box-shadow: 0px 0px 25px 0px rgba(80, 143, 242, 0.1);
        // border-radius: 10px 10px 0px 0px;
        backdrop-filter: blur(15px);
        background: linear-gradient(270deg, #101620 0%, #1b2f4d 100%);
    }
    .route {
        display: flex;
        margin-bottom: 2px;
        align-items: center;
        .line {
            margin: 0 4px;
            vertical-align: middle;
        }
        .path {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
            vertical-align: middle;
            .arrow {
                margin-left: 4px;
            }
            &:last-child {
                color: rgba(255, 255, 255, 0.45);
                font-weight: bold;
            }
        }
    }

    .title {
        display: flex;
        justify-content: space-between;
        line-height: 32px;
        font-size: 20px;
        font-weight: bold;
        line-height: 32px;
        color: #ffffff;
        text-shadow: 0px 0px 4px rgba(62, 136, 233, 0.64);
        .dot {
            margin-right: 8px;
            width: 8px;
            height: 8px;
            background: #53ffff;
            border-radius: 4px;
            align-self: center;
        }
        .tool {
            flex: 1;
        }
    }
}
</style>
