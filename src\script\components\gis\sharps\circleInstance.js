import {
    addLastStatus,
    toCoordinate,
    formatBaseStations,
    getDiffSet,
    getIntersectionSet,
    isImportCreated,
    positionCircleTest,
    isExistWithinCurCircle,
    getCoordinateExtremum,
} from '@/utils/method.js';
import { menuList } from '@/script/constant/gisOption.js';
import * as gisUtils from '@/utils/gisUtils';
export default () => {
    let mapIdToDetailFn = {};
    return class Circular {
    constructor(i, option = {}) {
        const h = i || 1;
        this.region = {
            prop: 'circular',
            circle: {},
            plane: null,
            color: option.color || 0x0085F9,
            frameColor: option.frameColor || 0x1A7CFF,
            innerBaseStations: [],
        }
        this.hole = {
            prop: 'hole',
            points: [],
            plane: [],
            color: 0xffffff,
            maxH: h / 10000 + (1 / 100),
        };
        this.radiate = {
            prop: 'radiate',
            circle: {},
            plane: null,
            color: 0xe59f00,
            frameColor: 0x1A7CFF,
        }
        this.isJustShow = option.isJustShow;
        this.baseStations = [];
        this.distance = 3;  // 默认辐射距离
        this.i = i;
    }
    static initGisInfo(g, that) {
        if (Circular.isInitialized) {
            return;
        }

        Circular.isInitialized = true;
        Circular.g = g;
        Circular.that = that;
        Circular.currentClickHole = null;
        Circular.i = 1;

        Circular.holeLayer = new g.layer();
        Circular.holeLayer.visible = true;
        g.gis.scene.add(Circular.holeLayer);

        // 空洞点击事件
        g.event.addClick(Circular.holeLayer, (data, event) => {
            const curHole = data.object.operateType;
            if (that.isJustShowLayer || curHole.isJustShow) return;
            Circular.currentClickHole = curHole.index;
            that.layerObj = that.planeObjs[curHole.i];
            if (event.button === 0) {
                // 鼠标左键
                Circular.setCurOperateItem(curHole);
                // 清除plane
                Circular.holeLayer.remove(data.object);
                // 绘制可编辑的多边形
                const mesh = gisUtils.areaEditCreate(g, {
                    name: '编辑图层',
                    color: curHole.color,
                    points: curHole.points,
                    justShow: that.isJustShowLayer,
                });
                g.layerList.areaEdit.start(mesh);
            } else if (event.button === 2) {
                that.menuList = menuList.filter((item) => item.prop === 'delHole');
                that.isShowMenu = true;
            }
        });
        // 多边形编辑完成事件
        g.layerList.areaEdit.onEditFinish.addEvent(Circular.areaEditFinish, 'Circular');
        // 圆双击
        g.event.addDoubleClick(g.layerList['圈选'], (data) => {
             const obj = data.object;
             const name = String(obj.name);
             const resourceId = name.replace('circular-', '');
             const setCurDetail = mapIdToDetailFn[resourceId];
             setCurDetail && setCurDetail();
        });
    }
    static async initRegionCoors(circles, isClearAll = false, isResetCheckBase = false, isEntry = false) {
        const { g, that, getHollowPolygon, initBaseStations, getExternalSquare } = Circular;
        if (isClearAll) that.clearAll();
        if (isResetCheckBase) that.isCheckBase = false;
        if (!circles || !circles.length) {
            that.$message.error('该圆形区域不存在');
            return;
        }
        // 已创建的复合区域，获取复合区域区域内基站
        if (that.isShowHideManage && that.isMultiRegion && that.baseInfo.regionId) {
            const { regionInnerList, hollowOutCellList } = await that.getCreatedInnerStations(that.baseInfo.regionId) || {};
            const hollowOutCellStations = formatBaseStations(hollowOutCellList || [], 'noAdd');
            let regionInnerListData = regionInnerList.filter((item) => item.cellType === 2);
            if (!regionInnerListData.length) {
                regionInnerListData = regionInnerList;
            }
            const regionInnerStations = formatBaseStations(regionInnerListData, 'added');
            that.multiCreatedStations = [...hollowOutCellStations, ...regionInnerStations];
        }
        // 处理空洞数据
        const hollowPolygon = getHollowPolygon(that.baseInfo, isEntry);

        const mergeCircles = [];
        const initBaseStationsPromises = circles.map((circle, index) => {
            // 赋值
            const hollows = hollowPolygon[index];
            that.planeObjs[index] = new Circular(index);

            that.layerObj = that.planeObjs[index];
            that.layerObj.drawRegion(circle, index, hollows);

            mergeCircles.push(...getExternalSquare(circle));

            if (that.isShowHideManage) {
                return initBaseStations(that.layerObj, that.isMultiRegion);
            }
            return Promise.resolve();
        });
        await Promise.all(initBaseStationsPromises);

        that.setAllBaseStations();

        if (that.multiCreatedStations && that.multiCreatedStations.length) {
            addLastStatus(that.multiCreatedStations, that.allBaseStations, (item, tarItem) => {
                item.cellBoundary = tarItem.cellBoundary;
            });
        }
        // 绘制辅助圆
        that.drawCircleChoice();
        // 移动到地图中心
        if (mergeCircles.length) {
            const extremum = getCoordinateExtremum(mergeCircles);
            g.cameraControl.zoomByPoints(extremum, 1);
        }
    }
    static initRegions(circles, { row, setCurDetail, config }) {
        const { that, getHollowPolygon } = Circular;
        if (!circles || !circles.length) {
            that.$message.error('该圆形区域不存在');
            return;
        }
        // 处理空洞数据
        const hollowPolygon = getHollowPolygon(row);

        const mergeCircles = [];
        const sharps = [];

        circles.forEach((circle, index) => {
            // 赋值
            const hollows = hollowPolygon[index];
            const curSharp = new Circular(index, config);
            // 绘制区域（图形 && 空洞）
            if (setCurDetail) {
                index = row.resourceId || row.regionId;
                mapIdToDetailFn[index] = setCurDetail;
            }
            curSharp.drawRegion(circle, index, hollows);
            sharps.push(curSharp);
            mergeCircles.push(...Circular.getExternalSquare(circle));
        });
        return { mergeRegions: mergeCircles, sharps };
    }
    static getExternalSquare(centerPoint) {
        const LAT_PIECE = 0.00001;
        const LNG_PIECE = 0.000009;
        const { centerLatitude, centerLongitude, radius } = centerPoint;
        return [
            { lat: centerLatitude + radius * LAT_PIECE, lng: centerLongitude - radius * LNG_PIECE },
            { lat: centerLatitude + radius * LAT_PIECE, lng: centerLongitude + radius * LNG_PIECE },
            { lat: centerLatitude - radius * LAT_PIECE, lng: centerLongitude + radius * LNG_PIECE },
            { lat: centerLatitude - radius * LAT_PIECE, lng: centerLongitude - radius * LNG_PIECE }
        ]
    }
    static getHollowPolygon(baseInfo, isEntry = false) {
        const { g, that, addInxForHollowList } = Circular;
        let holePolygonList = [];
        let { hollowPolygonList, multiPolygonList, circle = {} } = baseInfo;
        if (isImportCreated(hollowPolygonList)) {
            if (!multiPolygonList || !multiPolygonList.length) {
                multiPolygonList = circle && circle.radius ? [{
                    ...circle,
                    radius: circle.radius,
                }] : [];
            }
            holePolygonList = addInxForHollowList(multiPolygonList, hollowPolygonList, g, 'circular').holeList;
        } else {
            holePolygonList = hollowPolygonList;
        }

        if (isEntry) {
            holePolygonList = that.entryHoleList;
        }
        // 处理空洞数据
        const hollowPolygon = {};
        holePolygonList && holePolygonList.forEach((item) => {
            if (hollowPolygon[item.region]) {
                hollowPolygon[item.region].push(toCoordinate(item.polygon));
            } else {
                hollowPolygon[item.region] = [toCoordinate(item.polygon)];
            }
        });
        return Object.values(hollowPolygon).map((item) => item);
    }
    static addInxForHollowList(regionList = [], holeList = [], g) {
        const newHoleList = [];
        const copyHoleList = [...holeList];
        for (const [inx, region] of regionList.entries()) {
            let index = 0;
            for (const [holeInx, hole] of holeList.entries()) {
                const isExist = isExistWithinCurCircle(hole.polygon, region, g);
                if (isExist) {
                    newHoleList.push({
                        ...hole,
                        index: index++,
                        region: inx,
                    });
                    copyHoleList[holeInx] = null;
                }
            }
        }
        return {
            holeList: newHoleList,
            invalidHoleList: copyHoleList.filter(Boolean),
        };
    }
    static initBaseStations(layerObj, isMulti = false) {
        const { that, getBaseStations } = Circular;
        const { region, radiate, distance } = layerObj;
        return getBaseStations(region.circle, that.defExpansion, true, isMulti)
            .then((res) => {
                const { innerBaseStations, baseStations } = res;
                layerObj.baseStations = baseStations;
                region.innerBaseStations = innerBaseStations;
                radiate.circle = {
                    ...region.circle,
                    radius: region.circle.radius + distance * 1000,
                };
            })
            .catch((err) => {
                console.error(err);
                that.$message.error('绘制区域时获取基站数据报错');
            });
    }
    static setCurOperateItem(curOperateItem) {
        Circular.curOperateItem = curOperateItem;
    }
    static async circleFinish(radius, startPoint, data) {
        const { g, that, getBaseStations } = Circular;
        const { region, distance, radiate, baseStations: preBaseStations } = that.layerObj;
        region.circle = {
            radius,
            centerLatitude: startPoint.lat,
            centerLongitude: startPoint.lng,
        };
        region.plane = data.circle;
        const isExitRadiate = radiate && radiate.plane && radiate.circle;
        const { innerBaseStations, baseStations } = await getBaseStations(
            region.circle,
            distance,
        );
        if (preBaseStations.length) {
            radiate.circle = {
                ...region.circle,
                radius: radius + distance * 1000,
            };
        } else {
            that.layerObj.baseStations = baseStations;
        }
        if (isExitRadiate) {
            g.layerList['圈选'].remove(radiate.plane);
            that.layerObj.drawRadiate(radiate.circle);
        }
        // 圆形区域收缩
        if (innerBaseStations.length < region.innerBaseStations.length) {
            const diffBaseStations = getDiffSet(region.innerBaseStations, innerBaseStations);
            diffBaseStations.forEach((item) => Object.assign(item, {
                status: 'noAdd',
                isArea: '否',
            }));
            addLastStatus(diffBaseStations, preBaseStations);
            // 修改其他区域有差集diffBaseStations相关的点
            const otherLayers = that.planeObjs.filter((item) => item && (item.i !== that.layerObj.i));
            otherLayers.forEach((item) => {
                const intersection = getIntersectionSet(item.baseStations, diffBaseStations);
                intersection.forEach((val) => Object.assign(val, {
                    status: 'noAdd',
                }));
                addLastStatus(intersection, item.baseStations);
            });
        }
        region.innerBaseStations = innerBaseStations;
        addLastStatus(innerBaseStations, preBaseStations);
        that.setAllBaseStations();
        that.setBasePoints(true);
        that.$eventBus.$emit('updateSharpInfo');
    }
    static async getBaseStations(circle, expansion, isInit = false, isMulti = false) {
        const that = Circular.that;
        if (!circle || !Object.keys(circle).length) {
            that.$message.warning('区域不存在');
            return;
        }

        const params = {
            type: 1,
            shapeType: 1,
            isMultiRegion: 0,
            expansion, // 辐射距离，10：表示查看区域所有基站
            circle: {
                ...circle,
                radius: circle.radius,
            }
        }
        if (isInit && !that.isMultiRegion) {
            const isCreated = that.isCreated;
            const regionId = that.baseInfo.regionId;
            Object.assign(params, {
                type: isCreated && regionId ? 0 : 1,
                regionId: regionId || undefined,
            })
        }
        const res = await that.$post('getRegionExpansionCells', params);
        const { regionOutList, regionInnerList, hollowOutCellList } = res;
        let regionInnerListData = regionInnerList;
        if (params.regionId) {
            regionInnerListData = regionInnerList.filter((item) => item.cellType === 2);
            if (!regionInnerListData.length) {
                regionInnerListData = regionInnerList;
            }
        }
        const innerBaseStations = formatBaseStations(regionInnerListData, isMulti ? 'noAdd' : 'added');
        const outBaseStations = formatBaseStations(regionOutList, 'noAdd');
        const hollowOutCellStations = formatBaseStations(hollowOutCellList || [], 'noAdd');
        const baseStations = [...innerBaseStations, ...outBaseStations, ...hollowOutCellStations];
        return {
            innerBaseStations,
            outBaseStations,
            baseStations,
        };
    }
    static clearAll() {
        if (!Circular.isInitialized) return;
        Circular.holeLayer.removeAll();
        Circular.g.layerList['圈选'].removeAll();
    }
    static unbindEvent() {
        if (!Circular.isInitialized) return;
        const g = Circular.g;
        g.event.removeClick(Circular.holeLayer);
        if (g.layerList.areaEdit) {
            g.layerList.areaEdit.onEditFinish.removeEvent('Circular');
        }
        Circular.isInitialized = false;
    }
    static areaEditFinish(data) {
        const { g, that } = Circular;
        if (that.shapeType !== 1) return;
        const editPoints = gisUtils.three2world(g, data.points);
        data.name = '$123';
        g.layerList.areaEdit.removeByName('$123');

        that.layerObj.drawHole(editPoints);
        that.setBasePoints(true);
    }
    static getParams(allAreas, hollowOutRegionList) {
        let regionParams;
        if (allAreas.length === 1) {
            regionParams = {
                circle: allAreas[0].region.circle || {},
                isMultiRegion: 0,
                hollowOutRegionList,
            }
        } else if (allAreas.length > 1) {
            regionParams = {
                isMultiRegion: 1,
                regionList: allAreas
                    .map((area) => {
                        const circle = area.region.circle || {};
                        return {
                            areaType: 1,
                            polygon: [],
                            centerX: circle.centerLongitude, //圆形纬度
                            centerY: circle.centerLatitude, //圆形经度
                            radius: circle.radius ? circle.radius : null, //圆形半径
                        };
                    }).filter((item) => Boolean(item.radius)),
                hollowOutRegionList,
            }
        }
        return regionParams;
    }
    drawRegion(circle = this.region.circle, i = this.i, holePoints) {
        const { g, that } = Circular;
        const name = `circular-${i}`;
        const { centerLatitude, centerLongitude, radius } = circle;
        const mesh = g.layerList.圈选.create({
            name,
            circleColor: this.region.color || 0x0085F9,
            circleOpacity: 0.5,
            circleFrame: true,
            circleFrameColor: this.region.frameColor || 0x1A7CFF,
            cirCleShowClose: true,
            circleShowRadius: !that.isJustShowLayer && !this.isJustShow,
            radius,
            startPoint: { lat: centerLatitude, lng: centerLongitude },
        });
        this.region.plane = mesh.circle;
        this.region.circle = circle;
        this.i = i;

        if (holePoints) {
            this.hole.points = holePoints;
        }
        this.hole.points.forEach(item => {
            this.drawHole(item);
        });
    }
    clear() {
        Circular.g.layerList['圈选'].remove(this.region.plane);
        this.hole.plane.forEach((item) => {
            Circular.holeLayer.remove(item);
        });
    }
    drawHole(editPoints) {
        if (!editPoints || !editPoints.length) return;
        const g = Circular.g;
        const { color, maxH } = this.hole;
        const points = editPoints.map((item) => [item.lng, item.lat]);
        const holePoints = g.math.lsRegular(points) || [];

        g.meshList.plane.opacity = 0.3;

        const data = [
            {
                ls: holePoints,
                layers: [{ maxH, color }],
            },
        ];
        Object.assign(data, {
            needFrame: true,
            frameColor: 0x666666,
        });
        const holePlane = g.meshList.plane.create(data);
        const holeObjs = Object.assign({}, this.hole, {
            points: editPoints,
            i: this.i,
            index: this.hole.plane.length,//一个区域多个空洞标识
            isJustShow: this.isJustShow
        });
        holePlane.operateType = holeObjs;
        this.hole.plane.push(holePlane);
        Circular.holeLayer.add(holePlane);
        g.gis.needUpdate = true;
    }
    drawRadiate(circle) {
        const g = Circular.g;
        const { radius, centerLatitude, centerLongitude } = circle;
        const name = `circular-radiate-${this.i}`;
        const mesh = g.layerList.圈选.create({
            name,
            circleColor: 0xe59f00,
            circleOpacity: 0.2,
            circleFrame: true,
            circleFrameColor: 0xF39003,
            circleShowRadius: false,
            radius,
            startPoint: { lat: centerLatitude, lng: centerLongitude },
            ht: 0,
        });
        this.radiate.plane = mesh.circle;
    }
    async setRadiateInfo() {
        const { g, that, getBaseStations } = Circular;
        const { radiate, region, distance } = this;
        const { baseStations } = await getBaseStations(region.circle, distance);
        addLastStatus(this.baseStations, baseStations);
        this.baseStations = baseStations;
        radiate.circle = {
            ...region.circle,
            radius: region.circle.radius + distance * 1000,
        };
        radiate.plane && g.layerList['圈选'].remove(radiate.plane);
        this.drawRadiate(radiate.circle);
        that.setAllBaseStations();
        that.setBasePoints();
    }
    updateRadiation(distance) {
        if (distance && this.distance !== distance) {
            this.distance = distance;
            this.setRadiateInfo();
        }
    }
    async selectMenu(prop) {
        const { g, that } = Circular;
        if (prop === 'cancelRadiation') {
            g.layerList['圈选'].remove(this.radiate.plane);
            this.radiate.plane = null;
            that.isShowRadiation = false;
        } else if (prop === 'hole') {
            Circular.setCurOperateItem(this.hole);
            that.start('areaEdit'); // todo、areaEdit -> 多边形
        } else if (prop === 'delHole') {
            const { hole } = this;
            hole.points = [];
            const currentClickHole = Circular.currentClickHole || 0;
            Circular.holeLayer.remove(hole.plane[currentClickHole]);
            Circular.currentClickHole = null;
            that.setBasePoints(true);
        } else if (prop === 'auxiliaryCircle') {
            that.start('circleChoice');
        } else if (prop === 'delCircle') {
            // 删除圈选
            g.layerList['圈选'].remove(that.curCircleChoiceObj);
            const name = that.curCircleChoiceObj.name;
            that.curCircleChoiceObj = null;
            const circlePoints = that.circleChoices[name]['choicePoints'];
            if (circlePoints && circlePoints.length) {
                circlePoints.forEach((item) => {
                    item.status = 'noAdd'; // todo：非待添加，而是彻底删除
                });
                that.setBasePoints();
            }
            delete that.circleChoices[name];
        } else if (prop === 'outwardRadiate') {
            Circular.setCurOperateItem(this.radiate);
            const { circle } = this.radiate;
            if (circle && Object.keys(circle).length) {
                this.drawRadiate(circle);
            } else {
                this.setRadiateInfo();
            }
        } else if (prop === 'delCircular') {
            const g = Circular.g;
            const { region, hole, radiate } = this;
            g.layerList['圈选'].remove(region.plane);
            hole.plane.forEach((item) => {
                Circular.holeLayer.remove(item);
            });
            Circular.currentClickHole = null;
            radiate.plane && g.layerList['圈选'].remove(radiate.plane);
            that.planeObjs[this.i] = null;
            that.layerObj = {};
            that.setAllBaseStations();
            that.setBasePoints();
            that.$eventBus.$emit('updateSharpInfo');
        }
    }
    // 修改空洞里的基站
    handlerHolePoints(allBaseStations) {
        const g = Circular.g;
        const prePointList = [...allBaseStations];
        const prePositionTest = positionCircleTest(prePointList, this.region.circle, g);

        prePositionTest.forEach((item, index) => {
            const val = allBaseStations[index];
            if (item && val.status === 'noAdd') {
                val.status = 'added';
            }
        });
        const holePlane = Circular.holeLayer.Group.children;
        holePlane && holePlane.forEach((item) => {
            if (!item.operateType) {
                return;
            }
            const pointList = [...allBaseStations];
            const areaList = [...item.operateType.points];
            const positionTest = g.math.positionTest(pointList, areaList);
            positionTest.forEach((value, index) => {
                const val = allBaseStations[index];
                if (value && val.status === 'added') {
                    val.status = 'noAdd';
                }
            });
        });
    }
}
}
