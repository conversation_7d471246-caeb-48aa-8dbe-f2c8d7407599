<!--
 * @Date: 2025-08-05 20:41:09
 * @LastEditors: liurong <EMAIL>
 * @LastEditTime: 2025-08-06 19:24:34
 * @FilePath: \earthquake\src\script\components\copilot\business\queryEarthInfo.vue
-->
<template>
  <div class="query-earth">
    <dataTable
      :columns="columns"
      :data="data"
      :stripe="false"
      border
      class="query-earth__table"
    >
      <template #radio="{ row }">
        <el-radio v-model="row.radio"></el-radio>
      </template>
    </dataTable>
    <div class="query-earth__footer">
      <div class="query-earth__footer-btn" @click="handleSubmit">确定</div>
    </div>
  </div>
</template>
<script>
import dataTable from "_com/table/dataTable.vue";
export default {
  name: "query-earth",
  components: {
    dataTable,
  },
  inheritAttrs: false,
  props: {
    outData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      columns: [
        { prop: "radio", label: "", width: 50 },
        { prop: "disasterName", label: "灾害名称" },
        { prop: "time", label: "发震时间" },
        { prop: "location", label: "震中位置" },
        { prop: "magnitude", label: "震级" },
      ],
      data: [
        {
          radio: false,
          disasterName: "地震",
          time: "2025-08-06 19:24:34",
          location: "中国",
          magnitude: "6.0",
        },
        {
          radio: false,
          disasterName: "地震",
          time: "2025-08-06 19:24:34",
          location: "中国",
          magnitude: "6.0",
        },
        {
          radio: true,
          disasterName: "地震",
          time: "2025-08-06 19:24:34",
          location: "中国",
          magnitude: "6.0",
        },
      ],
    };
  },
  mounted() {
    // test loading
    setTimeout(() => {
      this.$emit("changeStatus", "success");
    }, 2000);
  },
  methods: {
    handleSubmit() {
      this.$emit("submit");
    },
  },
};
</script>

<style lang="less" scoped>
.query-earth {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 10px;
  .query-earth__table {
    /deep/ .el-table {
      tr,
      tr th {
        background-color: transparent !important;
      }
      tbody tr:hover > td {
        background-color: transparent !important;
      }
      .el-table__cell {
        font-weight: 400 !important;
        font-size: 13px !important;
        color: #ffffff !important;
      }
      &.el-table--border {
        border: 1px solid #75a2dd;
        border-right: none;
        border-bottom: none;
        .el-table__cell {
          border-right: 1px solid #75a2dd;
          border-bottom: 1px solid #75a2dd !important;
        }
        &::after {
          background-color: #75a2dd;
        }
      }
      .el-radio {
        &__inner {
          border: 1px solid #75a2dd;
          background-color: transparent;
          &::after {
            background-color: #75a2dd;
            width: 6px;
            height: 6px;
          }
        }
      }
    }
  }
  .query-earth__footer {
    display: flex;
    justify-content: flex-end;
    &-btn {
      user-select: none;
      cursor: pointer;
      width: 88px;
      height: 28px;
      background: rgba(56, 113, 179, 0.7);
      border-radius: 14px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 400;
      font-size: 15px;
      color: #ffffff;
    }
  }
}
</style>
