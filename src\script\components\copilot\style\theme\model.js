export const themes = {
    default: {
        baseBgColor: `rgba(${255},${255},${255},${1})`, //基础背景色
        baseTextColor: `rgba(${26},${26},${26},${1})`, //大体字体颜色
        subTextColor: `rgba(${123},${123},${123},${1})`, //二级字体颜色
        baseBorderColor: `rgba(40, 121, 255, 1)`, //基础边框颜色
        dialogueBgColor: `rgba(${230},${238},${252},${1})`, //对话背景色
        inputBorderColor: `rgba(${113},${146},${253},${1})`, //输入框边框色
        baseFontSize: `${16}px`, //默认字体大小
        subFontSize: `${14}px`, //二级字体大小
        footFontSize: `${13}px`, //底部字体大小
        baseBorderShadow: `2px 2px 4px rgba(62, 136, 233, 0.64)` //基础阴影色
    },
    blue: {
        baseBgColor: `rgba(${16},${49},${92},${0.81})`,
        baseTextColor: `rgba(${255},${255},${255},${1})`,
        subTextColor: `rgba(${140},${154},${172},${1})`,
        baseBorderColor: `rgba(${103},${243},${252},${1})`,
        dialogueBgColor: `linear-gradient( 90deg, #05A1EC 0%, #03498C 100%)`,
        inputBorderColor: `rgba(${38},${255},${249},${1})`,
        baseFontSize: `${16}px`,
        subFontSize: `${14}px`,
        footFontSize: `${13}px`,
        baseBorderShadow: `2px 2px 4px rgba(62, 136, 233, 0.64)`
    },
    dark: {
        baseBgColor: `rgba(${4},${16},${33},${0.81})`,
        baseTextColor: `rgba(${255},${255},${255},${1})`,
        subTextColor: `rgba(${140},${154},${172},${1})`,
        baseBorderColor: `rgba(${117},${162},${221},${1})`,
        dialogueBgColor: `linear-gradient( 90deg, #3768D1 0%, #002446 100%)`,
        inputBorderColor: `rgba(${117},${162},${221},${1})`,
        baseFontSize: `${16}px`,
        subFontSize: `${14}px`,
        footFontSize: `${13}px`,
        baseBorderShadow: `2px 2px 4px rgba(62, 136, 233, 0.64)`
    }
};
