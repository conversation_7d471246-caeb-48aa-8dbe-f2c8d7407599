// import XLSX from 'xlsx';
const XLSX = require('xlsx');
/**
 * @description: 
 * @param {Array} data 数据
 * @param {String} name 导出Excel文件名字
 *  * @param {Array} name ws配置
 * @return: 
 */
function exportExcel(data, name, wsConfig) {
    const ws = XLSX.utils.json_to_sheet(data);
    wsConfig.forEach(item => {
        ws[item.key] = item.value;
    });
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'SheetJS');
    XLSX.writeFile(wb, name + '.xlsx');
}

/**
 * @description: 导入excel文件并返回数据
 * @param {function} 回调函数参数data,dataRef,一个是数据，一个是exce表单的索引 
 * @return: 
 */
function importExcel(callback, faultback, vm) {
    var inputObj = document.createElement('input');
    inputObj.setAttribute('id', 'file');
    inputObj.setAttribute('type', 'file');
    inputObj.setAttribute('name', 'file');
    inputObj.setAttribute('style', 'visibility:hidden');
    inputObj.setAttribute('accept', '.xlsx,.xls,.csv');
    inputObj.addEventListener('change', (evt) => {
        const files = evt.target.files;
        if (files && files[0]) {
            let _fname = files[0].name;
            let _flist = _fname.split('.');
            let _fsuffix = _flist[_flist.length - 1];
            if (_fsuffix != 'xls' && _fsuffix != 'xlsx' && _fsuffix != 'csv') {
                faultback('请上传excel格式文件！');
            } else {
                _file(files[0], (data, dataRef) => {
                    callback(data, dataRef);
                }, vm);
            }
        }
    });
    document.body.appendChild(inputObj);
    inputObj.value;
    inputObj.click();

}

/**
 * @description: 处理文件
 * @param {Object} file 文件对象
 * @param {function} callback 回调函数 
 * @return: 
 */
function _file(file, callback, vm) {
    const make_cols = refstr => Array(XLSX.utils.decode_range(refstr).e.c + 1).fill(0).map((x, i) => ({
        name: XLSX.utils.encode_col(i),
        key: i
    }));

    /* Boilerplate to set up FileReader */
    const reader = new FileReader();
    if (vm) {
        vm.$popupLoading({
            show: true,
            element: vm.element,
            message: '数据解析中，请等待...',
        });
    }
    reader.onload = (e) => {
        setTimeout(() => {   /* Parse data */
            const bstr = e.target.result;
            const wb = XLSX.read(bstr, {
                type: 'binary'
            });
            if (vm) {
                vm.$popupLoading({
                    show: false,
                });
            }
            /* Get first worksheet */
            const wsname = wb.SheetNames[0];
            const ws = wb.Sheets[wsname];
            /* Convert array of arrays */
            const data = XLSX.utils.sheet_to_json(ws, {
                header: 1
            });
            /* Update state */
            callback(data, make_cols(ws['!ref']));
        }, 1300);



    };
    reader.readAsBinaryString(file);
}
export default {
    exportExcel,
    importExcel
};