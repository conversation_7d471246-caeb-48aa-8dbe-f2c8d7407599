import { shapeTypeOpts } from './common';
import { debounce } from 'lodash';
const singleEntryColumns = (shapeType, handleExpand) => {
    const isLocatePoint = shapeType === 5;
    const isLine = shapeType === 4;
    return [
        {
            title: '基础信息',
            prop: 'resName',
            children: [
                {
                    prop: 'regionName',
                    label: '资源名称',
                    type: 'input',
                    attrs: {
                        maxlength: 50,
                        'show-word-limit': true,
                    }
                },
                {
                    prop: 'city',
                    label: '地市',
                    type: 'selectArea',
                    attrs: {
                        filterable: true,
                        props: {
                            multiple: false,
                            label: 'label',
                            value: 'value',
                            children: 'children',
                        }
                    }
                },
                {
                    prop: 'layerIDs',
                    label: '图层类型',
                    type: 'select',
                    attrs: {
                        multiple: true,
                        'collapse-tags': true,
                    }
                },
                {
                    prop: 'shapeType',
                    label: '轮廓类型',
                    type: 'select',
                },
            ]
        },
        {
            title: '配置信息',
            prop: 'configInfo',
            children: [
                {
                    prop: 'roadLength',
                    label: '线段长度（公里）',
                    type: 'input',
                    attrs: {
                        disabled: true,
                    },
                    isShow: isLine
                },
                {
                    prop: 'roadType',
                    label: '线段类型',
                    type: 'select',
                    isShow: isLine
                },
                {
                    prop: 'expand',
                    label: '线段外扩范围（米）',
                    type: 'inputNumber',
                    attrs: {
                        min: 1,
                        max: 5000,
                    },
                    listeners: {
                        change: debounce(handleExpand, 2000),
                    },
                    isShow: isLine
                },
                {
                    prop: 'regionTypeIDs',
                    label: '地域类型',
                    type: 'selectArea',
                    attrs: {
                        isLastMultiSelect: false,
                        props: {
                            multiple: true,
                            label: 'label',
                            value: 'value',
                            children: 'children',
                            emitPath: false,
                        }
                    },
                    isShow: !isLine
                },
                {
                    prop: 'isIndoor',
                    label: '基站类型',
                    type: 'select',
                    isShow: !isLocatePoint,
                },
                {
                    prop: 'shapeMethod',
                    label: '区域外扩算法',
                    type: 'select',
                    isShow: !isLocatePoint && !isLine,
                },
                {
                    prop: 'describe',
                    label: '资源描述',
                    type: 'input',
                    attrs: {
                        type: 'textarea',
                        rows: 3,
                        maxlength: 500,
                        resize: 'none',
                        'show-word-limit': true,
                    }
                },
            ]
        }
    ];
}

const singleFormRules = (isLine) => {
    return {
        regionName: [{ required: true, message: '必填', trigger: 'blur' }],
        city: [{ type: 'array', required: true, message: '必填', trigger: 'change' }],
        layerIDs: [{ required: true, message: '必填', trigger: 'change' }],
        shapeType: [{ required: true, message: '必填', trigger: 'change' }],
        layerStatus: [{ required: isLine, message: '必填', trigger: 'change' }],
        roadType: [{ required: isLine, message: '必填', trigger: 'change' }],
        expand: [{ required: isLine, message: '必填', trigger: 'blur' }],
    }
};
const singleFormOpts = {
    city: [],
    layerIDs: [],
    shapeType: shapeTypeOpts,
    regionTypeIDs: [],
    isIndoor: [
        { label: '室外', value: 1 },
        { label: '室内', value: 2 },
        { label: '全部', value: 3 },
    ],
    shapeMethod: [
        { label: '区域内识别小区算法', value: 1 },
        { label: '区域外扩识别小区算法', value: 2 },
    ],
    layerStatus: [
        { label: '私有', value: 1 },
        { label: '公开', value: 2 },
    ],
    roadType: [
        { label: '高速', value: 0 },
        { label: '省道', value: 1 },
        { label: '国道', value: 2 },
        { label: '普通铁路', value: 3 },
        { label: '高铁', value: 4 },
    ]
};

const importResultCols = [
    {
        prop: 'regionName',
        label: '资源名称',
    },
    {
        prop: 'regionId',
        label: '资源ID',
        width: 180
    },
    {
        prop: 'row',
        label: '行号',
        width: 60,
    },
    {
        prop: 'rowMsg',
        label: '导入结果',
    },
    {
        prop: 'status',
        label: '状态',
        width: 60,
    },
    {
        prop: 'repeatability',
        label: '重复度',
        width: 66,
        align: 'center',
    },
]
const mapRelationshipCols = [
    {
        prop: 'label',
        label: '资源字段',
        align: 'center',
    },
    {
        prop: 'mapMethod',
        label: '映射方式',
        align: 'center',
    },
    {
        prop: 'mapField',
        label: '映射字段',
    },
];
const mapRelationship = [
    {
        resourceField: 'regionName',
        label: '资源名称',
        mapMethod: 2,
        mapField: null,
        required: true,
        selectField: true,
    },
    {
        resourceField: 'city',
        label: '地市',
        mapMethod: 3,
        mapField: null,
    },
    {
        resourceField: 'layerIDs',
        label: '图层类型',
        mapMethod: 1,
        mapField: [],
        required: true,
        // selectMethod: true,
        selectField: true,
        multiField: true,
    },
    {
        resourceField: 'shapeType',
        label: '轮廓类型',
        mapMethod: 3,
        mapField: null,
    },
    {
        resourceField: 'regionTypeIDs',
        label: '地域类型',
        mapMethod: 1,
        mapField: [],
        // selectMethod: true,
        selectField: true,
        isCascade: true,
    },
    {
        resourceField: 'isIndoor',
        label: '基站类型',
        mapMethod: 1,
        mapField: null,
        // selectMethod: true,
        selectField: true,
    },
    {
        resourceField: 'shapeMethod',
        label: '区域外扩算法',
        mapMethod: 1,
        mapField: null,
        // selectMethod: true,
        selectField: true,
    },
    {
        resourceField: 'describe',
        label: '资源描述',
        mapMethod: 2,
        mapField: null,
        selectField: true,
    },
    {
        resourceField: 'regionCoors',
        label: '区域坐标围栏',
        mapMethod: 3,
        mapField: null,
        required: true,
    },
    {
        resourceField: 'hollowOutRegionCoors',
        label: '空洞坐标围栏',
        mapMethod: 3,
        mapField: null,
    },
]
const mapMethodOpts = [
    { label: '下拉选择', value: 1 },
    { label: '文件匹配', value: 2 },
]
export {
    singleEntryColumns,
    singleFormRules,
    singleFormOpts,
    importResultCols,
    mapRelationshipCols,
    mapRelationship,
    mapMethodOpts,
};