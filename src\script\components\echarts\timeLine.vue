<template>
    <div class="time-charts" :id="name"></div>
</template>

<script>
import * as echarts from 'echarts';
const initEcharts = (myChart, options, type, that) => {
    const option = {
        baseOption: {
            timeline: {
                show: true,
                axisType: 'category',
                autoPlay: true,
                playInterval: 5000,
                realtime: false,
                data: [
                    ...options,
                    // {
                    //     value: '00:00',
                    //     tooltip: {
                    //         formatter: '{b} 7.8级',
                    //         confine: true,
                    //         show: true,
                    //     },
                    //     symbol: 'image://data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACoAAAAqCAYAAADFw8lbAAAAAXNSR0IArs4c6QAABUVJREFUWEfVmF9oW1Ucx09ukqZbE5sgNqUPav+lgbqmbR4iAUFQUTcKGz70ofogDLcxEd+kIG4Tofgm4timDz5o6ZM4kE0ZCoIQzEP6bxaSGhvnQ2k6RlOTuaVpbuR3vb/LLyfnnPzdg4HLPbn3nns+9/v7d86xsf/Jz9YCZyt9RMNUmhm70UFVzzX6DhVYXeh6g8ju89frvYcHkYFJgZtVij6PbdE1tCodGNuia9QLhLCtKAZ9eEgRNAzOw9H/KuAaWBGozKwUBmFV0CJIuEYP0ceIrGEpgzdVkDyc7D9vRh5O9p9C18CqgkKkoGaaHe6J2jAA9qOK6kRJWVvpw7JAkEEiHJyNdjQadS0uLp7o6+s77nK5JjVN6wdaXdd3isXi6u7u7s25ubkbsVisaMICKMLSNirNK2t8gAqUmhbBqs7pdHpmcHDwoqZpg6rkret6JpPJXBwZGfnOhERAehb5rwWtSjdo3hrIgYEB58bGxgWv13ue+1gVbyWXy10eHx+/tL29XaoDXBNkslyIalJIu2lu+97e3iWv1/s2Uul377Hila9Y6eZPrLz1l3HZPvQkcx5/gbnOvcG0Jx63PiCXy33m8/kuMMbKJiyeZW5gQMvyIe+LAGkcqVRqJhAIfIlKHlz/gd0/O89YviBW0+NmPVcXWNfJV6xI3tzcfHNsbAzcACDx4H23SlURKK+mBRkOh7vj8XjMbrc/DW8xIF9/h7FKnVJts7Gerz+1YMvl8p+RSCSaSCQeKmCrgksGypscYB2pVOpUIBD4wojqu/fY/rEX5Ury+nrcrPf2j5YbpNPp06Ojo9cZY4cCVWkKqzE9QqPZLZ8ESDB7Pp+/6na7X4OeDz78hD38+HIzMzXW/d55duSDd40+hULhG4/Hc9aERFgABFcANaGN5q/yUVE6MpQ0/dNxcHAQczqdw9D772dnWPl2silQ+7Ege+xXcE3GSqXSH11dXVGiKIWlihouIKvfoCr6pgWq63raZrO5YaC9vhBj9/9pCpT1HGW+3bX/ZKpUCpqmjQhAMQtU5VVVBQJYCxLauq7/boH6Q4wVmgR1H2W+bBXoqAmKasJZWARUoFRRAAbT/9Jh0z9HQEVBZanaFChUlt7e3lOdCKb9/f1vzcoGgHiIcqrUR2lqqjL96urqyVAodKUT6Wltbe3c5OQkpqeWTI81viaYgsHgkfX19Z+dTudT7ST8Uql0Z2Ji4vlkMvmgnWCik5Gq9AR+mkgkTkxPT19rp4QuLy+fCYfDNwSBROu+MupBKGnCN7OAY2dn532/33+mlUlJNpu91t/f/5ECsm7Ch3Fls6YqZb1erzOZTM77/f63mpnmZbPZz4PB4EIul4NpHvVLPoiUJZSC8ua3JiZYpeAcj8dfnZqamkeflWV/8MmVlZWFSCTyPVcy6exJZvbWpnmkYhlzgaGhoe6lpaWXh4eHX/J4PM84HA4/vPnw8DCbz+d/y2Qyt2ZnZ29tbW3BTAlrOQ/IQ9I6LwSVqYollZZWes1aQ3Gq4uQCqw2dLLc0cUZA/ixcK5mqiu7xq1oelAeWrZ1w1mSd21rckQxBl850uYwphl91dmRxx6vKZwIaaPz6Hp+15pGC9Xzby2UKqIKl4Hyb9ntkGxA8qAyWTrBFbRpPjW7niHZJqJ/W7D3Vg+WzAjU1v/56pJtkqAYfvXzQyZbZoqiviWDitzgev4xtaNtRBqvyYVkf0R6oal+0ytzUh3gF+Coou69SXFRJ6ypmdpJuENQDVanbyD0+sEQfIVWxGUVFL27042RQMr9UPt+pQetBtX3/X+2tvEnzA+srAAAAAElFTkSuQmCC',
                    //     symbolSize: 30,
                    // },
                ],
                label: {
                    formatter: function (value) {
                        if (value.length > 8 && value.includes(' ')) {
                            const valueList = value.split(' ');
                            return valueList[0] + '\n' + valueList[1] || '';
                        }
                        return value;
                    },
                },
                lineStyle: {
                    color: 'rgba(0, 0, 0, 0.45)',
                    width: 1,
                },
                currentIndex: 0,
                onChange: that.handleTimelineChange, // 监听时间轴变化
                left: 10,
                top: 15,
                right: 5,
                bottom: 10,
                symbol: 'emptyCircle',
                symbolSize: 5,
                checkpointStyle: {
                    symbol: 'image://data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACoAAAAqCAYAAADFw8lbAAAAAXNSR0IArs4c6QAABVBJREFUWEfVmF9MHEUcx393W+5QoAlC4iECxkIst1flAX0wom0q1xCDMdXGYE0lFxPUqC8mxpggQRJjTHxR459ACDbWaqrGSBrikaa1Nn1QHtDeHjVQIyCCCXcPgMgd3p2dvf1tfzc3s7u3lgcvuczsn5n5zPf3Z2fGA/+Tn2cHON32mbNiserU7YDXa+4F4DyMHZzdc7eQVmrqz5yAiuCc3uPBeSC7a9a+CNRucHxO33MyUYSlUFi3u2e2dTIoD8iuRfeoevhcphgFZXUevKidDFQGh5BWsLzKIgiEoyWaWai2lTnpM1b3GkrysCJokW/yyrHrrKEmrZt+SZW2A0UIhORLEbQMEgFEcOwevV+kqgyUB2CA3lQq9ZHP53vSbQ7i26XT6U/8fn+fAYmgrOTdoCgoMGVRUB2S/WOx2D2qqp4RpDU37DlN0w6GQqEfCCivqhlUsuilJkdQhcFubW2d8Pv9D7kho21SqdTp8vLyowCQ4UBFLiBNMzR4dEDjr0SjUbWzs/M7AFByOYD7TgFcXLHHvjcAcOEIgCcvTWZycvKBcDisEVAKzGeDkkAZsP5fX19/v7Ky8ggb8dzvAAe+sgc9exhg/6359zY2Nk5VVVU9Z0AyQPxLg8oqmEzfREAsR0dHb+/t7f3e4/GUsYEPfQ0QXZDDhhsBvn3EiJBcbntsbKwjEon8CgD/cLCuQdlETCWN+i5Wrq6uvllTU3OMDT/1J8Ddn8tBf3wcoP3m/PNEInG8trb2FQJJYW1BMdr5qKeBxIB1SFb29/ffMjg4yFS9gTV67DTAl1eKYR/dA/CFEXq5XO7vgYGBjqGhoT8M0JIVtQNlwAhqwi4vL78aCASeZY1nkgD7TgBkyFda8QBcOgrQelN+AisrKx/U1dW9QSCtQDGX6qXoW0/9tiA1UUVZPRKJ1A4PD5/3er27WW+9UYCPL19T9am9AGPh/HU2m13r6+vrGBkZSUhA0ew0PWFnQlCR+TFFmWoa0LsWFhZebGhoeIk1+m0N4I7jAOksgM8L8MsxgNv0KQAsLi6+3djY+A4H6cQ/pYpSN6CRT81vukFXV9fu8fHx84qi1LCGL5wDeO9ngOfvBHh3fx4yk8kkuru775+YmFhzY3YKhBLzroCJnw+qAn+dnZ19urm5+TXdD/8CuOtTgJ+eAAhU5Ludm5t7vaWlZcRIRUxFVJKVzNSY7IsSvWiFz8MiNF0x0VRlukFbW9uNU1NTZxVFqWedXE4C7DUCKJPJLLW3tx+Ynp7eFIDSRI9LvYIgEilopSoqi75KU5Vej8fjPa2trW9dC6V8bWZm5uVgMHiSQDI4VNSRmiLTl6qqqfDVtOObn58/U1ZWtgc72d7evtLU1HTwahpLCz6XjtV0AirKABhUCGlea5r2cDAY/BBB4/H4M6qqfkP8kP+u2/qmlelLCSyE1MuKigpvMpmM+ny+fel0+lJ1dfWhzc1NuiqiKlJIulimPmp6Er8Roy4mygCiLEBTmCcWiz2oqupJTdN6QqEQW2RjAueBZd/1kkF51+D3T3SdWrD5W1pa6qmvr/+M27jRLw+FtFXTykdl5mf3abqy2uyhMpgbeTjhSp7sPAsSiJXp7dIVdQN+M4htcYki23naHTw48lGRv/J7fdF+n0+losMG/h4/Mb6PokOyoheMG7JVlkhJ3kqy0xDHajrxUbssQPugJya0HX/ORKNadEgmFMuJj1oFFg/Kv+v01I4/FHNtejtYJ9axUm9HQUXwMh+3ChZbSCcqiAZ2+jUT+amovx0DdTtB15DXY8BSgtGpwv856u38TzZxR6a16/xfi002TnR2dl0AAAAASUVORK5CYII=', // 当前时间点标记的图形
                    symbolSize: 30, // 当前时间点标记的大小
                    label: {
                        show: true, // 显示当前时间点标记的文本
                        textStyle: {
                            color: 'red', // 当前时间点标记文本的颜色
                        },
                    },
                },
            },
            series: [],
            tooltip: {
                show: true,
                confine: true,
                axisPointer: {
                    type: 'shadow',
                    shadowStyle: {
                        color: 'rgba(52, 134, 255, 0.1)',
                    },
                },
                backgroundColor: 'rgba(0, 0, 0, 0.75)',
                borderWidth: 0,
                textStyle: {
                    color: '#fff',
                },
            },
        },
        grid: {
            top: 0,
            bottom: 0,
            left: 0,
        },
        options: [],
    };
    myChart.clear();
    myChart.setOption(option);
};
export default {
    name: 'timeLine',
    props: {
        data: {
            type: Array,
            default: () => [],
        },
        earthquakeData: {
            type: Object,
            default: () => {},
        },
        name: {
            type: String,
            default: 'time-line',
        },
    },
    data() {
        return {
            myChart: null,
        };
    },
    mounted() {
        this.initEchart();
    },
    watch: {
        data: {
            handler(newV) {
                if (newV) {
                    this.initEchart();
                }
            },
            deep: true,
        },
    },
    methods: {
        initEchart() {
            this.$nextTick(() => {
                if (!this.myChart) {
                    this.myChart = echarts.init(document.getElementById(this.name), null, {
                        renderer: 'canvas', // 强制使用canvas渲染
                        devicePixelRatio: 2, // 设置设备像素比为2，提高清晰度
                    });
                }
                initEcharts(this.myChart, this.data, this.data.type, this);
                this.addLister();
            });
        },
        handleTimelineChange(params) {
            const endData = this.data[params.currentIndex];
            this.$emit('timeLineChangeAction', endData);
        },
        removeLister() {
            console.log('移除监听');
            this.myChart.off('timelinechanged', this.handleTimelineChange);
        },
        addLister() {
            //先移除监听，防止重复
            this.myChart.off('timelinechanged', this.handleTimelineChange);
            // 监听时间轴变化
            this.myChart.on('timelinechanged', this.handleTimelineChange);
        },
    },
};
</script>

<style lang="less" scoped>
.time-charts {
    width: 100%;
    height: 100%;
}
</style>
