<template>
  <el-dialog
    class="input-result"
    title="批量录入结果"
    :visible="visible"
    :close-on-click-modal="false"
    append-to-body
    @open="handleOpen"
    @close="handleClose"
  >
    <!-- 主体 -->
    <el-tabs v-model="activeName">
      <el-tab-pane v-for="(tab, inx) in tabList" :label="tab.label" :name="tab.name" :key="inx">
        <div v-if="tab.name === activeName" class="input-result__content">
          <!-- 输出 -->
          <el-table
            :data="tab.tableData"
            :header-cell-style="theadColor"
            :max-height="tab.tableData.length > 8 ? 327 : null"
            header-cell-class-name="custom-cell"
            cell-class-name="custom-cell"
            size="mini"
            border
            @selection-change="handleSelectChange"
          >
            <el-table-column type="selection" :width="35" />
            <el-table-column v-for="item in tab.columns" :key="item.prop" v-bind="item" />
            <el-table-column label="操作">
              <template v-slot="{ row }">
                <el-link type="primary" @click="handleOperate(row, tab.name)">
                  {{ getOperateLabel(tab.name) }}
                </el-link>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-tab-pane>
    </el-tabs>
    <!-- 底部 -->
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" size="mini" @click="sure">批量删除</el-button>
    </div>
  </el-dialog>
</template>
<script>
import { entryResultTabList } from '@/script/constant/resourceCreate.js';
export default {
  name: 'input-result',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    baseInfo: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      activeName: 'success',
      tabList: entryResultTabList,
      selectedList: [],
      theadColor: {
        backgroundColor: '#F6F7FA',
        fontSize: '14px',
        fontWeight: 'normal',
        color: '#4a4a4a',
      },
    };
  },
  computed: {
    curTab() {
      return this.tabList.find((item) => item.name === this.activeName);
    },
  },
  methods: {
    handleOperate() {},
    bulkDelete() {},
    reentry() {},
    handleSelectChange(selectedList) {
      this.selectedList = selectedList;
    },
    getOperateLabel(type) {
      return type === 'success' ? '删除' : '重新录入';
    },
    handleOpen() {},
    handleClose() {
      this.activeName = 'success';
      this.selectedList = [];
      this.$emit('update:visible', false);
    },
    sure() {},
  },
};
</script>
<style lang="less" scoped>
.input-result {
  &__content {
    /deep/ .el-table__header-wrapper .gutter {
      background-color: #f6f7fa;
    }
    .el-table {
      margin-bottom: 10px;
      /deep/ .el-table__body-wrapper {
        overflow-y: auto;
      }
      /deep/ .custom-cell {
        .el-checkbox {
          margin-bottom: 0;
        }
      }
      .level {
        display: inline-block;
        width: 32px;
        height: 20px;
        text-align: center;
        line-height: 20px;
      }
    }
  }
  /deep/ .el-dialog__header {
    padding: 10px 16px;
    border-bottom: 1px solid #ccc;
    .el-dialog__title {
      font-size: 15px;
      font-weight: bold;
    }
    .el-dialog__headerbtn {
      top: 14px;
    }
  }
  /deep/ .el-dialog__body {
    padding: 10px 16px;
  }
  /deep/ .el-dialog__footer {
    padding: 10px 16px;
    border-top: 1px solid #ccc;
  }
}
.bold {
  font-weight: bold;
  color: #454545;
}
</style>
