<template>
  <el-dialog
    class="success-dialog"
    :visible="visible"
    :close-on-click-modal="false"
    width="420px"
    append-to-body
    destroy-on-close
    @close="handleClose"
  >
    <!-- 主体 -->
    <div class="success-dialog__main">
      <img class="w-56" :src="successIcon" />
      <div class="tip">文件批量导入成功</div>
      <div class="explain">
        如需查看文件导入详情请至
        <span class="status" @click="jumpPage"> 资源录入状态 </span>
        页面查看
      </div>
    </div>
  </el-dialog>
</template>
<script>
import successIcon from '@/img/space/icons/success.png';
export default {
  name: 'create-resPackage',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      successIcon,
    };
  },
  methods: {
    jumpPage() {
      this.$router.push({
        path: 'entryStatus',
      });
      this.handleClose();
    },
    handleClose() {
      this.$emit('update:visible', false);
    },
  },
};
</script>
<style lang="less" scoped>
.success-dialog {
  align-content: center;
  &__main {
    display: flex;
    flex-direction: column;
    align-items: center;
    .tip {
      margin: 22px 0 10px 0;
      font-size: 18px;
      font-weight: bold;
    }
    .explain {
      font-size: 16px;
      .status {
        color: #0091ff;
        text-decoration: underline;
        cursor: pointer;
      }
    }
  }
  /deep/ .el-dialog {
    margin-top: 0 !important;
    display: flex;
    flex-direction: column;
    border-radius: 10px;
    background: linear-gradient(180deg, #f2ffec 0%, #ffffff 100%);
  }
  /deep/ .el-dialog__header {
    position: relative;
    .el-dialog__headerbtn {
      top: 14px;
    }
  }
  /deep/ .el-dialog__body {
    padding: 16px;
    padding-bottom: 30px;
    flex: 1;
    height: 0;
  }
}
.w-56 {
  width: 56px;
}
</style>
