{"name": "earthquake", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve --mode development", "build": "vue-cli-service build --mode production", "lint": "vue-cli-service lint", "build-yun": "vue-cli-service build --mode yun", "build-test": "vue-cli-service build --mode test", "build-prodTest": "vue-cli-service build --mode prodTest"}, "dependencies": {"amfe-flexible": "^2.2.1", "axios": "^0.19.0", "core-js": "^3.6.5", "crypto-js": "^4.0.0", "dayjs": "^1.11.11", "echarts": "^5.4.0", "element-ui": "2.15.8", "exceljs": "^4.4.0", "html2canvas": "^1.4.1", "jquery": "^3.7.1", "lodash": "^4.17.15", "mtplat-gis": "^4.2.14", "nprogress": "^0.2.0", "postcss-pxtorem": "^5.1.1", "proj4": "^2.9.1", "vue": "^2.6.11", "vue-router": "^3.2.0", "vuex": "^3.4.0", "xlsx": "^0.18.5", "js-audio-recorder": "0.5.7", "speak-tts": "^2.0.8", "typed.js": "^2.1.0", "marked": "^4.0.7"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.13", "@vue/cli-plugin-eslint": "~4.5.13", "@vue/cli-service": "~4.5.13", "babel-eslint": "^10.1.0", "babel-loader": "^9.1.3", "eslint": "^6.7.2", "eslint-plugin-vue": "^6.2.2", "javascript-obfuscator": "^3.2.7", "compression-webpack-plugin": "^6.1.0", "uglifyjs-webpack-plugin": "^2.2.0", "less": "^3.0.4", "less-loader": "^5.0.0", "style-resources-loader": "^1.4.1", "url-loader": "^1.0.1", "vue-template-compiler": "^2.6.11", "webpack": "^4.29.6", "webpack-obfuscator": "^2.6.0"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}