<template>
    <div ref="myChart" class="mychart-element"></div>
</template>

<script>
import {request} from '@/script/plugin2x/hainan/util/request.js';
import echarts from 'echarts';
import { cityConfig } from '../../common/cityId.js';
const legendInfo = {
	'defaultColor': [
		{ start: 75, color: '#FF736B' },
		{ start: 50, end: 75, color: '#FDCF0F' },
		{ start: 25, end: 50, color: '#49D5E4' },
		{ end: 25, color: '#61B8FF' },
	],
};
export default {
    name:'map-echarts',
    props: {
		isShowReturn: {
			type: Boolean,
			default: false,
		},
		activeChooseName: {
			type: String,
			default: ''
		}
	},
	computed: {
		splitList() {
			return this.legendInfo[this.activeChooseName] || this.legendInfo['defaultColor'];
		}
	},
	data() {
		return {
			mapUrl: '/static_init/echartsMapJson/',
			cityId: 'geometryProvince/51.json',
			country: 'geometryCouties',
			myChart: null,
			cityConfig,
			legendInfo: legendInfo
		};
	},
	mounted() {
	},

	methods: {
		setMap(params) {
			let jsonPath = '';
			if (params.name === '全省' || !params.name) {
				jsonPath = cityConfig[0].cityId + '.json';
			}else if(params.name === '海南'){
				jsonPath = cityConfig[0].cityId +'_full.json';
			} else {
				let tempId = cityConfig.find(item => item.name === params.name).cityId;
				jsonPath = tempId + '.json';
			}
			return request('get', `HaiNanWeb/Static/echartsMapJson/${jsonPath}`).then(res => {
				return res;
			});
		},
		async mapInit(params) {
			let geoJson = {};
			try {
				geoJson = await this.setMap(params);
			} catch (error) {
				this.$popupMessageWindow('获取地图文件出错');
			}
			this.myChart = echarts.init(this.$refs.myChart);
			if (this.myChart) {
				let option = {
					// eslint-disable-next-line no-ternary
					tooltip: params.tooltip ? params.tooltip : {
						trigger: 'item',
						formatter: (item) => {

							if (item.value == null || isNaN(item.value)) {
								return '无数据';
							}
							// let suffix = '分';
							let unit = params.unit || '%';
							return `${item.name}:${item.value}${unit}`;
						},
					},
					geo: 
						{
							// 地理坐标系组件
							map: 'china',
							top:'36%',
							left:'74%',
							roam : false,
							aspectScale:0.65,
							zoom:0.35,
							layoutCenter: ['50%', '50%'],
							itemStyle : {
								normal : {
									areaColor : '#1673c5',
									borderColor : '#36a0fd'
								},
								emphasis : {
									areaColor:'#FF32FF'
								}
							}
						},
					series: [
						{
							type: 'map',
							mapType: 'hainan', // 自定义扩展图表类型
							// eslint-disable-next-line no-ternary
							label: params.label ? params.label : {
								show:true,
                        		position:'top',
								formatter: function (temp) {
									return temp.name + '\n' + temp.value;
								},
							},
							left:'20%',
							roam: true, //是否开启平游或缩放
							scaleLimit: {
								//滚轮缩放的极限控制
								min: 1,
								max: 2,
							},
							aspectScale: 1, //长宽比
							layoutCenter: params.layoutCenter || ['50%', '50%'],
							emphasis:{
								itemStyle:{
									areaColor:'#FF32FF'
								}
							},
							itemStyle: {
								normal: {
									// areaColor: '#091632',
									borderColor: '#fff',
									borderWidth: 1,
									label: {
										show: true,
										color: '#000',
									},
									emphasis: {
										label: {
											show: true,
										},
									},
								},
							},
							data: params.series,
						},
					],
				};
				if(params.name === '海南'){
					const hainanData  = JSON.parse(JSON.stringify(geoJson));
					//将轮廓点数量排序，方便找到需要的轮廓
					hainanData.features[0].geometry.coordinates.sort((a, b) => {
						return b[0].length - a[0].length;
					});
					hainanData.features[0].geometry.coordinates = [[hainanData.features[0].geometry.coordinates[0][0]]];
					echarts.registerMap('hainan', hainanData);
				}else{
					echarts.registerMap('hainan', geoJson);
				}
				if(params.level==='prov'){
					option.series[0].center = [109.844902, 19.0392];
					option.series[0].zoom = 1.5;
					option.series[0].layoutSize = '500%';
				} else { //非显示海南时，将设置的参数恢复默认值
					option.series[0].center = undefined;
					option.series[0].zoom = 1;
					option.series[0].layoutSize = undefined;
				}
				this.myChart.setOption(option, true);
				//防止冒泡
				this.myChart.off('click');
				this.myChart.on('click', (item) => {
					if (params.level === 'prov') {
						if (!this.isShowReturn) {
							this.$emit('areaChange', item.name);
						}
					}
				});
			}
		},
		resize() {
			if (this.myChart) {
				this.myChart.resize();
			}
		},
    }
};
</script>

<style lang="less" scoped>
.mychart-element {
	width: 100%;
	height: 100%;
	flex: 1;
}
</style>