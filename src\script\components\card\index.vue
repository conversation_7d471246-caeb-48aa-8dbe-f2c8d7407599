<template>
    <div class="card">
        <img class="card-title" :src="img" />
        <span class="text">{{title}}</span>
        <div class="card-content">
            <slot></slot>
        </div>
    </div>
</template>

<script>
export default {
    name:'card',
    props:{
        img:{
            type:String,
            default:'keyAreas'
        },
        title:{
            type:String,
            default:''
        }
    }
}
</script>

<style lang="less" scoped>
.card{
    width:100%;
    height:100%;
    background: rgba(23,42,65,0.6);
    box-shadow: 0px 2px 4px 0px rgba(0,0,0,0.65);
    border-radius: 12px;
    backdrop-filter: blur(8px);
    padding:15px;
    &-title{
        width:100%;
        height:42px;
        position:relative;
    }
    .text{
        position:absolute;
        left: 50px;
        top: 23px;
        color: #fff;
        font-size: 16px;
        font-weight: 600;
        text-shadow: 0px 0px 0.22222rem rgba(62, 136, 233, 0.64);
    }
    &-content{
        height:calc(100% - 42px);
        width:100%;
        padding:10px 0px;
    }
}
</style>