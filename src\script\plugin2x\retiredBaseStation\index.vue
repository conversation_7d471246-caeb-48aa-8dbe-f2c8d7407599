<template>
    <div class="retired">
        <div class="title-top">
            <div class="title">行政区域退服基站管理</div>
            <searchBar class="search-form" :fields="formCols" :form="form">
                <template>
                    <el-button
                        class="earth-btn-common"
                        type="primary"
                        size="small"
                        @click="search()"
                        >查询</el-button
                    >
                </template>
            </searchBar>
        </div>
        <div class="content" slot="content">
            <gisMap ref="homeMap" @loaded="loadedMap" />
            <div class="content-left">
                <titleBar :img="require(`../../../img/retired/overview.png`)" title="概览" />
                <el-carousel height="180px">
                    <el-carousel-item v-for="(items, i) in cardList" :key="i">
                        <div class="overview-list">
                            <div
                                class="overview-list-item"
                                v-for="(item, index) in items"
                                :key="index"
                            >
                                <img
                                    :src="require(`../../../img/retired/${item.img}.png`)"
                                    alt=""
                                />
                                <div class="overview-list-item-value">{{ item.count }}</div>
                                <div class="overview-list-item-name">{{ item.name }}</div>
                            </div>
                        </div>
                    </el-carousel-item>
                </el-carousel>
                <titleBar :img="require(`../../../img/retired/trend.png`)" title="趋势分析" />
                <div class="trend-analysis">
                    <div
                        class="trend-analysis-item"
                        v-for="item in trendChartList"
                        :key="item.title"
                    >
                        <titleBar
                            class="sub-title"
                            :img="require(`../../../img/retired/sub-title-bg.png`)"
                            :title="item.title"
                        />
                        <common-charts :name="item.id" :data="item.option"></common-charts>
                    </div>
                </div>
            </div>
            <div class="content-center">
                <div class="legend">
                    <div class="legend-title">图例</div>
                    <div v-for="(item, index) in legend" :key="index" class="legend-item">
                        <img :src="require(`../../../img/retired/${item.img}.png`)" alt="" />
                        <div class="legend-item-name">{{ item.name }}</div>
                    </div>
                </div>
            </div>
            <div class="content-right">
                <titleBar :img="require(`../../../img/retired/station.png`)" title="基站列表">
                    <searchBar
                        slot="right"
                        class="table-search-form"
                        :fields="stationFormCols"
                        :form="stationForm"
                    ></searchBar>
                </titleBar>
                <div class="list">
                    <dataTable
                        class="data-table"
                        :columns="columns"
                        :data="tableData"
                        :pagination="paginationData"
                        :total="total"
                        :updateTable="getTableData"
                    >
                        <!-- 震级 -->
                        <template #status="{ row }">
                            <div
                                v-if="row.status != null"
                                class="row-list"
                                :class="['green','red'][row.status]"
                            >
                                ● {{ ['正常', '退服'][row.status] }}
                            </div>
                        </template>
                        <template #操作="{ row }">
                            <el-popover
                                placement="left"
                                title="标题"
                                width="300"
                                trigger="click"
                                popper-class="station-popper"
                            >
                                <div>
                                    <div class="title">退服详情</div>
                                    <div class="flex-row">
                                        <span class="popper-name">退服总时长：</span>
                                        <span class="popper-value">{{ row.duration }}</span>
                                    </div>
                                </div>
                                <el-button slot="reference" style="color: #53ffff" type="text"
                                    >详情</el-button
                                >
                            </el-popover>
                        </template>
                    </dataTable>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import searchBar from '_com/searchBar/searchBar.vue';
import commonCharts from '_com/echarts/commonCharts.vue';
import gisMap from '_com/gisMap/index.vue';
import initPoint from '_com/gisMap/layer/point.js';
import {
    formCols,
    getPlatformOverview,
    tableColumn,
    stationFormCols,
    getTrendChartList,
} from '_const/retired.js';
import dataTable from '_com/table/dataTable.vue';
import titleBar from '_com/titleBar/index.vue';
import dayjs from 'dayjs';
export default {
    name: 'retiredBaseStation',
    components: {
        searchBar,
        dataTable,
        titleBar,
        commonCharts,
        gisMap,
    },
    data() {
        return {
            form: {
                region: {
                    province: 630000,
                    city: 630100,
                    district: '',
                },
                type: 1,
                queryTime: '',
                status: 3,
            },
            stationForm: {
                status: 3,
            },
            platformOverview: getPlatformOverview(),
            legend: [
                { name: '正常基站', img: 'normal-station' },
                { name: '退服基站', img: 'back-station' },
            ],
            columns: tableColumn,
            tableData: [],
            paginationData: {
                curPage: 1,
                pageSize: 20,
            },
            total: 0,
            trendChartList: getTrendChartList([], []),
        };
    },
    computed: {
        formCols() {
            const isShowQueryTime = this.form.type === 0;
            return formCols(this.getAreas, isShowQueryTime, this.handleDateChange);
        },
        stationFormCols() {
            return stationFormCols();
        },
        regionLevel() {
            const { region } = this.form;
            const { province, city, district } = region;
            return Number(Boolean(province)) + Number(Boolean(city)) + Number(Boolean(district));
        },
        cardList() {
            const preList = this.platformOverview.slice(0, 3);
            const lastList = this.platformOverview.slice(3);
            return [preList, lastList];
        },
    },
    watch: {
        'form.type': {
            handler() {
                this.form.queryTime = '';
            },
        },
        'stationForm.status': {
            handler() {
                this.paginationData.curPage = 1;
                this.getTableData();
            },
        },
    },
    methods: {
        search() {
            this.getOverviewAndStation();
        },
        getOverviewAndStation() {
            const { region, queryTime, status } = this.form;
            const { province, city, district } = region;
            // valid
            if (!province || !city) {
                this.$message.warning('请选择省市');
                return;
            }
            this.stationForm.status = status;
            this.getPost(
                'post',
                'getBaseStationOverview',
                {
                    areaType: 2, // 1: 自定义 2: 行政区
                    regionLevel: this.regionLevel,
                    regionCode: district || city || province,
                    queryTime: queryTime || null,
                    status,
                },
                '退服基站概览',
                (res = {}) => {
                    const { stationTotal, stationOutTrendList = [], stationList = [] } = res;
                    // 概览
                    this.platformOverview = getPlatformOverview(res);
                    // 趋势分析
                    const trendData = [];
                    const stationData = [];
                    for (const { dataTime, number, stationOutTrendTime } of stationOutTrendList) {
                        const time = dayjs(dataTime).format('HH:mm');
                        trendData.push([time, stationOutTrendTime]);
                        stationData.push([time, number]);
                    }
                    this.trendChartList = getTrendChartList(trendData, stationData);
                    // 基站列表
                    this.tableData = stationList.slice(0, 20);
                    this.total = stationTotal;
                    // 渲染基站 todo
                    const withdrawServiceStations = [];
                    const normalBaseStations = [];
                    for (const item of stationList) {
                        const { status, cgiLat, cgiLng } = item;
                        const stations =
                            status === 0 ? normalBaseStations : withdrawServiceStations;
                        stations.push({
                            latLng: {
                                lat: cgiLat,
                                lng: cgiLng,
                            },
                            data: item,
                        });
                    }
                    this.drawBaseStation(normalBaseStations, withdrawServiceStations);
                }
            );
        },
        getTableData(paginationData = {}) {
            const { curPage = 1, pageSize = 20 } = paginationData;
            const { region, queryTime } = this.form;
            const { status } = this.stationForm;
            const { province, city, district } = region;
            // valid
            if (!province || !city) {
                this.$message.warning('请选择省市');
                return;
            }
            let params = {
                areaType: 2, // 1: 自定义 2: 行政区
                regionLevel: this.regionLevel,
                regionCode: district || city || province,
                queryTime: queryTime || null,
                status,
                pageSize,
                currentPage: curPage,
            };
            this.getPost('post', 'getBaseStationPage', params, '退服基站分页查询', (res) => {
                const { stationList = [], totalPageNum = 0 } = res || {};
                this.tableData = stationList;
                this.total = totalPageNum;
            });
        },
        loadedMap(g) {
            this.g = g;
            this.NormalBasePoint = initPoint(g, {
                img: require('../../../img/icon/map_baseStation_normal.png'),
                activeImg: require('../../../img/icon/map_baseStation_normal_active.png'),
                globalSize: 40,
            });
            this.SuspendedBasePoint = initPoint(g, {
                img: require('../../../img/icon/map_baseStation_suspended.png'),
                activeImg: require('../../../img/icon/map_baseStation_suspended_active.png'),
                globalSize: 40,
            });
            //
            this.search();
          /*   this.drawBaseStation(
                [
                    {
                        latLng: {
                            lat: 39.90221813,
                            lng: 116.38753954,
                        },
                        data: {},
                    },
                    {
                        latLng: {
                            lat: 39.90481813,
                            lng: 116.38463754,
                        },
                        data: {},
                    },
                ],
                [
                    {
                        latLng: {
                            lng: 116.38863954,
                            lat: 39.90381313,
                        },
                        data: {},
                    },
                    {
                        latLng: {
                            lng: 116.38762954,
                            lat: 39.90381813,
                        },
                        data: {},
                    },
                ]
            ); */
        },
        // 渲染基站
        drawBaseStation(normalBaseStations = [], withdrawServiceStations = []) {
            this.NormalBasePoint.createPoints(normalBaseStations, true, false);
            this.SuspendedBasePoint.createPoints(withdrawServiceStations, true, false);
        },
        getAreas(regionLevel, parentRegionId) {
            return new Promise((resolve, reject) => {
                this.getPost(
                    'post',
                    'getBaseStationRegion',
                    { regionLevel, parentRegionId },
                    '获取行政区域',
                    (res) => {
                        resolve(res.dataList);
                    },
                    (err) => {
                        this.$message.error(err);
                        reject([]);
                    }
                );
            });
        },
        handleDateChange(val) {
            const date = new Date(val);
            const minutes = date.getMinutes();
            if (minutes % 10 !== 0) {
                this.$message({
                    message: '时间选择的分钟只能是00, 10, 20, 30, 40, 50',
                    type: 'warning',
                });
                this.form.queryTime = '';
            }
        },
    },
};
</script>

<style lang="less" scoped>
.retired {
    width: 100%;
    height: 100%;

    display: flex;
    flex-flow: column;
    overflow: hidden;
}

/deep/ .search-form {
    margin-top: 15px;

    &.common .el-row .el-form-item {
        margin: 0;
    }
}

.table-search-form {
    margin-top: -5px;
}

.content {
    position: relative;
    width: 100%;
    flex: 1;

    display: flex;
    flex-direction: column;
    overflow: hidden;

    /deep/.gis-map #home-gis::before {
        background: transparent;
    }
    &-left {
        position: absolute;
        top: 20px;
        left: 20px;
        display: flex;
        flex-direction: column;
        width: 512px;
        height: calc(100% - 36px);
        background: rgba(23, 42, 65, 0.8);
        z-index: 2;
        padding: 10px;
        border-radius: 10px;
        > img {
            width: 512px;
            z-index: 2;
        }
        .statistics-title {
            width: 100%;
            height: 80px;
            line-height: 80px;
            font-size: 14px;
            position: relative;
            padding-left: 20px;
            color: #fff;
            z-index: 2;
            &::after {
                position: absolute;
                content: '';
                top: 35px;
                left: 5px;
                width: 8px;
                height: 8px;
                background: #53ffff;
                border-radius: 50%;
            }
        }
        .overview-list {
            display: flex;
            justify-content: space-between;
            width: 100%;
            position: static;
            z-index: 2;
            padding: 15px;
            &-item {
                display: flex;
                flex-flow: column;
                align-items: center;
                justify-content: space-between;
                width: 33%;
                height: 135px;
                img {
                    width: 70px;
                    height: 70px;
                }
                &-name {
                    font-weight: normal;
                    font-size: 16px;
                    color: #ffffff;
                    line-height: 24px;
                    text-shadow: 0px 0px 4px rgba(62, 136, 233, 0.64);
                    text-align: right;
                    font-style: normal;
                }
                &-value {
                    font-family: PangMenZhengDao;
                    font-size: 24px;
                    color: #ffffff;
                    line-height: 27px;
                    text-shadow: 0px 0px 4px rgba(62, 136, 233, 0.64);
                    text-align: right;
                    font-style: normal;
                }
            }
        }
        .trend-analysis {
            flex: 1;
            display: flex;
            flex-flow: column;
            /deep/.sub-title {
                height: 28px;
                margin-top: 10px;

                .title-text {
                    font-size: 14px;
                    font-weight: normal;
                    left: 28px;
                    top: 4px;
                }
            }
            .trend-analysis-item {
                flex: 1;
                display: flex;
                flex-flow: column;
                .echart-wrapper {
                    flex: 1;
                }
            }
        }
    }
    &-right {
        position: absolute;
        top: 20px;
        right: 20px;
        display: flex;
        flex-direction: column;
        width: 512px;
        height: calc(100% - 40px);
        z-index: 2;
        background: rgba(23, 42, 65, 0.8);
        padding: 10px;
        border-radius: 10px;
        .list {
            width: 100%;
            height: calc(100% - 42px);
            display: flex;
            flex-direction: column;
            padding-top: 15px;
        }
    }
    &-center {
        position: absolute;
        bottom: 35px;
        right: 552px;
        display: flex;
        z-index: 2;
        .legend {
            width: 120px;
            height: 100px;
            padding: 10px 16px 16px;
            background: rgba(23, 42, 65, 0.6);
            box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.65);
            border-radius: 8px;
            backdrop-filter: blur(8px);
            display: flex;
            flex-flow: column;
            justify-content: space-between;

            .legend-title {
                font-weight: 500;
                font-size: 16px;
                color: #ffffff;
                line-height: 22px;
                text-align: left;
                font-style: normal;
            }
            .legend-item {
                display: flex;

                img {
                    width: 16px;
                    height: 16px;
                    margin-right: 10px;
                }
                .legend-item-name {
                    font-weight: 400;
                    font-size: 14px;
                    color: #ffffff;
                    line-height: 16px;
                    text-align: left;
                    font-style: normal;
                    opacity: 0.6;
                }
            }
            .text-item {
                width: 70px;
                padding-top: 10px;
            }
        }
    }
    .nanhai {
        position: absolute;
        right: 730px;
        bottom: 26px;
        width: 135px;
        height: 205px;
        background: url('../../../img/home/<USER>') no-repeat center center / 100% 100%;
        z-index: 2;
    }
}
.showExpand {
    font-size: 14px;
    color: #3871b3;
    cursor: pointer;
}
.data-table {
    width: 100%;
    height: calc(100% - 100.55px);
    &.shrinkHeight {
        height: calc(100% - 145.1px);
    }
}
/deep/.el-button--primary {
    background-color: #3871b3;
}
.text {
    font-size: 15px;
    color: #ffffff;
    line-height: 22px;
    text-shadow: 0 0 2px #fff, 0 0 2px #fff, 0 0 2px #fff, 0 0 4px #3e88e9,
        0px 0px 2px rgba(62, 136, 233, 0.64);
}
.row-list {
    display: flex;
    align-items: center;
    width: 100%;
    height: 20px;
    border-radius: 2px;
    padding: 0 10px;
    .circle {
        width: 6px;
        height: 6px;
        border-radius: 50%;
        margin-right: 10px;
    }
    .earthquakeLevel-text {
        font-size: 14px;
        margin-right: 5px;
    }
    &.green {
        color: #3df258;
    }
    &.red {
        color: #fe3333;
    }
}
.title-text {
    position: absolute;
    left: 40px;
    top: 11px;
    color: #fff;
    font-size: 16px;
    font-weight: 600;
    text-shadow: 0px 0px 0.22222rem rgba(62, 136, 233, 0.64);
}
.title {
    font-size: @font-size-h1;
    font-weight: bold;
    color: #ffffff;
    line-height: 28px;
    text-shadow: 0px 0px 4px rgba(62, 136, 233, 0.64);
    position: relative;
    &::after {
        position: absolute;
        content: '';
        left: -15px;
        top: 10px;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: #53ffff;
    }
}
.popper-name {
    font-size: 14px;
    color: #9aafc1;
    line-height: 20px;
    padding-top: 10px;
}
.popper-value {
    font-size: 14px;
    color: #ffffff;
    line-height: 20px;
    padding-top: 10px;
}
</style>
<style lang="less">
.station-popper {
    &.el-popover {
        width: 300px;
        height: 100px;
        background: rgba(23, 42, 65, 0.3);
        box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.5), 0px 2px 6px 0px rgba(0, 0, 0, 0.5);
        border-radius: 12px;
        border: 1px solid;
        border-image: linear-gradient(135deg, rgba(176, 188, 255, 1), rgba(95, 107, 168, 1)) 1 1;
        backdrop-filter: blur(32px);
        padding: 20px 30px;
    }

    .el-popover__title {
        display: none;
    }
    .popper__arrow {
        display: none;
    }
}
</style>
