<template>
  <div class="new-earthquake-mask" v-if="dialogVisible" @click.self="null">
    <div class="new-earthquake-container">
      <div class="new-earthquake-header">
        <div class="new-earthquake-header-title">新增地震</div>
        <div class="new-earthquake-header-close" @click="dialogVisible = false">
          <i class="el-icon-close"></i>
        </div>
      </div>
      <div class="new-earthquake-content">
        <searchBar
          class="search-form"
          :fields="formCols"
          :form="form"
        ></searchBar>
        <div class="content-footer">
          <div class="content-footer-btn" @click.stop="dialogVisible = false">取消</div>
          <div class="content-footer-btn confirm-btn" @click.stop="handleConfirm">确定</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import searchBar from "_com/searchBar/searchBar.vue";

export default {
  name: "newEarthquake",
  components: {
    searchBar,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      formCols: [
        {
          element: "el-input",
          prop: "position",
          label: "参考位置",
          labelWidth: '82px',
          attrs: {
            clearable: true,
            placeholder: "请输入",
          },
          span: 24,
        },
        {
          element: "el-input",
          prop: "longitude",
          label: "经度",
          labelWidth: '48px',
          attrs: {
            clearable: true,
            placeholder: "请输入",
          },
          span: 12,
        },
        {
          element: "el-input",
          prop: "latitude",
          label: "纬度",
          labelWidth: '48px',
          attrs: {
            clearable: true,
            placeholder: "请输入",
          },
          span: 12,
        },
        {
          element: "el-input",
          prop: "earthquakeDepthLevel",
          label: "震源深度",
          labelWidth: '82px',
          attrs: {
            clearable: true,
            placeholder: "请输入",
          },
          span: 12,
        },
        {
          element: "el-input",
          prop: "earthquakeScaleLevel",
          label: "地震震级",
          labelWidth: '82px',
          attrs: {
            clearable: true,
            placeholder: "请输入",
          },
          span: 12,
        },
        {
          element: "el-input",
          prop: "earthquakeScaleLevel?",
          label: "地震震级",
          labelWidth: '82px',
          attrs: {
            clearable: true,
            placeholder: "请输入",
          },
          span: 12,
        },
      ],
      form: {
        position: "",
        longitude: "",
        latitude: "",
        earthquakeDepthLevel: "",
        earthquakeScaleLevel: "",
      },
    };
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(value) {
        this.$emit("update:visible", value);
      },
    },
  },
  mounted() {},
  methods: {
    handleConfirm() {
      this.$emit("confirm", this.form);
      this.dialogVisible = false;
    },
  },
};
</script>

<style lang="less" scoped>
.new-earthquake-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}
.new-earthquake-container {
  width: 511px;
  height: fit-content;
  background: #05090e;
  border-radius: 10px;
  border: 1px solid #75a2dd;
  display: flex;
  flex-direction: column;
  gap: 20px;
  position: relative;
  padding: 20px;
  cursor: default;
  .new-earthquake-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    &-title {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: bold;
      font-size: 15px;
      color: #ffffff;
      text-shadow: 0px 3px 6px rgba(255, 255, 255, 0.16);
    }
    &-close {
      user-select: none;
      position: absolute;
      right: 20px;
      top: 20px;
      cursor: pointer;
      i {
        font-size: 17px;
        color: #9dbcdc;
      }
    }
  }
  .new-earthquake-content {
    min-height: 0;
    flex: 1;
    .content-footer {
      display: flex;
      justify-content: flex-end;
      gap: 10px;
      &-btn {
        width: 54px;
        height: 22px;
        border-radius: 4px;
        border: 1px solid #285487;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 11px;
        color: #ffffff;
        cursor: pointer;
        user-select: none;
        &.confirm-btn {
            background: #285487;
        }
      }
    }
  }
}
</style>
