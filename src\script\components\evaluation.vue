<template>
  <div class="evaluation">
    <div class="option">
      <el-checkbox-group v-model="checkItems" class="checkGroup" size="mini">
        <el-checkbox-button
          v-for="item in checkOpts"
          class="custom-btn"
          :key="item.label"
          :label="item.value"
        >
          {{ item.label }}
        </el-checkbox-button>
      </el-checkbox-group>
    </div>
    <div v-show="isShowComment" class="entry">
      <el-input
        type="textarea"
        :rows="1"
        placeholder="请输入内容"
        v-model="textarea"
      >
      </el-input>
    </div>
    <div class="footer">
      <el-button class="gray" type="text" size="small" @click="cancel">取消</el-button>
      <el-button type="text" size="small" @click="sure">提交</el-button>
    </div>
  </div>
</template>
<script>
export default {
  name: 'evaluation',
  props: {
    type: {
      type: String,
      default: 'good',
    },
  },
  data() {
    return {
      textarea: '',
      checkItems: [],
    };
  },
  computed: {
    checkOpts() {
      if (this.type === 'good') {
        return [
          { label: '区域正确', value: 1 },
          { label: '区域经常使用', value: 2 },
          { label: '区域重要', value: 3 },
          { label: '写评价', value: 4 },
        ]
      }
      return [
        { label: '区域有误', value: 1 },
        { label: '区域绘制过大', value: 2 },
        { label: '区域绘制过小', value: 3 },
        { label: '写评价', value: 4 }
      ]
    },
    isShowComment() {
      return this.checkItems.includes(4);
    },
  },
  methods: {
    cancel() {
      this.$emit('close');
    },
    sure() {
      this.$emit('submit', {
        checkTypes: this.checkItems,
        evaluation: this.textarea,
        isShowComment: this.isShowComment,
      });
    }
  }
};
</script>
<style lang="less" scoped>
.evaluation {
  padding: 10px 4px 0 10px;
  .entry {
    margin-top: 10px;
  }
  .checkGroup {
    display: flex;
    justify-content: space-between;
    .custom-btn {
      margin: 0;
      padding: 0;
      /deep/ .el-checkbox-button__inner {
        padding: 6px;
        border-left: 0.667px solid rgb(220, 223, 230);
      }
    }
    & .el-checkbox-button.is-checked /deep/ .el-checkbox-button__inner {
      background-color: #e9f2ff;
      color: #000;
    }
    /deep/ .el-checkbox-button__inner {
        border-width: 1px;
    }
  }

  .footer {
    margin-top: 4px;
    text-align: right;
  }
}
.gray {
  color: rgba(0, 0, 0, 0.45);
}
</style>
