<!-- 数据表格 -->
<template>
    <div class="data-table" ref="tables">
        <el-table
            ref="commonTable"
            :data="tableData"
            :size="size"
            style="width: 100%"
            :max-height="maxHeight"
            :span-method="spanMethod"
            :header-cell-style="theadColor"
            :cell-class-name="handleCellStyle"
            :row-class-name="rowClassName"
            :row-key="rowKey"
            :border="border"
            @row-click="handleRowClick"
            @cell-click="cellClick"
            @select-all="handleSelectAll"
            @selection-change="handleSelectChange"
        >
            <el-table-column v-if="isSelectable" type="selection" reserve-selection width="36" />
            <el-table-column
                v-for="item in columns"
                v-bind="item"
                v-slot="{ row, $index }"
                :key="item.prop"
            >
                <slot :name="item.prop" :row="row" :inx="$index">
                    {{ row[item.prop] }}
                </slot>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <div
            v-if="isNeedPagination && paginationData.totalCount > pageSizes[0]"
            class="wrap-pagination"
        >
            <el-pagination
                class="pagination"
                :current-page="paginationData.curPage"
                :page-sizes="pageSizes"
                :page-size="paginationData.pageSize"
                :total="paginationData.totalCount"
                :layout="layout"
                :pager-count="pagerCount"
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>
    </div>
</template>

<script>
export default {
    name: 'data-table',
    props: {
        columns: {
            type: Array,
            default: () => [],
            required: true,
        },
        tableData: {
            type: Array,
            default: () => [],
            required: true,
        },
        isNeedPagination: {
            type: Boolean,
            default: true,
        },
        spanMethod: {
            type: Function,
            default: () => {},
        },
        size: {
            type: String,
            default: 'small',
        },
        border: {
            type: Boolean,
            default: false,
        },
        theadStyle: {
            type: Object,
            default: () => ({}),
        },
        handleCellStyle: {
            type: Function,
            default: () => {},
        },
        paginationData: {
            type: Object,
            default: () => ({
                curPage: 1,
                pageSize: 10,
                totalCount: 0,
            }),
        },
        tableHeight: {
            type: [String, Number],
            default: '100%',
        },
        maxHeight: {
            type: Number,
        },
        layout: {
            type: String,
            default: 'total, prev, pager, next, sizes, jumper',
        },
        rowClassName: {
            type: [String, Function],
            default: '',
        },
        isSelectable: {
            type: Boolean,
            default: false,
        },
        rowKey: {
            type: Function,
            default: () => {},
        },
        pagerCount: {
            type: Number,
            default: 7,
        },
        pageSizes: {
            type: Array,
            default: () => [10, 15, 30, 50],
        },
    },
    data() {
        return {
            // tableHeight:'90%',
        };
    },
    computed: {
        windowHeight() {
            const size = this.$store.state.windowSize;
            const startInx = size.indexOf('x');
            const endInx = size.indexOf('$');
            return Number(size.slice(startInx + 1, endInx === -1 ? 99 : endInx));
        },
        theadColor() {
            return {
                backgroundColor: '#1B293B',
                fontWeight: 'bold',
                color: '#9AAFC1',
                'border-radius': 0,
                boxShadow: 'inset 0px -1px 0px 0px rgba(0,0,0,0.08)',
                borderRadius: '2px 2px 0px 0px',
                fontSize: '14px',
                ...this.theadStyle,
            };
        },
    },
    methods: {
        handleSizeChange(pageSize) {
            this.$emit('updateTable', {
                pageSize,
                curPage: 1,
            });
        },
        handleCurrentChange(curPage) {
            this.$emit('updateTable', { curPage });
        },
        handleRowClick(row, column, event) {
            this.$emit('rowClick', row, column, event);
        },
        cellClick(row, column, cell, event) {
            this.$emit('cellClick', row, column, cell, event);
        },
        handleSelectChange(selection) {
            this.$emit('selectChange', selection);
        },
        handleSelectAll(selection) {
            this.$emit('selectAll', selection);
        },
    },
};
</script>

<style lang="less" scoped>
.data-table {
    height: 100%;
    display: flex;
    flex-flow: column;
    flex-direction: column;
    .el-table {
        flex: 1;
        width: 100%;
        background-color: transparent;
        &::before {
            display: none;
        }
        /*   display: flex;
    flex-direction: column; */
        /deep/ .el-table__row  {
          box-shadow: inset 0px -1px 0px 0px rgba(0, 0, 0, 0.08);
                opacity: 0.7;
                &:nth-child(odd) > td {
                    background: #101d33;
                }
                &:nth-child(even) > td {
                    background: #1b293b;
                }
         &:hover > td {
            background-color: #040a1e !important;
            cursor: pointer;
        } 
        }
        
        /deep/ .success-row {
            .el-table__cell {
                background-color: #040a1e !important;
            }
            &:hover > td {
                background-color: #040a1e !important;
                cursor: pointer;
            }
        }
    }
    /deep/ .el-table__header-wrapper {
        .gutter {
            background-color: #f6f7fa;
        }
    }

    /deep/ .custom-cell {
        padding: 4px 0;
        .cell {
            padding-left: 4px;
            padding-right: 4px;
            line-height: 20px;
        }
    }
    /deep/.el-table__body-wrapper {
        overflow-y: auto;
        &::-webkit-scrollbar {
            width: 3px;
            height: 3px;
        }
        &::-webkit-scrollbar-thumb {
            border-radius: 10px;
            box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
            background: #5c6f92;
        }
        &::-webkit-scrollbar-track {
            /* 滚动条里面轨道 */
            box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
            border-radius: 10px;
            background: transparent;
        }
        &::-webkit-scrollbar-corner {
            background: rgba(0, 0, 0, 0);
        }
        height: calc(100% - 40px);
        .cell {
            font-size: 14px;
        }
        .el-table__body {
            width: 100% !important;
        }
    }
    /deep/ td.el-table__cell,
        /deep/ th.el-table__cell {
            color: #ffffff;
            border-top: none !important;
            border-bottom: none !important;
        }
    .wrap-pagination {
        text-align: right;
        height: 40px;
        .pagination {
            margin: 8px 0;
            padding-right: 0;
            /deep/.btn-prev,
            /deep/ .btn-next {
                color: #fff;
                background: unset;
                border: none;
            }
            /deep/ .el-pager {
                background-color: transparent;
                .number,
                .el-icon {
                    color: #ffffff;
                    background: unset;
                    font-weight: 500;
                    &.active {
                        background: rgba(117, 163, 223, 0);
                        border-radius: 3px;
                        border: 1px solid #a8bfe8;
                    }
                }
            }
            /deep/ .el-pagination__sizes {
                margin-right: 0;
                .el-input__inner {
                    font-size: 12px;
                    color: #fff;
                    background: #1f3459;
                    box-shadow: inset 0px 0px 7px 0px rgba(73, 132, 255, 0.5);
                    border-radius: 3px;
                    border: 1px solid #a8bfe8;
                }
            }
            /deep/ .el-pagination__total {
                color: #fff;
            }
        }
    }
}
</style>
