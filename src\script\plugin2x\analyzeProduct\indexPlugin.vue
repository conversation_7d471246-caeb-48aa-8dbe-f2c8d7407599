<template>
    <div class="analyzeProduct">
        <div class="title-top">
            <div class="title">任务报告</div>
        </div>
        <div class="content" slot="content">
            <searchBar class="search-form" :fields="formCols" :form="form">
                <template>
                    <el-button
                        class="earth-btn-common"
                        type="primary"
                        size="small"
                        @click="search()"
                        >查询</el-button
                    >
                </template>
            </searchBar>
            <dataTable
                class="data-table"
                :columns="columns"
                :data="tableData"
                :pagination="paginationData"
                :total="total"
                :updateTable="getTableData"
            >
                <!-- 时间粒度 -->
                <template #timeType="{ row }">
                    <div v-if="row.timeType != null" :class="`row-list`">
                        <span>{{ timeTypeList[row.timeType] }}</span>
                    </div>
                </template>
                <!-- 任务名称 -->
                <template #taskName="{ row }">
                    <div v-if="row.taskCreateSource === 1 && row.taskName != null" class="row-list">
                        <el-popover
                            title="任务详情"
                            width="320"
                            popper-class="detail-popper"
                            trigger="hover"
                            placement="right"
                        >
                            <div class="earth-overflow-y">
                                <div
                                    class="active-alarm-detail-item"
                                    v-for="(item, index) in detailData"
                                    :key="index"
                                    :style="{width:index === 4 ?'100%':'50%','line-height':'1.43rem'}"
                                >
                                    <span class="earth-bold-text">{{ item.name }}</span>
                                    <span class="earth-default-text">{{ row[item.props] || '-' }}</span>
                                </div>
                            </div>
                            <span slot="reference" class="show-task-name">{{ row.taskName }} <img src="../../../img/icon/icon_question.png" alt=""/></span>
                        </el-popover>
                    </div>
                </template>
                <!-- 操作 -->
                <template #operation="{ row }">
                    <el-button class="btn-item" style="color:#53FFFF" type="text" @click="openDetail(row)">查看报告</el-button>
                    <el-button class="btn-item" style="color:#53FFFF" type="text" @click="downLoad(row)">下载</el-button>
                    <el-button class="btn-item" style="color:#53FFFF" type="text" @click="openStation(row)">基站情况</el-button>
                </template>
            </dataTable>
        </div>
    </div>
</template>

<script>
import searchBar from '_com/searchBar/searchBar.vue';
import dataTable from '_com/table/dataTable.vue';
import { formCols, tableColumns, detailData, timeTypeList,downloadOption } from '_const/analyzeProduct.js';
import {createExcel} from '../../../utils/creatExcel.js';
export default {
    name: 'analyzeProduct',
    components: {
        searchBar,
        dataTable,
    },
    data() {
        return {
            formCols: formCols(),
            form: {
                taskId: '',
                taskName: '',
                earthquakeName: '',
                taskCreateTime: '',
            },
            columns: tableColumns,
            tableData:[
                // {
                //     taskId: 157,
                //     createUserName: 'xiaozhang',
                //     taskName: '上海外滩',
                //     taskStatus: 1,
                //     taskCreateTime: '2008-05-11 14:10:01',
                //     executeStartTime: '2008-05-11 14:30:00',
                //     executeEndTime: '2008-05-20 14:35:00',
                //     earthquakeName: '上海市闽行区',
                //     centerLon: 116.32753954,
                //     centerLat: 39.90221813,
                //     analysisRadius: 2000,
                //     earthquakeLevel: 7.8,
                //     earthquakeDepth: 10,
                //     shapeType: 1,
                //     occurTime: '2024-05-01 12:52:26',
                //     timeType: 1,
                //     analysisStartTime: '2008-05-11 14:30:00',
                //     analysisEndTime: '2008-05-20 15:00:00',
                //     taskNowSustainTime: '2008-05-20 15:00:00',
                //     populationIds: '1,2',
                //     heatMapType: 1,
                //     disasterType: 1,
                //     taskCreateSource: 1,
                // },
                // {
                //     taskId: 357,
                //     createUserName: 'xiaozhang',
                //     taskName: '上海-泥石流',
                //     taskStatus: 1,
                //     taskCreateTime: '2008-05-11 14:10:01',
                //     executeStartTime: '2008-05-11 14:30:00',
                //     executeEndTime: '2008-05-20 14:35:00',
                //     earthquakeName: '上海市闽行区',
                //     analysisRadius: 1000,
                //     earthquakeLevel: 7.8,
                //     earthquakeDepth: 10,
                //     occurTime: '2024-05-01 12:52:26',
                //     shapeType: 2,
                //     regionCoors: '116.32753954,39.90221814;116.32755954,39.90221113;116.33753854,39.90221813;116.32756954,39.90291813',
                //     timeType: 2,
                //     analysisStartTime: '2008-05-11 14:30:00',
                //     analysisEndTime: '2008-05-20 15:00:00',
                //     taskNowSustainTime: '2008-05-20 15:00:00',
                //     populationIds: '1,2',
                //     heatMapType: 1,
                //     disasterType: 1,
                //     taskCreateSource: 2,
                // }
            ],
            paginationData: {
                curPage: 1,
                pageSize: 20,
            },
            total: 0,
            detailData: detailData,
            timeTypeList,
        };
    },
    mounted() {
        this.search();
    },
    methods: {
        search() {
            this.paginationData.curPage = 1;
            this.getTableData({ curPage: 1 });
        },
        async getTableData(paginationData = {}) {
            const { curPage = 1, pageSize = 20 } = paginationData;
            const { taskId, taskName, earthquakeName, taskCreateTime } = this.form;
            let params = {
                pageSize,
                currentPage: curPage,
                taskId,
                taskName,
                taskStatus: '2',
                taskCreateTime,
                burialPepTaskPara: {
                    earthquakeName,
                },
            };
            this.getPost('post', 'getTaskList', params, '获取任务报告列表信息', (res) => {
                this.total = res.totalPageNum ;
                this.tableData = res.detailList;
            });
        },
        openDetail(row) {
            if (row.taskCreateSource === 2) {
                const name = row.heatMapType === 1 ? 'analyzeDetails' : 'resultProduct';
                this.$router.push({
                    name,
                    params: row,
                });
            } else {
                this.$router.push({
                    name: 'analyzeDetails',
                    params: row,
                });
            }
        },
        openStation(row){
            this.$router.push({
                name: 'baseStationSituation',
                params: row,
            });
        },
        downLoad(row){
            this.getPost('post', 'downloadExcel', {
                taskId:row.taskId
            }, 'Excel数据报告下载', (res) => {
                const {analyzeDetail,influencedList,lossContactList} = res;
                const excelData = [];
                if(influencedList.length) {
                    let addRow = []; 
                    for(let i= 0;i < influencedList.length ;i++){
                        const {dataTime,allPopCnt,gridDetail} = influencedList[i];
                        gridDetail.forEach((item,index) => {
                            if(index == 0){
                                addRow.push([dataTime,allPopCnt,item.gridCenterInglat,item.popCnt]);
                            }else{
                                addRow.push(['','',item.gridCenterInglat,item.popCnt]);
                            }
                        })
                        
                    }
                    excelData.push({
                        A1Value:analyzeDetail,
                        addRow:addRow,
                        key:1
                    })
                }
                if(lossContactList.length){
                    let addRow = []; 
                    for(let i= 0;i < lossContactList.length ;i++){
                        const {dataTime,allPopCnt,gridDetail} = lossContactList[i];
                        gridDetail.forEach((item,index) => {
                            if(index == 0){
                                addRow.push([dataTime,allPopCnt,item.gridCenterInglat,item.popCnt]);
                            }else{
                                addRow.push(['','',item.gridCenterInglat,item.popCnt]);
                            }
                        })
                        
                    }
                    excelData.push({
                        A1Value:analyzeDetail,
                        addRow:addRow,
                        key:2
                    })
                }
                const data = downloadOption(excelData);
                createExcel(row.taskName,data);

            });
        }
    },
};
</script>

<style lang="less" scoped>
.analyzeProduct{
    width:100%;
    height:100%;
    padding-bottom:20px;
}
.content {
    width: 100%;
    height: calc(100% - 76px);
    padding:15px;
    display: flex;
    flex-direction: column;
    .data-table {
        height: calc(100% - 46px);
        border-radius: 0.44rem;
    }
}
.row-list {
    display: flex;
    align-items: center;
}
.tooltips-title {
    font-size: 16px;
    margin-bottom: 10px;
}
.show-task-name{
    display:flex;
    align-items:center;
    img{
        padding-left:5px;
    }
}
.earth-overflow-y{
    width:100%;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
}
.btn-item{
    color:#53FFFF;
    position: relative;
    &::after {
        position: absolute;
        right: -13px;
        top: 13px;
        width: 1px;
        background: #D8D8D8;
        height: 11px;
        content: '';
    }
    &:last-child {
        &::after {
            position: absolute;
            right: -13px;
            top: 13px;
            width: 1px;
            background: #D8D8D8;
            height: 0px;
            content: '';
        }
    }
}
.btn-item + .btn-item{
    padding-left:15px;
}
</style>

<style lang="less">
.detail-popper{
    &.el-popover{
        background: rgba(39, 69, 106, 0.8);
        box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.65);
        backdrop-filter: blur(8px);
        border:none;
        color:#fff;
    }

    .el-popover__title{
        display:none;
    }
    .popper__arrow{
        display:none;
    }
}

</style>