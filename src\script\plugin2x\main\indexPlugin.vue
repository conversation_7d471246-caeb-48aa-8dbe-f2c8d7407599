<template>
  <div id="earthquake">
    <main-router v-if="!isHide" @jumpRouter="jumpRouter"></main-router>
    <div class="main-content">
      <keep-alive :exclude="['presetCreation', 'baseStationSituation']">
        <router-view class="page-route"></router-view>
      </keep-alive>
      <!-- 测试机器人 -->
      <copilot 
        v-if="true"
        ref="copilot"
        :location="location"
        :modelOpts="modelOpts"
        :model="model"
        :recommendationList="recommendationList" />
    </div>
  </div>
</template>
<script>
import mainRouter from './components/mainRouter.vue';
import copilot from '_com/copilot/index.vue';
export default {
  components: {
    mainRouter,
    copilot,
  },
  name: 'earthquake',
  data() {
    return {
      isHide:false,
      modelOpts: [
          {
              label: "通义千问",
              value: "Qwen",
              stream: true
          }
      ],
      model: "Qwen",
      recommendationList: [
          "南宁市的停车场数量是多少？",
          "青秀区的停车场数量是多少",
          "南宁青秀山公园的停车场数量是多少？"
      ]
    };
  },
  computed:{
    location() {
      return {
        top: 100,
        left: "auto",
        right: "23%",
        bottom: "auto"
      };
    }
  },
  created(){
    const page = this.hasQueryParam(location.href,'page');
    const outside = this.hasQueryParam(location.href,'outside');
    if(page || outside){
      this.isHide = true;
    }
  },
  methods: {
    jumpRouter(value) {
      this.$router.push({
        name:value
      })
    },
    hasQueryParam(url, key) {
      const data = url.replace('#/','');
      const urlObj = new URL(data);
      return urlObj.searchParams.has(key);
    },
  },
};
</script>
<style lang="less" scoped>
#app{
  width:100%;
  height:100%;
}
#earthquake {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
  background: #101A24;
  display:flex;
  flex-direction:column;
}

.main-content {
  width: 100%;
  flex:1;
  // height: calc(100% - 2.67rem);
  background: #040A1E;
  overflow:hidden;
}
</style>
