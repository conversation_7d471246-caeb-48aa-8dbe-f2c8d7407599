<template>
    <div class="map-legend">
        <div class="map-title">{{title}}</div>
        <template v-if="type==='block'">
            <div  class="legend-item" v-for="(item,index) in legends" :key="index">
                <div class="block" :style="{'background':item.background}"></div>
                <div class="value">{{item.value}}</div>
            </div>
        </template>
        <template v-else>
            <div   class="legend-item" v-for="(item,index) in legends" :key="index"> 
                <img class="img" :src="item.img" alt=""/>
                <div class="value">{{item.value}}</div>
            </div>
        </template>
    </div>
</template>

<script>
export default {
    name: "map-legend",
    props: {
        title: {
            type: String,
            default: "图例"
        },
        type: {
            type: String,
            default: "block"
        },
        legends: {
            type: Array,
            default: () => [
                {background:'#FF3333',value:'5000-10000'},
                {background:'#FFA040',value:'3000-5000'},
                {background:'#FFFF02',value:'1000-3000'},
                {background:'#00BE42',value:'0-1000'},
            ]
        },
    },
    data() {
        return {};
    },
};
</script>
<style lang="less" scoped>
.map-legend{
    background: rgba(23,42,65,0.6);
    box-shadow: 0px 2px 4px 0px rgba(0,0,0,0.65);
    border-radius: 8px;
    padding:10px;
    .map-title{
        font-weight: 500;
        font-size: 14px;
        color: #FFFFFF;
        line-height: 20px;
    }
    .legend-item{
        display:flex;
        align-items:center;
        height:20px;
        .img{
            width: 12px;
            height: 12px;
        }
        .block{
            width: 12px;
            height: 12px;
            border-radius: 2px;
        }
        .value{
            color: #9EA2AA;
            font-size: 14px;
            padding-left:10px;
        }
    }
}
</style>
