<template>
  <div class="select-city">
    <el-select
      v-if="level >= 1"
      v-model="selectedValue.province"
      placeholder="省"
      :clearable="clearable"
      :disabled="disabled"
      @change="handleProvinceChange"
      :loading="loadingProvince"
    >
      <el-option
        v-for="item in provinceOpts"
        :key="item.regionId"
        :label="item.regionName"
        :value="item.regionId"
      />
    </el-select>
    <el-select
     v-if="level >= 2"
      v-model="selectedValue.city"
      placeholder="市"
      :clearable="clearable"
      :disabled="disabled"
      @change="handleCityChange"
      :loading="loadingCity"
    >
      <el-option
        v-for="item in cityOpts"
        :key="item.regionId"
        :label="item.regionName"
        :value="item.regionId"
      />
    </el-select>
    <el-select
      v-if="level >= 3"
      v-model="selectedValue.district"
      placeholder="区县"
      :clearable="clearable"
      :disabled="disabled"
      :loading="loadingDistrict"
    >
      <el-option
        v-for="item in districtOpts"
        :key="item.regionId"
        :label="item.regionName"
        :value="item.regionId"
      />
    </el-select>
  </div>
</template>

<script>
export default {
  name: 'select-city',
  model: {
    prop: 'value',
    event: 'change',
  },
  props: {
    value: {
      type: Object,
      default: () => ({
        province: '',
        city: '',
        district: '',
      }),
    },
    clearable: {
      type: Boolean,
      default: false,
    },
    level: {
      type: Number,
      default: 3,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    // 获取地区数据的函数
    getAreas: {
      type: Function,
      required: true
    }
  },
  data() {
    return {
      provinceData: [],
      cityData: [],
      districtData: [],
      loadingProvince: false,
      loadingCity: false,
      loadingDistrict: false
    };
  },
  computed: {
    selectedValue: {
      get() {
        return this.value;
      },
      set(newValue) {
        this.$emit('change', newValue);
      },
    },
    provinceOpts() {
      return this.provinceData;
    },
    cityOpts() {
      return this.cityData;
    },
    districtOpts() {
      return this.districtData;
    },
  },
  watch: {},
  methods: {
    // 获取地区数据的通用方法
    async fetchRegionData(level, parentId = null) {
      try {
        // 根据不同级别设置加载状态
        if (level === 1) this.loadingProvince = true;
        else if (level === 2) this.loadingCity = true;
        else if (level === 3) this.loadingDistrict = true;
        
        const data = await this.getAreas(level, parentId);
        
        // 根据不同级别设置数据
        if (level === 1) {
          this.provinceData = data;
        } else if (level === 2) {
          this.cityData = data;
        } else if (level === 3) {
          this.districtData = data;
        }
      } catch (error) {
        console.error('获取地区数据失败:', error);
        this.$message.error('获取地区数据失败');
      } finally {
        // 重置加载状态
        if (level === 1) this.loadingProvince = false;
        else if (level === 2) this.loadingCity = false;
        else if (level === 3) this.loadingDistrict = false;
      }
    },
    
    handleProvinceChange() {
      // 清空下级数据
      this.cityData = [];
      this.districtData = [];
      
      this.$emit('change', {
        ...this.selectedValue,
        city: '',
        district: '',
      });
      
      // 如果选择了省，加载市级数据
      if (this.selectedValue.province) {
        this.fetchRegionData(2, this.selectedValue.province);
      }
    },
    
    handleCityChange() {
      // 清空区县数据
      this.districtData = [];
      
      this.$emit('change', {
        ...this.selectedValue,
        district: '',
      });
      
      // 如果选择了市，加载区县数据
      if (this.selectedValue.city) {
        this.fetchRegionData(3, this.selectedValue.city);
      }
    },
  },
  mounted() {
    // 初始加载省级数据
    this.fetchRegionData(1);
    
    // 如果已有初始值，加载对应级别的数据
    if (this.selectedValue.province && this.level >= 2) {
      this.fetchRegionData(2, this.selectedValue.province);
    }
    
    if (this.selectedValue.city && this.level >= 3) {
      this.fetchRegionData(3, this.selectedValue.city);
    }
  },
};
</script>

<style lang="less" scoped>
.select-city {
  display: flex;
  gap: 4px;
}
</style>

