<!-- 区域列表 -->
<template>
  <div class="resource-list">
    <div class="resource-list__head">
      <span class="title"> 资源列表 </span>
    </div>
    <div class="resource-list__body">
      <el-table
        :data="regionList"
        style="width: 100%"
        :max-height="regionList.length > 10 ? 437 : null"
        :header-cell-style="headerStyle"
        :row-class-name="activeRowClassName"
        size="small"
        @row-click="handleRowClk"
      >
        <el-table-column prop="resourceId" label="资源ID" />
        <el-table-column prop="resourceName" label="资源名称" class-name="custom-cell" />
        <el-table-column prop="createTime" label="创建时间" class-name="custom-cell" />
      </el-table>
    </div>
  </div>
</template>
<script>
export default {
  name: 'resourceList',
  props: {
    regionList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      curRow: {},
      headerStyle: {
        background: '#F4F7FC',
        color: '#000',
        fontWeight: 'bold',
      },
    };
  },
  computed: {},
  created() {
    this.setAreaList();
  },
  methods: {
    async setAreaList() {},
    handleRowClk(row) {
      if (row === this.curRow) return;
      this.curRow = row;
      this.$emit('locateToRegion', row);
    },
    activeRowClassName({ row }) {
      if (row === this.curRow) {
        return 'success-row';
      }
      return '';
    },
  },
};
</script>
<style lang="less" scoped>
.resource-list {
  &__head {
    margin: 0 -12px;
    padding: 6px 16px 12px 16px;
    line-height: 22px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.08);
    .title {
      width: 208px;
      height: 22px;
      font-size: 16px;
      font-weight: bold;
      color: rgba(0, 0, 0, 0.85);
    }
  }
  &__body {
    margin-top: 12px;
  }
  .el-table {
    /deep/ .el-table__row:hover > td {
      background-color: transparent;
      cursor: pointer;
    }
    /deep/ .success-row {
      background-color: #bfe3ff;
    }
    /deep/ .custom-cell {
      padding: 4px 0;
      .cell {
        padding-left: 4px;
        padding-right: 4px;
        line-height: 20px;
      }
    }
    /deep/ .el-table__header-wrapper .gutter {
      background-color: #f6f7fa;
    }
  }
}
</style>
