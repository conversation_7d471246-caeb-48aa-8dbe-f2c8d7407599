<template>
    <!-- 问答主体 -->
    <div class="conversations conversations-overflow" ref="content">
        <div
            class="talk-item"
            :class="[`talk-${item.role}`]"
            v-for="(item, index) in talkRecords"
            :key="index"
        >
            <!-- 用户提问 -->
            <span v-if="item.role === 'user'" class="talk-content">{{ item.text }}</span>
            <!-- 机器人回答 -->
            <template v-else>
                <img class="talk-item-icon" src="../img/logo.gif" alt="" />
                <div class="talk-content">
                    <div v-if="item.isDeepThink" class="deepSeek" @click="handleFold">
                        <img class="thinkIcon" src="../img/thinkIcon.png" alt="" />
                        <span v-if="item.textStatus === 'loading'" class="deepSeek-text loader"
                            >深度思考中，请稍候
                        </span>
                        <span v-else class="deepSeek-text">已深度思考</span>
                    </div>
                    <!-- 成功状态 -->
                    <div class="wrap">
                        <div v-if="item.textStatus === 'loading'" class="loader">
                            正在生成中，请稍后
                        </div>
                        <div v-else class="text" v-html="item.text"></div>
                        <div
                            v-if="item.textStatus === 'success'"
                            v-loading="item.status === 'loading'"
                            element-loading-spinner="el-icon-loading"
                            element-loading-background="rgba(0, 0, 0, 0.8)"
                            class="content"
                            :class="{ 'is-loading': item.status === 'loading' }"
                        >
                            <component
                                v-if="item.component"
                                v-show="item.status === 'success'"
                                class="component"
                                :is="item.component.name"
                                v-bind="item.component.bind"
                                v-on="item.component.on"
                            >
                            </component>
                        </div>
                    </div>
                    <!-- 工具集 -->
                    <!-- 推荐问题 -->
                </div>
            </template>
        </div>
    </div>
</template>

<script>
export default {
    name: 'conversations',
    props: {
        talkRecords: {
            type: Array,
            default: () => [],
        },
    },
    components: {},
    data() {
        return {
            isFold: false,
        };
    },
    computed: {},
    mounted() {
        console.log(this.talkRecords);
    },
    beforeDestroy() {},
    methods: {
        // 深度思考折叠
        handleFold() {
            this.isFold = !this.isFold;
            // 获取所有think类名的元素
            const thinkEle = document.querySelector('.think');
            // 遍历设置过渡效果和display样式
            thinkEle.style.height = this.isFold ? '0' : 'auto';
            thinkEle.style.opacity = this.isFold ? '0' : '1';
            thinkEle.style.display = this.isFold ? 'none' : 'block';
        },
    },
};
</script>

<style lang="less" scoped>
.conversations {
    width: 100%;
    height: 100%;
    padding: 40px 20px;

    &-overflow {
        overflow-y: auto;
        box-sizing: border-box;

        &::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }

        &::-webkit-scrollbar-thumb {
            border-radius: 10px;
            box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
            background: #44c7ff;
        }

        &::-webkit-scrollbar-track {
            /* 滚动条里面轨道 */
            box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
            border-radius: 10px;
            background: transparent;
        }

        &::-webkit-scrollbar-corner {
            background: rgba(0, 0, 0, 0);
        }
    }
}

.talk-item {
    display: flex;
    flex-wrap: wrap;

    &-icon {
        width: 31px;
        height: 31px;
    }

    &.talk-user {
        flex-direction: row-reverse;

        .talk-content {
            display: flex;
            flex-wrap: wrap;
            padding: 5px 12px;
            max-width: calc(100% - 32px);
            color: var(--baseTextColor);
            font-size: var(--baseFontSize);
            background: var(--dialogueBgColor);
            border: 1px solid var(--baseBorderColor);
            border-radius: 7px;
            max-width: 100%;
        }
    }
    &.talk-robot {
        .talk-content {
            display: flex;
            flex-wrap: wrap;
            flex: 1;
            color: var(--baseTextColor);
            font-size: var(--baseFontSize);
            padding: 5px 12px;

            .deepSeek {
                position: relative;
                height: 28px;
                line-height: 28px;
                color: var(--baseBorderColor);

                &-text {
                    padding: 0 12px;
                }

                .thinkIcon {
                    position: absolute;
                    left: -6px;
                    top: 50%;
                    transform: translateY(-50%);
                }
            }

            .tool {
                display: flex;
                align-items: center;
                padding: 10px 0;
                width: 100%;

                &-btn {
                    padding-right: 15px;
                    position: relative;

                    &:not(:last-child) {
                        border-right: 1px solid #738094;
                    }
                }
            }

            .tool-btn + .tool-btn {
                padding-left: 15px;
            }
        }
    }
    .wrap {
        flex: 1;
        .text {
            font-size: var(--baseFontSize);
            color: var(--baseTextColor);
            line-height: 22px;
            min-width: 100%;
            position: relative;

            &.text-loading {
            }
        }

        .content {
            margin-top: 10px;
            &.is-loading {
                height: 300px;
                width: 100%;
            }
        }
    }
}

.talk-item + .talk-item {
    margin-top: 10px;
}

.loader {
    font-size: var(--baseFontSize);
    height: 24px;
    position: relative;
    color: var(--baseBorderColor);
}

.loader::after {
    content: '';
    animation: dot-blink 1s linear infinite;
    position: absolute;
    top: -5px;
    left: 170px;
}

@keyframes dot-blink {
    0% {
        content: '.';
    }

    50% {
        content: '..';
    }

    100% {
        content: '...';
    }
}

.recommendedQuestions {
    padding: 0 0 10px 0;
    max-width: 100%;

    &-text {
        font-size: var(--baseFontSize);
        font-weight: bold;
        margin-bottom: 10px;
    }

    &-item {
        border: 1px solid #c2cad3;
        color: var(--baseTextColor);
        border-radius: 7px;
        font-size: 12px;
        padding: 5px 12px;

        &:hover {
            background-color: rgba(0, 0, 0, 0.1);
        }
    }

    &-item + &-item {
        margin-top: 10px;
    }
}

/deep/ .think {
    margin-bottom: 12px;
    padding-left: 12px;
    border-left: 3px solid #405c84;
    color: var(--subTextColor);
    transition: all 0.3s ease-in-out;
}

.blue {
    color: #2390fe;
    cursor: pointer;
}
</style>
