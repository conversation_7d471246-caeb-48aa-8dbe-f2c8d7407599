import activeLocatePointIcon from '@/img/space/gis/activeLocatePoint.png';
import { toPointSequence, getCoordinateExtremum } from '@/utils/method.js';
import { EventBus } from '@/script/common/eventBus.js';

export default () => {
   let mapIdToDetailFn = {};
class Point {
    constructor(parent, latLng, option = {}) {
        this.parent = parent;
        this.latLng = latLng;
        this.mesh = null;
        this.regionName = option.regionName;
        this.regionId = option.regionId;
    }
    static initLayer(g, that) {
        Point.g = g;
        Point.that = that;
        Point.pointLayer = new g.layer();
        Point.pointLayer.visible = true;
        g.gis.scene.add(Point.pointLayer);
        // 位置点点击事件
        g.event.addClick(Point.pointLayer, (data, event) => {
            EventBus.$emit('locatePointClick', data, event);
            if (that.isJustShowLayer) return;
            const i = data.object.name;
            that.layerObj = that.planeObjs[i];
            if (event.button === 2) {
                that.menuList = [ { label: '删除', prop: 'delete' } ];
                that.isShowMenu = true;
            }
        });
        g.event.addDoubleClick(Point.pointLayer, (data) => {
            const obj = data.object;
            const resourceId = obj.name;
            const setCurDetail = mapIdToDetailFn[resourceId];
            setCurDetail && setCurDetail();
        });
        // hove
        let divObj = null;
        g.event.addHover(Point.pointLayer, (data) => {
            if (divObj) return;
            const latLng = g.math.three2world(data.point);
            const regionName = data.object.regionName;
            const tip = regionName || `${latLng.lng},${latLng.lat}`;
            divObj = g.layerList.divLayer.addDiv({
                dom: ` <div style="
                        padding: 0 4px;
                        width:  fit-content;
                        text-align: center;
                        line-height: 24px;
                        background-color: rgba(255, 255, 255, 0.32); /* 半透明背景 */
                        backdrop-filter: blur(10px); /* 模糊背景 */
                        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2); /* 可选，增加阴影效果 */
                        pointer-events: none;
                        transform: translate(-50%, -36px);
                        white-space: nowrap;
                        border-radius: 4px;
                    ">
                        ${tip}
                    </div>`,
                point: latLng,
            });
        }, () => {
            divObj.remove();
            divObj = null;
        });
    }
    static clearAll() {
        Point.pointLayer.removeAll();
    }
    static unbindEvent() {
        Point.g.event.removeClick(Point.pointLayer);
    }
    drawRegion(latLng = this.latLng, i = this.parent.i, img = activeLocatePointIcon, config = {}) {
        const { g } = Point;
        this.latLng = { ...latLng, ht: 20, size: 36, dir: 0, ...config };
        const points = [this.latLng];
        const material = g.meshList.img.getMaterial({ url: img, opacity: 1 });
        points.autoScale = true;
        this.mesh = g.meshList.img.create(points, material);
        this.mesh.name = i;
        this.mesh.regionName = this.regionName;
        this.mesh.regionId = this.regionId;
        Point.pointLayer.add(this.mesh);
    }
    clearPoint() {
        Point.pointLayer.remove(this.mesh);
    }
    toMove(point =  this.latLng) {
        const { g } = Point;
        g.cameraControl.move(point);
        g.gis.needUpdate = true;
    }
    selectMenu(prop) {
        const { that } = Point;
        if (prop === 'delete') {
            this.clearPoint();
            that.planeObjs[this.parent.i] = null;
            that.layerObj = {};
            that.$eventBus.$emit('updateSharpInfo');
        }
    }
}

return class LocatePoint {
    constructor(i, option) {
        this.i = i;
        this.point = new Point(this, null, option);
    }
    static initGisInfo(g, that) {
        if (LocatePoint.isInitialized) {
            return;
        }
        LocatePoint.isInitialized = true;
        Object.assign(LocatePoint, {
            g,
            that,
            i: 1,
            menuList: [{ prop: 'delete', label: '删除' }],
        });
        g.event.onClickNone.addEvent('map', (e) => {
            if (that.isJustShowLayer) return;
            if (LocatePoint.isAllDotting && that.circleType === 'locatePoint') {
                const latLng = g.math.three2world(g.math.rayCaster(0, { e: e.event }).point);
                that.layerObj.drawRegion(latLng);
                LocatePoint.setDottingStatus(false);
                that.$eventBus.$emit('updateSharpInfo');
            }
        });
        Point.initLayer(g, that);
    }
    static setCurOperateItem(curOperateItem) {
        LocatePoint.curOperateItem = curOperateItem;
    }
    static setDottingStatus(status) {
        LocatePoint.isAllDotting = status;
    }
    static gisCenterMove(point) {
        const { g } = LocatePoint;
        g.cameraControl.move(point);
        // g.cameraControl.zoom = 17;
        g.gis.needUpdate = true;
    }
    // 初始化点数据
    static initRegionCoors(latLngList, isClearAll = false) {
        const { g, that, initRegions, gisCenterMove } = LocatePoint;
        if (isClearAll) that.clearAll();
        const { mergeRegions: locations } = initRegions(latLngList, {
            cb: (curSharp, index) => {
                that.planeObjs[index] = curSharp;
            }
        });
        if (locations.length === 1) {
            gisCenterMove(locations[0]);
        } else {
            const extremum = getCoordinateExtremum(locations);
            g.cameraControl.zoomByPoints(extremum, 1);
        }
    }
    static initRegions(latLngList, option = {}) {
        const { row = {}, cb, setCurDetail } = option;
        const { that } = LocatePoint;
        latLngList = latLngList.filter(({ lat, lng }) => lat && lng);
        if (!latLngList || !latLngList.length) {
            that.$message.error('该位置点坐标不存在');
            return;
        }
        const sharps = [];
        latLngList.forEach((latLng, index) => {
            const curSharp = new LocatePoint(index, {
                regionName: row.regionGroupName || row.regionName || row.resourceName,
                regionId: row.regionId,
            });
            LocatePoint.setDottingStatus(true);
            let resourceId;
            if (setCurDetail) {
                resourceId = row.resourceId || row.regionId;
                mapIdToDetailFn[resourceId] = setCurDetail;
            }
            curSharp.drawRegion(latLng, resourceId);

            sharps.push(curSharp);
            cb && cb(curSharp, index);
        });

        return {
            mergeRegions: latLngList,
            sharps,
        };
    }
    static clearAll() {
        if (!LocatePoint.isInitialized) return;
        Point.clearAll();
    }
    static unbindEvent() {
        if (!LocatePoint.isInitialized) return;
        const { g } = LocatePoint;
        g.event.onClickNone.removeEvent('map');
        Point.unbindEvent();
        LocatePoint.isInitialized = false;
    }
    // 外部调用params
    static getParams(allAreas) {
        let regionParams;
        if (allAreas.length === 1) {
            regionParams = {
                isMultiRegion: 0,
                regionCoors: toPointSequence([allAreas[0].point.latLng]),
            }
        } else if (allAreas.length > 1) {
            regionParams = {
                isMultiRegion: 1,
                regionCoors: allAreas
                    .map((area) => toPointSequence([area.point.latLng])).join('|'),
            }
        }
        return regionParams;
    }
    drawRegion(latLng, i) {
        this.point.drawRegion(latLng, i);
    }
    clear() {
        this.point.clearPoint();
    }
    selectMenu(prop) {
        this.point.selectMenu(prop);
    }
};
};
