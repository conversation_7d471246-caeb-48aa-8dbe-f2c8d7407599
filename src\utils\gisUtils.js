
// 添加双击事件回调
export function doubleClick(g, callBack, vm, key = 'boxSelectLayer') {
    g.layerList[key].onDoubleClick.addEvent(callBack.bind(vm));
}
//监听多边形框选
export function addBoxSelectLayerListen(g, callBack, vm) {
    doubleClick(g, callBack, vm, 'boxSelectLayer');
}
// 监听线段框选 回调
export function addLineLayerListen(g, callBack, vm) {
    doubleClick(g, callBack, vm, 'lineLayer');
}
//监听工具箱橡皮擦 回调
export function addClear(g, callBack, vm) {
    g.tool.onClear.addEvent(callBack.bind(vm));
}
// 执行事件
export function runGisFun(g, layer, method) {
    g.layerList[layer][method]();
}
// 清除区域
export function clearLayerAll(g, layerList = [], methods) {
    layerList.forEach((item) => {
        runGisFun(g, item, methods);
    });
}
// 橡皮擦事件 主动清楚图层信息
export function cleanLayerInfor(g, layerType = 'boxSelectLayer') {
    g.layerList[layerType].clearAll();
}

// 处理区域数据
export function handleGeoData(data, type = 'point') {
    const fun = {
        distance: () => {
            let lineLength = data.distance[data.distance.length - 1];
            lineLength = (Number(lineLength) * 1000).toFixed(2) * 1;
            return lineLength;
        },
        point: () => {
            return data.point
                .map((item) => {
                    return parseFloat(item.lng) + ',' + parseFloat(item.lat);
                })
                .join(';');
        },
    };
    return fun[type]();
}

export function handleBoxSelectLayerMode(g, type = 'outMode') {
    g.layerList.boxSelectLayer[type]();
}
// 坐标转换(数据同为经纬度)
export function translatePoint(g, type, coordinate) {
    let newCoordinate = { ...coordinate };
    const mapUtil = g.math;
    switch (type) {
        case 'GCJ02':
            newCoordinate = mapUtil.gcj02_To_Gps84(coordinate);
            break;
        case 'BD09':
            newCoordinate = mapUtil.bd09_To_Gps84(coordinate);
            break;
        default:
            break;
    }
    return newCoordinate;
}

//绘制多边形
export function drawPointBox(g, dataInfo, vm) {
    let dataTemp = dataInfo.map((item) => {
        return [item.lng, item.lat];
    });
    let regularData = g.math.lsRegular(dataTemp);
    let data = [
        {
            ls: regularData,
            ht: 0 * g.HM,
            layers: [
                { maxH: 0, color: 0xff8888 },
                { maxH: 40 * g.HM, color: 0x00cccc },
            ],
        },
    ];
    //设置透明度,需要在创建plane之前设置,之后才会生效;
    //透明度是整体设置的,设置后所有的plane都使用这个透明度
    g.meshList.plane.opacity = 0.5;
    //传入数据创建平面
    let plane = g.meshList.plane.create(data);
    //图层添加模型
    vm.layer.add(plane);
}
// 多边形跳转-自动缩放到刚好能全部展示所有坐标的大小位置
export function goWithPoints(g, points) {
    setTimeout(() => {
        g && g.cameraControl.zoomByPoints(points, 1.5);
    }, 200);
}
//跳转指定坐标
export function goToMove(g, lng, lat) {
    let scaleData = { targetPoint: { lat: lat, lng: lng }, zoom: 11 };
    g.cameraControl.gradualChangeRadius(scaleData);
}
// 创建一个多边形
export function areaEditCreate(g, data) {
    return g.layerList.areaEdit.createArea(data);
}
// 编辑完成
export function areaEditFinishListen(g, callBack, vm, name) {
    g.layerList.areaEdit.onEditFinish.addEvent(callBack.bind(vm), name);
}
export function areaEditChangeListen(g, callBack, vm, name) {
    g.layerList.areaEdit.onEditChange.addEvent(callBack.bind(vm), name);
}

export function three2world(g, points) {
    let list = [];
    points.forEach((item) => {
        const child = g.math.three2world(item);
        list.push(child);
    });
    return list;
}

export const toCoordinate = (pointsList) => {
    const coords = [];
    for (const item of pointsList) {
        const coord = [];
        for (const it of item) {
            coord.push([it.lng, it.lat]);
        }
        if (coord.length) {
            coord.push(coord[0]);
        }
        coords.push(coord);
    }
    return coords;
};

/**
 * 合并多个多边形为一个多边形
 * @param {Array<Array<{lng: number, lat: number}>>} pointsList - 多边形数组，每个多边形由经纬度点组成
 * @returns {Array<Array<number>>|Array<Array<Array<number>>>} - 合并后的多边形坐标数组
 * - 如果输入为空，返回空数组
 * - 如果只有一个多边形，直接返回该多边形的坐标
 * - 如果有多个多边形，返回合并后的多边形坐标
 */
/* export const union = (pointsList) => {
    if (pointsList.length === 0) {
        return [];
    }
    if (pointsList.length === 1) {
        const coords = toCoordinate([pointsList[0]]);
        return coords;
    }
    const coords = toCoordinate(pointsList);
    const polygonList = coords.map((coord) => turf.polygon([coord]));
    const polygons = turf.featureCollection(polygonList);
    const res = turf.union(polygons);
    const coordinates = res?.geometry?.coordinates || [];
    return coordinates.length > 1 ? coordinates.map((item) => item.flat(1)) : coordinates;
};
 */