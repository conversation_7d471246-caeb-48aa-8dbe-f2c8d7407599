
const mapFields = {
    type: {
        1: '单个资源',
        2: '区域组',
    },
    shapeType: { // 轮廓类型
        1: '圆形',
        2: '多边形',
        4: '线段',
        5: '位置点'
    },
    isIndoor: { // 基站类型
        1: '室外',
        2: '室内',
        3: '全部',
    },
    status: ['没小区匹配', '已匹配', '区域重叠异常'], // 区域基站是否匹配完成
    layerStatus: {
        1: '私有',
        2: '开放',
    },
    isValid: ['不起效', '起效'], // 区域是否起效
    shapeMethod: { // 区域外扩算法
        1: '区域内识别小区算法',
        2: '区域外扩识别小区算法',
    },
    resourceSource: {
        1: '个人创建',
        2: '订阅',
    }
};

const shapeTypeOpts = [
    { label: '多边形', value: 2 },
    { label: '圆形', value: 1 },
    // { label: '线段', value: 4 },
    // { label: '位置点', value: 5 },
];
const mapShapeType = {
    1: {
        label: '圆形',
        prop: 'Circular',
    },
    2: {
        label: '多边形',
        prop: 'Polygon',
    },
    4: {
        label: '线段',
        prop: 'line',
    },
    5: {
        label: '位置点',
        prop: 'locatePoint'
    },
};

const menuList = [
    {
        label: '专有资源',
        prop: 'ownedResources',
        icon: require('@/img/space/resSubscribe/ownedRes.png'),
        children: [
            { label: '多边形', prop: 'polygon' },
            { label: '圆形', prop: 'circular' },
            { label: '位置点', prop: 'locatePoint' },
            { label: '线段', prop: 'line' },
        ]
    },
    {
        label: '公共资源',
        prop: 'pubResources',
        icon: require('@/img/space/resSubscribe/pubRes.png'),
        children: [
            { label: '图层', prop: 'layer' },
        ]
    }
];
const APP_ID = '8868b6d5-c134-b292-8c91-6ba013266b0c';

export {
    mapFields,
    shapeTypeOpts,
    menuList,
    mapShapeType,
    APP_ID,
};