## 应急平台

## 目录结构

```bash
|-- public/                     // 静态资源目录
|   |-- favicon.ico             // 页面标签图标
|   |-- index.html              // 单页面入口模板
|
|-- src/                        // 源代码
|   |-- img/                    // 资源目录，用于存放图片资源
|   |-- script/                 // 源代码
|   |-- |-- api/                // 接口存放目录
|   |-- |-- components/         // 组件目录，用于存放Vue组件
|   |-- |-- plugin2x/           // 视图组件目录，用于存放页面级组件
|   |-- |-- constant/           // 常量目录
|   |-- |-- router/             // 路由配置文件
|   |-- |-- store/              // Vuex状态管理
|   |-- |-- mixins/             // 通用混入目录
|   |-- App.vue                 // 应用根组件
|   |-- main.js                 // 应用入口js
|-- |-- style/                  // 通用样式目录
|   |-- |-- acss.less           //通用样式
|   |-- |-- index.less          //入口文件
|   |-- |-- setting.less        //变量设置文件
|-- |-- utils/                  // 通用工具方法目录
|-- .env.development            // 本地环境运行配置文件
|-- .env.production             // 生产环境运行配置文件
|-- .env.test                   // 测试环境运行配置文件
|-- .env.yun                    // 公网环境运行配置文件
|-- .gitignore                  // Git忽略文件列表
|-- babel.config.js             // Babel配置文件，用于将ES6代码转换为ES5
|-- package.json                // 项目依赖配置文件
|-- README.md                   // 项目说明文件
|-- vue.config.js               // Vue CLI项目的配置文件
```

### 运行

```
npm run serve
```

### 测试环境打包

```
npm run build-test
```

### 生产环境打包

```
npm run build
```
### 公网环境打包

```
npm run build-yun
```
### 生产测试环境打包

```
npm run build-prodTest
```