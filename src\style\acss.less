.earth{
    &-flex-row{
        display: flex;
        align-items: center;
    }
    &-flex-row-com{
        display: flex;
        align-items: center;
        justify-content: center;
    }
    &-flex-column{
        display: flex;
        flex-direction:column;
    }
    &-h-full{
        height:100%;
    }
    &-w-full{
        width:100%;
    }
    &-overflow-y{
        overflow-y: auto;
        &::-webkit-scrollbar {
            width: 3px;
            height: 3px;
        }
        &::-webkit-scrollbar-thumb {
            border-radius: 10px;
            box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
            background: #5C6F92;;
        }
        &::-webkit-scrollbar-track {
            /* 滚动条里面轨道 */
            box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
            border-radius: 10px;
            background: transparent;
        }
        &::-webkit-scrollbar-corner{
            background:rgba(0,0,0,0);
        }
    }
    &-dialog{
        .el-dialog{
            display:flex;
            flex-direction:column;
            margin:0 !important;
            position: absolute;
            top:50%;
            left:50%;
            transform: translate(-50%, -50%);
            background: rgba(23,42,65,0.6);
            box-shadow: 0px 2px 4px 0px rgba(0,0,0,0.65);
            border-radius: 12px;
            backdrop-filter: blur(8px);
          }
        .el-dialog__header{
            font-weight:bold;
            text-align: left;
            .el-dialog__title{
                color:#fff;
            }
        }
    }
    &-cursor{
        cursor: pointer;
        &:hover{
            opacity: 0.8;
        }
    }
    &-shadow{
        box-shadow: 0px 0px 5px 1px rgba(13, 59, 128, 0.4);
        cursor: pointer;
        &:hover{
            box-shadow: 0px 0px 5px 3px rgba(13, 59, 128, 0.4);
        }
    }
    &-bold-text{
        font-size:@font-size-h4;
        font-weight:bold;
    }
    &-default-text{
        font-size:@font-size-h4;
    }
    &-text-ellipsis{
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    &-btn-common{
        &.el-button--primary{
            background:@btn-primary-color;
            color:@white;
        }
        &.el-button{
            border-radius: 4px;
            padding: 0px;
            width: 6.56rem;
            height: 32px;
            border:0;
            &:hover{
                opacity: 0.8;
            }
        }
    }
    &-btn-dark{
        &.el-button--primary{
            background:@btn-primary-color;
            color:@white;
        }
        &.el-button{
            color: #fff;
            background-color: rgba(117, 163, 223, 0);
            box-shadow: inset 0px 0px 8px 0px #4984ff;
            border-radius: 4px;
            border: 1px solid #a8bfe8;
            &:hover{
                background-color: transparent;
                opacity: 0.8;
            }
        }
    }
    &-btn-purple{
        &.el-button--primary{
            background:@btn-primary-color;
            color:@white;
        }
        &.el-button{
            color: #fff;
            background-color: #3871b3;
            box-shadow: inset 0px 0px 8px 0px #4984ff;
            border-radius: 4px;
            border: 1px solid #a8bfe8;
            &:hover{
                background-color: transparent;
                opacity: 0.8;
            }
        }
    }
}
.fade-right-enter-active,
.fade-bottom-enter-active,
.fade-left-enter-active,
.fade-top-enter-active,
.fade-right-leave-active,
.fade-bottom-leave-active,
.fade-left-leave-active,
.fade-top-leave-active {
    transition: all 0.5s ease;
}

.fade-top-leave-to,
.fade-top-enter {
    transform: translateY(-100%);
}

.fade-left-leave-to,
.fade-left-enter {
    transform: translateX(-100%);
}

.fade-bottom-leave-to,
.fade-bottom-enter {
    transform: translateY(100%);
}
.fade-right-leave-to,
.fade-right-enter {
    transform: translateX(100%);
}

.title-top{
    width:100%;
    background: linear-gradient( 270deg, #101620 0%, #1B2F4D 100%);
    padding: 20px 40px 20px;
    .title {
        font-size: @font-size-h1;
        font-weight: 600;
        color: #FFFFFF;
        line-height: 38px;
        text-shadow: 0px 0px 4px rgba(62,136,233,0.64);
        position:relative;
        &::after{
            position:absolute;
            content:'';
            left:-20px;
            top:14px;
            width:8px;
            height:8px;
            border-radius:50%;
            background: #53FFFF;
        }
    }
}
.text-shadow{
    text-shadow:0 0 2px #fff, 0 0 2px #fff, 0 0 2px #fff, 0 0 2px #3E88E9,0px 0px 2px rgba(62,136,233,0.64);
}
.flex-row{
    display:flex;
    align-items:center;
}
.flex-column{
    display:flex;
    flex-direction: column;
}
