<!-- 重叠区域 -->
<template>
  <div class="overlaps-area">
    <dataTable
      class="data-table"
      :columns="columns"
      :data="tableData"
      :total="total"
      :row-class-name="activeRowClassName"
      :max-height="500"
      :updateTable="setTableData"
      layout="total, prev, pager, next"
      @row-click="handleRowClk"
    >
      <template #repetition="{ row }"> {{ toPercentage(row.repetition) }} </template>
    </dataTable>
  </div>
</template>

<script>
import dataTable from '@/script/components/dataTableLast.vue';
import { overlapsTableCols } from '@/script/constant/resourceManifest.js';
import { toPercentage, toPointSequence } from '@/utils/method.js';

const config = {
  color: 0xee0a24,
  frameColor: 0xee0a24,
  basal: 1,
  isJustShow: true,
};
export default {
  name: 'overlaps-area',
  components: {
    dataTable,
  },
  inject: ['root'],
  props: {
    planeObjs: {
      type: Array,
      default: () => [],
    },
    row: {
      type: Object,
    },
    isResCreate: {
      type: Boolean,
      default: false,
    },
    isCollapse: {
      type: Boolean,
    },
    curLayer: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      tableData: [],
      allTableData: [],
      total: 0,
      toPercentage,
      curRow: {},
    };
  },
  computed: {
    columns() {
      return overlapsTableCols;
    },
    isOverlapsArea() {
      return this.curLayer.label === '重叠区域';
    },
  },
  watch: {
    row: {
      handler() {
        this.getOverlapAreas();
      },
    },
    isCollapse: {
      handler(newVal) {
        /*     if (this.isOverlapsArea && newVal) {
          this.getOverlapAreas();
        } */
        if (!newVal) {
          this.clearAllSharps();
        }
      },
    },
  },
  created() {
    if (this.isResCreate) {
      this.$eventBus.$on('getOverlapAreas', (form) => {
        this.getOverlapAreas(form);
      });
    } else {
      this.getOverlapAreas();
    }
    this.$watch('isOverlapsArea', (val) => {
      if (!val && this.curRow) {
        (this.curRow.sharps || []).forEach((sharp) => sharp.clear());
      }
    });
  },
  methods: {
    async getOverlapAreas(form) {
      let params;
      if (form) {
        params = this.getResCreateParams(form);
      } else {
        if (!this.isExitOverlapsArea(this.row.shapeType)) return;
        params = this.getParams(this.row);
      }
      const res = await this.$post('overlapValid', params);
      const { repetitionDetailList } = res[0];
      this.allTableData = repetitionDetailList;
      this.setTableData();
    },
    getParams(row) {
      const {
        shapeType,
        regionName,
        resourceName,
        isMultiRegion,
        layerIDs,
        city,
        regionCoors,
        circle,
        multiPolygonList,
      } = row;
      const params = {
        regionName: resourceName || regionName,
        shapeType,
        isMultiRegion: isMultiRegion,
        layerIdList: layerIDs.split('、'),
        city: city,
      };
      switch (shapeType) {
        case 1: // 圆形
          if (params.isMultiRegion) {
            return Object.assign(params, { multiPolygonList });
          }
          return Object.assign(params, {
            centerLon: circle.centerLongitude,
            centerLat: circle.centerLatitude,
            radius: circle.radius,
          });
        case 2: // 多边形
          if (params.isMultiRegion) {
            return Object.assign(params, {
              multiPolygonList,
            });
          }
          return Object.assign(params, { regionCoors });
        case 4: // 线段
          return Object.assign(params, { regionCoors });
      }
    },
    getResCreateParams(form) {
      const { regionName, city, layerIDs, shapeType } = form;
      const planeObjs = this.planeObjs.filter(Boolean);
      const isMultiRegion = Number(planeObjs.length > 1);
      const params = {
        regionName,
        shapeType,
        isMultiRegion,
        layerIdList: layerIDs,
        city: city[city.length - 1],
      };

      switch (shapeType) {
        case 1: // 圆形
          if (isMultiRegion) {
            return Object.assign(params, {
              multiPolygonList: planeObjs.map((item) => {
                const { centerLatitude, centerLongitude, radius } = item.region.circle;
                return {
                  areaType: 1, //形状类型
                  centerX: centerLongitude, //经度
                  centerY: centerLatitude, //纬度
                  radius: radius, //半径
                };
              }),
            });
          }
          const curItem = planeObjs[0].region.circle;
          const { centerLongitude, centerLatitude, radius } = curItem;
          return Object.assign(params, {
            centerLon: centerLongitude,
            centerLat: centerLatitude,
            radius: radius,
          });
        case 2: // 多边形
          if (isMultiRegion) {
            return Object.assign(params, {
              multiPolygonList: planeObjs.map((item) => {
                const points = item.region.points;
                return {
                  areaType: 2,
                  polygon: toPointSequence(points),
                };
              }),
            });
          }
          const points = planeObjs[0].region.points;
          return Object.assign(params, {
            regionCoors: toPointSequence(points),
          });
        case 4: // 线段
          const linePoints = planeObjs[0].lineSegment.points;
          return Object.assign(params, {
            regionCoors: toPointSequence(linePoints),
          });
      }
    },
    setTableData(paginationData = {}) {
      // 条件过滤
      const allTableData = this.allTableData;
      // 设置分页
      this.total = allTableData.length;
      const { curPage = 1, pageSize = 10 } = paginationData;
      let startIndex = Math.max(0, (curPage - 1) * pageSize);
      let endIndex = Math.min(this.total, curPage * pageSize);
      this.tableData = allTableData.slice(startIndex, endIndex);
    },
    handleRowClk(row) {
      if (this.curRow === row) {
        row.isChecked = !row.isChecked;
        this.curRow = row.isChecked ? row : {};
      } else {
        (this.curRow.sharps || []).forEach((sharp) => sharp.clear());
        row.isChecked = true;
        this.curRow = row;
      }

      if (!row.sharps) {
        const { sharps, extremum } = this.root.setRegionGroup([row], { config });
        Object.assign(row, {
          sharps,
          extremum,
        });
      } else {
        const method = row.isChecked ? 'drawRegion' : 'clear';
        row.sharps.forEach((sharp) => sharp[method]());
        if (row.isChecked) {
          this.root.moveCenter(row.extremum);
        }
      }
    },
    activeRowClassName({ row }) {
      if (row === this.curRow) {
        return 'success-row';
      }
      return '';
    },
    isExitOverlapsArea(shapeType) {
      return [1, 2, 4].includes(shapeType);
    },
    clearAllSharps() {
      for (const row of this.allTableData) {
        (row.sharps || []).forEach((sharp) => sharp.clear());
      }
    },
  },
};
</script>

<style lang="less" scoped>
.overlaps-area {
  width: 370px;
  .data-table {
    /deep/ .el-table__row:hover > td {
      background-color: transparent !important;
      cursor: pointer;
    }
  }
}
</style>
