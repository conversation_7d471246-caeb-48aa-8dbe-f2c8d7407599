<template>
    <el-popover
        v-model="visible"
        placement="bottom"
        width="400"
        trigger="click"
        popper-class="area-popover"
    >
        <div class="area-input-box">
            <div class="tip">{{ title }}</div>
            <el-input
                class="area-input"
                type="textarea"
                :rows="2"
                :placeholder="tips.placeholder"
                :autosize="{ minRows: 2, maxRows: 4 }"
                v-model="textarea"
            >
            </el-input>
            <template v-if="isShowEmpty">
                <div class="tip">空洞</div>
                <el-input
                    class="area-input"
                    type="textarea"
                    :rows="2"
                    :placeholder="emptyTip.placeholder"
                    :autosize="{ minRows: 2, maxRows: 4 }"
                    v-model="holeArea"
                >
                </el-input>
            </template>
            <div class="example">
                <div class="title">
                    <i class="el-icon-circle-check success"></i>
                    <span>范例：</span>
                </div>
                <div class="content">{{ emptyTip.example }}</div>
            </div>
            <div class="area-input-footer">
                <el-button class="cancel" size="small" @click="cancel"> 取消 </el-button>
                <span class="split-box"> </span>
                <el-button class="sure" type="primary" size="small" @click="setAreaCoors()"
                    >提交</el-button
                >
            </div>
        </div>
        <div class="btn-box" slot="reference"><img :src="edit" alt="" srcset="" />输入区域坐标</div>
    </el-popover>
</template>

<script>
import edit from '@/img/space/icons/edit.png';
export default {
    name: 'entry-region',
    model: {
        prop: 'value',
        event: 'change',
    },
    props: {
        shapeType: {
            type: Number,
            default: 2,
        },
        value: {
            type: Boolean,
            default: false,
        },
        isSingleRegion: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            textarea: '',
            holeArea: '',
            edit,
        };
    },
    computed: {
        visible: {
            get() {
                return this.value;
            },
            set(newVal) {
                this.$emit('change', newVal);
            },
        },
        tips() {
            switch (this.shapeType) {
                case 2:
                    if (this.isSingleRegion) {
                        return {
                            placeholder: '请输入经纬度（以“,”间隔），至少三个，每个以“;”间隔',
                            example:
                                '131.32643959259033,30.7321228043094;124.326439592593,31.73212280430',
                        };
                    }
                    return {
                        placeholder:
                            '请输入经纬度（以“,”间隔），至少三个，每个以“;”间隔，若要区分不同区域请以“|”间隔',
                        example:
                            '131.32643959259033,30.7321228043094|124.326439592593,31.73212280430',
                    };
                case 1:
                    return {
                        placeholder:
                            '请输入圆心经纬度和半径（m），经纬度以“,”间隔，圆心和半径之间以“;”间隔，若要区分不同区域请以“|”间隔',
                        example: '121.32643959259033,30.7321228043094;580',
                    };
                case 4:
                    return {
                        placeholder: '请输入经纬度（以“,”间隔），至少两个，每个以“;”间隔.',
                        example:
                            '121.32643959259033,30.7321228043094;121.32643959259033,30.7321228043094;',
                    };
                case 5:
                    return {
                        placeholder: '请输入位置点经纬度，以“,”间隔，若要区分不同位置点请以“|”间隔',
                        example: '121.33,30.04|122.32,28.43',
                    };
                default:
                    return {};
            }
        },
        emptyTip() {
            if (this.isSingleRegion) {
                return {
                    placeholder: '请输入经纬度（以“,”间隔），至少三个，每个以“;”间隔',
                    example: '131.32643959259033,30.7321228043094;124.326439592593,31.73212280430',
                };
            }
            return {
                placeholder:
                    '请输入经纬度（以“,”间隔），至少三个，每个以“;”间隔，若要区分不同区域请以“|”间隔',
                example: '131.32643959259033,30.7321228043094|124.326439592593,31.73212280430',
            };
        },
        isLocatePoint() {
            return this.shapeType === 5;
        },
        isLine() {
            return this.shapeType === 4;
        },
        isShowEmpty() {
            return !this.isSingleRegion && [1, 2].includes(this.shapeType);
        },
        title() {
            return this.isLine ? '线路' : '区域';
        },
    },
    methods: {
        cancel() {
            this.$emit('change', false);
        },
        parseCircle(input) {
            const [center, radius] = input.split(';');
            const [longitude, latitude] = center.split(',');
            return {
                centerLongitude: parseFloat(longitude),
                centerLatitude: parseFloat(latitude),
                radius: parseFloat(radius),
            };
        },
        parseLocatePoint(input) {
            const [longitude, latitude] = input.split(',');
            return {
                lng: parseFloat(longitude),
                lat: parseFloat(latitude),
            };
        },
        setAreaCoors() {
            let regionList = this.textarea.split('|') || [];
            const holeList = this.holeArea.split('|') || [];
            if (this.shapeType === 1) {
                regionList = regionList.map((item) => this.parseCircle(item));
            } else if (this.isLocatePoint) {
                regionList = regionList.map((item) => this.parseLocatePoint(item));
            }
            this.$emit('setAreaCoors', regionList, holeList);
        },
        clear() {
            this.textarea = '';
            this.holeArea = '';
        },
    },
};
</script>

<style lang="less" scoped>
.area-input-box {
    .tip {
        color: #ffffff;
        margin: 4px 0;
    }
    .example {
        margin: 12px 0;
        display: flex;
        .content {
            flex: 1;
            color: #666666;
        }
    }
    .area-input-footer {
        text-align: right;
    }
}

.btn-box {
    display: inline-block;
    padding: 0 10px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    color: #fff;
    font-weight: 500;
    font-size: 16px;
    cursor: pointer;
    border-radius: 0 4px 4px 0;
    background-color: #1f3459;
    img {
        width: 24px;
        height: 24px;
        display: inline-block;
        margin-right: 4px;
        vertical-align: middle;
    }
}
.success {
    color: #74c72a;
}
</style>

<style lang="less">
.area-popover {
    background: #1f3459;
    border-radius: 8px;
    border: 1px solid rgba(182, 213, 255, 0.2);
    .area-input {
        .el-textarea__inner {
            background: rgba(0, 20, 46, 0.3);
            box-shadow: inset 0px 0px 8px 0px #4984ff;
            border-radius: 4px;
            border: 1px solid #a8bfe8;
        }
    }
    .area-input-footer {
        text-align: right;
        .cancel {
            color: #fff;
            margin-right: 12px;
            background: rgba(117, 163, 223, 0);
            border-radius: 4px;
            border: 1px solid #a8bfe8;
        }
        .sure {
            background: #3871b3;
            border-radius: 4px;
        }
    }
}
</style>
