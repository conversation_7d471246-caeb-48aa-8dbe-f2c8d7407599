import axios from 'axios';
import Vue from 'vue';
const POST_SUCCESS = '000001';
window.cancelRequest = (urlName) => {
    window.cancelTokenCollect[urlName] &&
        window.cancelTokenCollect[urlName].forEach((item) => {
            item.cancel('中断请求：' + urlName);
        });
};
// axios.defaults.headers.common['User-Agent'] = 'cmit-wznl';
const server = axios.create({
    timeout: 180000,
    baseURL: window.origin + '/'
});

server.interceptors.request.use(
    (config) => {
        config.headers['content-type'] = 'application/json';
        config.data = JSON.stringify({ ...config.data });
        const token = localStorage.getItem('token');
        token && (config.headers.token = token);
        config.agentId && (config.headers['agentId'] = config.agentId);
        return config;
    },
    (err) => {
        Promise.reject(err);
    }
);

server.interceptors.response.use(
    (res) => {
        if (res.status === 200) {
            //判断是否正确返回
            if (res.data.returnCode == POST_SUCCESS || res.data.code === 200) {
                return Promise.resolve(res.data.data);
            }
            return Promise.reject(res.data.returnMsg || res.data.errmsg);
        }
        return Promise.reject(res.data.returnMsg);
    },
    (err) => {
        if (err.response.status === 401) {
            Vue.prototype.$message({ message: '权限已失效，请重新登陆！', type: 'error' });
        } else {
            return Promise.reject(err);
        }
    }
);

export default server;
