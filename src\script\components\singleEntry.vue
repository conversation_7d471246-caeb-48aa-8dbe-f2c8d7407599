<template>
    <el-form ref="singleFormRef" class="single-entry" :model="form" :rules="rules" size="small">
        <div class="wrap" v-for="(item, inx) in formColumns" :key="inx">
            <div class="title">
                <slot :name="item.prop">
                    {{ item.title }}
                </slot>
            </div>
            <div class="content">
                <template v-for="it in item.children">
                    <el-form-item
                        v-show="!('isShow' in it) || it.isShow"
                        :label="it.label"
                        :prop="it.prop"
                        :key="it.prop"
                    >
                        <el-input
                            v-if="it.type === 'input'"
                            v-model="form[it.prop]"
                            v-bind="it.attrs"
                        />
                        <el-select
                            v-else-if="it.type === 'select'"
                            v-model="form[it.prop]"
                            class="w-full"
                            popper-class="custom-select"
                            v-bind="it.attrs"
                        >
                            <el-option
                                v-for="opt in formOpts[it.prop]"
                                :label="opt.label"
                                :value="opt.value"
                                :key="opt.value"
                            />
                        </el-select>
                        <selectDistrict
                            v-else-if="it.type === 'selectArea'"
                            v-model="form[it.prop]"
                            :options="formOpts[it.prop]"
                            v-bind="it.attrs"
                        />
                        <el-input
                            v-else-if="it.type === 'textarea'"
                            v-model="form[it.prop]"
                            v-bind="it.attrs"
                        />
                        <el-input-number
                            class="w-full"
                            v-else-if="it.type === 'inputNumber'"
                            v-model="form[it.prop]"
                            v-bind="it.attrs"
                            v-on="it.listeners"
                        />
                    </el-form-item>
                </template>
            </div>
        </div>
    </el-form>
</template>

<script>
import {
    singleEntryColumns,
    singleFormRules,
    singleFormOpts,
} from '@/script/constant/resourceCreate.js';
import selectDistrict from '@/script/components/selectDistrict.vue';
import { getAreasToCity } from '@/utils/method.js';
export default {
    name: 'single-entry',
    components: {
        selectDistrict,
    },
    props: {
        shapeType: {
            type: Number,
        },
        form: {
            type: Object,
            default: () => ({}),
        },
        handleExpand: {
            type: Function,
        },
    },
    data() {
        return {};
    },
    computed: {
        formColumns() {
            return singleEntryColumns(this.shapeType, this.handleExpand);
        },
        formOpts() {
            const { spaceAreas, spaceLayers, spaceDistricts } = this.$store.getters;
            return {
                ...singleFormOpts,
                city: getAreasToCity(spaceDistricts),
                regionTypeIDs: spaceAreas,
                layerIDs: spaceLayers,
            };
        },
        isLine() {
            return this.shapeType === 4;
        },
        rules() {
            return singleFormRules(this.isLine);
        },
    },
    methods: {
        async validate() {
            try {
                return await this.$refs.singleFormRef.validate();
            } catch (err) {
                return err;
            }
        },
    },
};
</script>

<style lang="less" scoped>
.single-entry {
    .wrap {
        margin-top: 16px;

        .content {
            padding: 0 4px;

            .el-form-item {
                display: flex;
                flex-direction: column;
                margin-bottom: 2px;

                /deep/ .el-form-item__label {
                    margin-bottom: 0;
                    padding-right: 0px;
                    width: 100% !important;
                    text-align: left;
                    color: #dfe1ec;
                }
                /deep/ .el-form-item__content {
                    .el-input__count .el-input__count-inner,
                    .el-input__count {
                        background: transparent;
                    }
                    .el-input__inner,
                    .el-textarea__inner {
                        color: #fff;
                        background: rgba(117, 163, 223, 0);
                        box-shadow: inset 0px 0px 8px 0px #4984ff;
                        border-radius: 2px;
                        border: 1px solid #a8bfe8;
                    }
                    .el-cascader__search-input {
                        color: #fff;
                        background: unset;
                    }
                    .el-form-item__error {
                        left: auto;
                        right: 0;
                    }
                }
            }
        }
    }
    .title {
        position: relative;
        padding-left: 20px;
        line-height: 38px;
        font-weight: bold;
        font-size: 15px;
        font-weight: bold;
        color: #fff;
        background: url(../../img/space/resCreate/baseInfoBg.png);
        background-size: 100% 100%; // 新增这行，使背景图片完全填充容器
    }
}

.w-full {
    width: 100%;
}
</style>
<style lang="less">
.custom-select {
    background-color: #101d34;
    border-color: #a8bfe8;
    .el-select-dropdown__item {
        color: #fff;
    }
    .el-select-dropdown__item:hover {
        color: #409eff;
    }
    .el-select-dropdown__item.hover {
        color: #409eff;
    }
    &.is-multiple .el-select-dropdown__item.selected {
        background: rgba(0, 0, 0, 0.5);
    }
}
</style>
