<template>
  <div class="bulk-entry">
    <div class="bulk-entry__upload">
      <div class="title">步骤一：上传文件</div>
      <div class="download">
        <i class="el-icon-download blue"></i>
        <span class="blue" @click="uploadInfo.download">{{ uploadInfo.downloadText }}</span>
      </div>
      <el-upload
        ref="uploadRef"
        class="upload"
        action=""
        :accept="uploadInfo.accept"
        :before-upload="handleBefore"
        :on-success="handleSuccess"
        :on-remove="handleRemove"
        :file-list="fileList"
        :http-request="handleRequest"
        auto-upload
        drag
      >
        <img :src="uploadImg" alt="上传" />
        <div class="text"><span class="blue">上传</span>批量录入文件</div>
        <div class="divider"></div>
      </el-upload>
      <div v-if="!fileList.length" class="warning-tip">
        <i class="el-icon-warning"></i>
        请上传文件！
      </div>
      <div class="explain">{{ uploadInfo.explain }}</div>
    </div>
    <div v-show="isComplexFile" class="bulk-entry__map">
      <div class="title">
        <span>步骤二：{{ additionInfo.title }}</span>
        <el-tooltip :content="additionInfo.tip" placement="top">
          <i class="el-icon-question"></i>
        </el-tooltip>
      </div>
      <div class="content">
        <div v-if="isComplexFile" class="field-map">
          <el-button
            type="warning"
            :disabled="isDisabled"
            :loading="loading"
            size="small"
            @click="setMapFields()"
          >
            字段映射 <i class="el-icon-right"></i>
          </el-button>
          <el-button
            v-if="additionInfo.mapRelationship.length"
            type="text"
            @click="setMapFields(true)"
            >查看字段映射详情</el-button
          >
        </div>
        <el-switch
          v-else
          v-model="additionInfo.status"
          class="switch"
          inactive-text="大文件模式："
        />
      </div>
    </div>
    <!-- 字段映射弹窗 -->
    <setMapConnect
      v-if="isShowMapFieldDialog"
      :visible.sync="isShowMapFieldDialog"
      :mapFields="localNames"
      :readonly="readonly"
      @updateMapFields="updateMapFields"
    />
  </div>
</template>

<script>
import uploadImg from '@/img/space/icons/upload.png';
export default {
  name: 'upload-file',
  components: {
    setMapConnect: () => import('./setMapConnect.vue'),
  },
  props: {
    type: {
      type: String,
    },
    uploadInfo: {
      type: Object,
    },
    additionInfo: {
      type: Object,
    },
  },
  data() {
    return {
      uploadImg,
      uploadUrl: '',
      file: {},
      fileList: [],
      isDisabled: true,
      loading: false,
      isShowMapFieldDialog: false,
      fileId: '',
      localNames: [],
      readonly: false,
    };
  },
  computed: {
    isShp() {
      return this.type === 'Shp';
    },
    isGeoJson() {
      return this.type === 'GeoJson';
    },
    isNormalFile() {
      return ['Excel', 'CSV'].includes(this.type);
    },
    isComplexFile() {
      return ['GeoJson', 'Shp'].includes(this.type);
    },
  },
  methods: {
    handleRequest(option) {
       option.onSuccess({}, option.file);
    },
    handleBefore(file) {
      // 校验
      const warnMsg = this.uploadInfo.validator(file.name);
      if (warnMsg) {
        this.$message.warning(warnMsg);
        return false;
      }
    },
    handleSuccess(res, file) {
      this.uploadInfo.file = file;
      this.fileList = [file];
      if (this.additionInfo.readFile) {
        this.updateMapFields([]);
        this.parseFile(file);
      }
    },
    handleRemove() {
      this.fileList = [];
      this.localNames = [];
    },
    parseFile(file) {
      const params = new FormData();
      params.append('file', file.raw);
      this.loading = true;
      this.additionInfo
        .readFile(params)
        .then((res) => {
          const { localNames = [], fileName, fileId } = res || {};
          Object.assign(this.uploadInfo.file, {
            fileName,
            fileId,
          });
          this.localNames = localNames;
          this.isDisabled = false;
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
        });
    },
    updateMapFields(tableData) {
      this.additionInfo.mapRelationship = tableData;
    },
    setMapFields(readonly = false) {
      this.readonly = readonly;
      this.isShowMapFieldDialog = true;
    },
  },
};
</script>

<style lang="less" scoped>
.bulk-entry {
  &__upload {
    padding: 4px 0 12px 0;
    .download {
      margin: 10px 0;
      font-family: AlibabaPuHuiTi, AlibabaPuHuiTi;
      font-weight: 400;
      font-size: 14px;
      color: #0091ff;
      line-height: 14px;
      font-style: normal;
      text-decoration-line: underline;
    }
    .upload {
      padding: 0 12px 16px 12px;
      text-align: center;
      border-radius: 4px;
      border: 1px dashed rgba(0, 0, 0, 0.15);
      /deep/ .el-upload {
        width: 100%;
        .el-upload__input {
          display: none;
        }
        .el-upload-dragger {
          width: 100%;
          height: auto;
          border: none;
        }
      }
      /deep/ .el-upload-list {
        max-width: 242.67px;
        .el-upload-list__item {
          margin-top: 6px;
          border: 1px solid #ccc;
        }
        .el-upload-list__item-name {
          margin-right: 20px;
          text-align: left;
          color: #aaa;
        }
      }
      .text {
        font-size: 14px;
        font-weight: bold;
        letter-spacing: 1px;
      }
      .divider {
        margin: 4px 0 6px 0;
        height: 1px;
        border: 1px dashed rgba(0, 0, 0, 0.15);
      }
    }
    .warning-tip {
      margin-top: 8px;
      font-family: AlibabaPuHuiTi, AlibabaPuHuiTi;
      font-weight: 400;
      font-size: 14px;
      color: #ee0a24;
    }
    .explain {
      margin-top: 6px;
      font-family: AlibabaPuHuiTi, AlibabaPuHuiTi;
      font-weight: 400;
      font-size: 12px;
      color: #aaa;
    }
  }
  &__map {
    margin-top: 20px;
    .switch {
      margin-top: 10px;
      /deep/ .el-switch__label {
        &.is-active {
          font-weight: 400;
          font-size: 14px;
          // color: #333333;
          font-style: normal;
          font-family: AlibabaPuHuiTi, AlibabaPuHuiTi;
        }
      }
    }
    .field-map {
      margin-top: 10px;
    }
  }
  .title {
    display: flex;
    align-items: center;
    font-weight: bold;
    font-size: 16px;
    // color: #333333;
    color: #ccc;
    text-align: left;
    font-style: normal;
    font-family: AlibabaPuHuiTi, AlibabaPuHuiTi;
    &::before {
      content: '';
      display: inline-block;
      margin-right: 8px;
      width: 4px;
      height: 14px;
      vertical-align: middle;
      background: #0091ff;
      border-radius: 1px;
    }
    .el-icon-question {
      margin-left: 4px;
      font-size: 18px;
      color: #9c9c9c;
      color: #666
    }
  }
}
.blue {
  color: #0091ff;
  cursor: pointer;
  text-decoration: underline;
  font-weight: 500;
}
</style>
