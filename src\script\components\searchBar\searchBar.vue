<template>
  <el-form
    ref="searchBarRef"
    :class="['common',type]"
    :model="form"
    v-bind="{
      size: 'small',
      'label-width': '4.75rem',
      inline: true,
      ...$attrs,
    }"
    v-on="$listeners"
  >
    <el-row :gutter="gutter">
      <template
        v-for="(
          {
            prop,
            label,
            element,
            attrs,
            listeners,
            slot,
            rules,
            size,
            labelWidth,
            itemClassName,
            span = 24,
            layout,
            isShow = true,
            isFull = true,
            isDisabled,
			      rightSlot,
            isTransparent = !prop && !element,
          },
          index
        ) in fields"
      >
        <el-col
          v-show="isShow"
          :key="prop || index"
          :span="span"
          v-bind="layout"
        >
          <el-form-item
            :class="[
              itemClassName,
              {isTransparent},
              {isDisabled},
            ]"
            v-bind="{
              label,
              prop,
              rules,
              size,
              labelWidth,
            }"
          >
            <template #error="{ error }">
              <span class="custom-error">
                {{ error }}
              </span>
            </template>
            <!-- 组件 -->
            <component
              v-if="element"
              :is="element"
              :class="{ 'w-full': isFull }"
              v-model="form[prop]"
              v-bind="attrs"
              v-on="listeners"
            >
              <!-- 基础类型 -->
              <template v-if="isDetermineType(slot)">{{ slot }}</template>
              <!-- 引用类型 -->
              <template v-else>
                <component
                  v-for="(it, inx) in processSlot(slot)"
                  :is="slot.element || it.element"
                  :key="inx"
                  v-bind="it"
                  v-on="it.listeners"
                >
                  <template v-if="it.text">{{ it.text }}</template>
                </component>
              </template>
            </component>
            <!-- 自定义插槽/默认插槽 -->
            <template v-else>
              <slot :name="prop || 'default'"></slot>
            </template>
			<template v-if="rightSlot">
				<slot :name="rightSlot"></slot>
			</template>
          </el-form-item>
        </el-col>
      </template>
    </el-row>
  </el-form>
</template>
<script>
export default {
  name: "search-bar",
  inheritAttrs: false,
  props: {
    fields: {
      type: Array,
      default: () => [],
      validator(value) {
        return Array.isArray(value) && value.length;
      },
    },
    form: {
      type: Object,
      validator(value) {
        return value instanceof Object && Object.keys(value).length;
      },
    },
    gutter: {
      type: Number,
      default: 12,
    },
	type:{
		type:String,
		default:'searchBar'
		// 查询控件：searchBar
		// form表单：normalForm
		// 标题、内容换行表单：tCWrapForm
	}
  },
  methods: {
    async validForm() {
      try {
        return await this.$refs.searchBarRef.validate();
      } catch (err) {
        return err;
      }
    },
    // 基础数据类型
    isDetermineType(value) {
      return value === null || !["object", "function"].includes(typeof value);
    },
    isObject(type) {
      return Object.prototype.toString.call(type) === "[object Object]";
    },
    processSlot(slot) {
      switch (true) {
        case Array.isArray(slot):
          return slot;
        case this.isObject(slot) && slot.element && Array.isArray(slot.enums):
          slot.enums.element = slot.element;
          return slot.enums;
        case this.isObject(slot):
          return [slot];
        default:
          return [];
      }
    },
  },
  async deactivated() {
    if (this.$listeners.validate) {
      const res = await this.validForm();
      this.$emit("validate", res);
    }
  },
};
</script>
<style lang="less" scoped>
.common {
  width: 100%;
  .el-row {
    display: flex;
    flex-flow: wrap;
    .el-col:last-of-type .el-form-item {
      margin-right: 0;
    }
    .el-form-item {
      display: flex;
      margin: 0 0 15px 0;
      background-color: transparent;
      .custom-error {
        position: absolute;
        right: 0;
        top: -26px;
        padding: 0 6px;
        height: 20px;
        line-height: 20px;
        border-radius: 2px;
        font-size: 12px;
        color: #fff;
        background-color: #f56c6c;
        z-index: 999;
        &::before {
          content: "";
          position: absolute;
          margin-top: -5px;
          width: 10px;
          height: 8px;
          right: 50%;
          bottom: -3px;
          transform: translateX(50%) rotate(45deg);
          background-color: #f56c6c;
          z-index: -1;
        }
      }
      &.isTransparent {
        border: none;
        background-color: transparent;
        box-shadow: none;
      }
      &.isDisabled {
        /deep/ .el-form-item__label {
          background-color: #f5f7fa;
        }
      }
      /deep/ .el-form-item__label {
        position: relative;
        margin-bottom: 0;
        padding-right: 10px;
        // font-size: 0.78rem;
        color:#fff;
      }
      /deep/ .el-form-item__content {
        position: relative;
        display: flex;
        align-items: center;
        flex: 1;
        .el-input__inner {
          border-radius: 0;
          background: rgba(117, 163, 223, 0);
          color: #DFE1EC;
        }
      }
    }
  }
}
.searchBar{
	.el-form-item{
    background: rgba(117, 163, 223, 0);
    box-shadow: inset 0px 0px 8px 0px #4984FF;
    border-radius: 2px;
    border: 1px solid #A8BFE8;
    &.not-border{
      border:none;
      box-shadow:none;
    }
	}
	/deep/ .el-form-item__label{
		&::before {
          margin-right: 2px !important;
        }
        // label 竖线
        &::after {
          content: "";
          display: inline-block;
          position: absolute;
          width: 1px;
          height: 14px;
          top: 50%;
          right: 0;
          transform: translateY(-50%);
          background: #3E485F;
        }
	}
	/deep/ .el-form-item__content{
		.el-input__inner{
			border: none;
		}
	}
}
.tCWrapForm{
	.el-form-item{
		flex-direction:column;
	}
  }
.w-full {
  width: 100%;
}
/deep/.el-range-editor--small .el-range-input{
  background: transparent;
  color: #DFE1EC;
}
/deep/.el-input-number.is-controls-right[class*=small] [class*=decrease], .el-input-number.is-controls-right[class*=small] [class*=increase]{
  background: transparent;
  border-radius:0px;
}
/deep/ .el-input-number__increase{
  background: transparent;
}
/deep/.el-input-number.is-controls-right .el-input-number__decrease{
  border-left: 1px solid #A8BFE8;
  border-right: 1px solid #A8BFE8;
  color: #fff;
}
/deep/.el-input-number.is-controls-right .el-input-number__increase{
  border-bottom: 1px solid #A8BFE8;
  border-left: 1px solid #A8BFE8;
  border-right: 1px solid #A8BFE8;
  color: #fff;
}
/deep/.el-checkbox-group{
  padding-left:10px;
}
/deep/.el-checkbox{
  width:100%;
  color: #DFE1EC;
}
/deep/.el-date-table td.in-range div{
  background-color: #A8BFE8;
}
/deep/.el-tag.el-tag--info{
  background-color: #1b293B;
  border:0px;
}

</style>
<style lang="less">
.el-popper[x-placement^=bottom] .popper__arrow::after{
      border-bottom-color: #040A1E;
}
.el-date-picker__header-label{
  color:#fff;
}
.earth-picker{
    .el-picker-panel__footer .el-button--text{
            display: none !important;
    }

    .el-input--small .el-input__inner{
        background: RGBA(63, 67, 68, 1);
        color: #fff;
        border: none;
    }
    .el-picker-panel__footer{
        background: transparent;
    }
    .el-time-spinner__item{
        line-height: 18px;
    }
    &.el-picker-panel{
        background: #040A1E;
        box-shadow: inset 0px 0px 8px 0px #4984FF;
        border-radius: 2px;
        border: 1px solid #A8BFE8;
        z-index: 10011;
    }
    .el-picker-panel__body{
        color:#fff;
    }
    .el-picker-panel__icon-btn{
        color:#fff;
    }
    .el-time-panel__btn{
        color:#fff;
    }
    .el-time-spinner__item.active:not(.disabled){
        color:#fff;
    }
    .el-time-panel{
        background: #040A1E;
    }
}
.el-select-dropdown.is-multiple .el-select-dropdown__item.selected.hover{
  background-color: #1b293B;
}
.el-select-dropdown.is-multiple .el-select-dropdown__item.selected{
  background-color: #1b293B;
}
.el-date-table td.in-range div{
  background-color: #1b293B;
}
</style>
