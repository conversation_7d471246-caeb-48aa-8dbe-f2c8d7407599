import * as echarts from 'echarts';

const tableColumn = [
    {
        prop: 'lacCell',
        label: 'LACCELL',
    },
    {
        prop: 'netType',
        label: '网络制式',
        width: 125,
    },
    {
        prop: 'status',
        label: '基站状态',
        width: 125,
    },
    {
        prop: '操作',
        label: '操作',
        width: 80,
    },
];

const getPlatformOverview = (data = {}) => [
    {
        name: '基站总数(个)',
        count: data.stationTotal || '-',
        unit: '个',
        props: 'stationTotal',
        img: 'baseStationsNum',
    },
    {
        name: '退服基站数(个)',
        count: data.stationOutNumber || '-',
        unit: '个',
        props: 'stationOutNumber',
        img: 'back-number',
    },
    {
        name: '基站退服率(%)',
        count: data.stationOutProportion ? Number(data.stationOutProportion).toFixed(2) : '-',
        unit: '%',
        props: 'stationOutProportion',
        img: 'back-rate',
    },
    {
        name: '平均退服时长',
        count: data.stationOutTime || '-',
        unit: '天',
        props: 'stationOutTime',
        img: 'stop-time',
    },
    {
        name: '新增退服基站数(个)',
        count: data.newStationOutNumber || '-',
        unit: '天',
        props: 'newStationOutNumber',
        img: 'newStationOutNumber',
    },
];
const getTrendChartList = (trendData = [], stationData = []) => {
    return [
        {
            title: '退服基站数',
            id: 'bar',
            option: {
                type: 'common',
                yAxis: [''],
                dataZoom: true,
                data: [
                    {
                        type: 'bar',
                        barWidth: '30%',
                        itemStyle: {
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                { offset: 0, color: 'rgba(73, 132, 255, 1)' },
                                { offset: 1, color: 'rgba(30, 57, 112, 0.60)' },
                            ]),
                        },
                        data: stationData,
                    },
                ],
            },
        },
        {
            title: '平均退服时长',
            id: 'line',
            option: {
                type: 'common',
                yAxis: [''],
                data: [
                    {
                        type: 'line',
                        itemStyle: {
                            color: 'rgba(125, 172, 220, 1)',
                        },
                        areaStyle: {
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                {
                                    offset: 0,
                                    color: 'rgba(125, 172, 220, 0.50)',
                                },
                                {
                                    offset: 1,
                                    color: 'rgba(125, 172, 220, 0)',
                                },
                            ]),
                        },
                        data: trendData,
                    },
                ],
            },
        },
    ];
};
const formCols = (getAreas, isShowQueryTime = true, handleDateChange) => {
    const list = [
        {
            element: () => import('@/script/components/selectCity.vue'),
            prop: 'region',
            label: '分析范围',
            attrs: {
                getAreas,
                clearable: true,
                placeholder: '请选择',
            },
            span: 5,
        },
        {
            prop: 'type',
            label: '查询时间',
            attrs: {
                clearable: true,
                placeholder: '请选择',
            },
            element: 'el-select',
            slot: {
                element: 'el-option',
                enums: [
                    { label: '离线', value: 0 },
                    { label: '实时', value: 1 },
                ],
            },
            span: 3,
        },
        {
            prop: 'queryTime',
            element: 'el-date-picker',
            attrs: {
                clearable: true,
                type: 'datetime',
                'popper-class': 'earth-picker',
                format: 'yyyy-MM-dd HH:mm',
                'value-format': 'yyyy-MM-dd HH:mm:00',
            },
            listeners: {
                change: handleDateChange,
            },
            span: 3,
            isShow: isShowQueryTime,
        },
        { span: 3 },
    ];
    return list;
};
const stationFormCols = () => {
    const list = [
        {
            prop: 'status',
            label: '基站状态',
            attrs: {
                clearable: true,
                placeholder: '请选择',
                size: 'mini',
            },
            element: 'el-select',
            slot: {
                element: 'el-option',
                enums: [
                    { label: '退服', value: 1 },
                    { label: '正常', value: 2 },
                    { label: '全部', value: 3 },
                ],
            },
            span: 24,
        },
    ];
    return list;
};
export { tableColumn, getPlatformOverview, formCols, stationFormCols, getTrendChartList };
