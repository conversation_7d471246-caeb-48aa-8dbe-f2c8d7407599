<template>
    <el-dialog
        width="300px"
        class="earth-dialog axios-progress"
        :visible="visible"
        :center="true"
        :show-close="false"
    >
        <div class="progress-bar">
            <div class="bg"></div>
            <div id="progress" class="bar" :style="{ width: progress + '%' }"></div>
        </div>
        <div class="label">{{ label }}</div>
    </el-dialog>
</template>
 
<script>
export default {
    name:'axiosProgress',
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            progress: 0,
            label:'基站校验中...',
            interval:null,
        }
    },
    watch:{
        visible:{
            handler(newV){
                if(newV){
                    this.progress = 0;
                    this.startProgress();
                }else{
                    this.stopProgress();
                }
            }
        }
    },
    methods: {
        startProgress(totalDuration= 3000,updateInterval= 500){
            this.$nextTick(() => {
                this.stopProgress();
                const progressBar = document.getElementById('progress');
                progressBar.style.transition = `none`;
                this.progress = 0;
                const timeout = setTimeout(() => {
                    progressBar.style.transition = `width ${updateInterval/1000}s linear`;
                    this.interval = setInterval(() => {
                        this.progress += (updateInterval / totalDuration) * 100; // 每次增加的进度
                        if (this.progress >= 100) {
                            this.startProgress(totalDuration,updateInterval);
                        }
                    }, updateInterval);
                    clearTimeout(timeout);
                }, 100);
            })
        },
        stopProgress(){
            this.interval && clearInterval(this.interval); // 停止定时器
        },
    }
};
</script>
 
<style lang="less" scoped>
/deep/.el-dialog .el-dialog__header{
    padding:8px;
}
.progress-bar {
    width: 100%;
    height: 16px;
    border-radius: 10px;
    border: 1px solid #516890;
    position:relative;
}
.bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 5px;
}
.bar {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    background: linear-gradient( 90deg, #4984FF 0%, #06D4FF 100%);
    border-radius: 9px;
    /* transition: width 0.5s linear; */
    will-change: width;
}
.label {
    font-weight: 400;
    font-size: 14px;
    color: #FFFFFF;
    line-height: 20px;
    padding-top:15px;
    text-align:center;
    animation: example 1s linear alternate infinite; 
    will-change: opacity;
}
@keyframes example {
  from {opacity: 0.2;}
  to {opacity: 1;}
}
</style>
