
import {fontSizeRem} from '@/utils/utils.js';
// 公共图配置
const comomCon = (options) => {
    const option = {
        title: {
            text: options.title ? `{a|${options.title}}\n{b|}` : '',
            textStyle: {
                rich: {
                    a: {
                        // 其他样式设置
                        color: '#046DBC', // 文本颜色
                        fontSize: fontSizeRem(12),
                        fontWeight: '500',
                    },
                    b: {
                        width: 40,
                        height: 0,
                        borderWidth: 3,
                        borderColor: '#C6EBFF',
                    },
                },
            },
            top: 5,
            textAlign: 'center',
            left: '49%',
        },
        tooltip: {
            confine: true,
            trigger: 'axis',
            axisPointer: {
                type: 'shadow',
                shadowStyle: {
                    color: 'rgba(52, 134, 255, 0.1)',
                },
            },
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            borderWidth: 0,
            textStyle: {
                color: '#ccc',
            },
        },
        dataZoom: [
            {
                type: 'inside',
                start: 0,
                end: 100,
            },
        ],
        grid: {
            top: 20,
            left: 10,
            right: 10,
            bottom: 5,
            containLabel: true,
        },
        xAxis: {
            type: 'category',
            data: options.xAxis,
            axisTick: { show: false },
            boundaryGap: true,
            axisLabel: {
                show: true,
                color: '#D0DEEE',
                fontSize: fontSizeRem(12),
                // interval: 0, // 显示所有标签
                formatter: function (value) {
                    if (!options.isxAxisBreak) {
                        return value;
                    } else {
                        if (value.includes(' ')) {
                            const valueList = value.split(' ');
                            return valueList[0] + '\n' + valueList[1] || '';
                        }
                        return value;
                    }
                },
            },
            axisLine: {
                show: true,
                lineStyle: {
                    color: '#3B5675',
                },
            },
            splitLine: {
                show: false,
                lineStyle: {
                    type: 'dashed',
                    color: '#D8D8D8',
                },
            },
        },
        yAxis: options.yAxis.map((item, index) => {
            const data = {
                type: 'value',
                name: item || '',
                nameTextStyle: {
                    fontSize: fontSizeRem(11),
                    padding: index === 0 ? [20, 0, 0, 15] : [20, 10, 0, 0],
                    color: '#6C8097',
                },
                axisLine: { show: false },
                axisTick: { show: false },
                axisLabel: {
                    margin: 4,
                    fontSize: 11,
                    color: '#6C8097',
                },
                splitLine: {
                    lineStyle: {
                        type: 'dashed',
                        color: '#1E3049',
                    },
                },
                minInterval:1,
            };
            return data;
        }),
        series: [],
    };
    return option;
};



const ChartsCon = {
    pureCommon:(options) => {
        return options;
    },
    common:(options) =>{
        const option = comomCon(options);
        if (options.hasLegend) {
            option.legend = {
                show: true,
                left: 'center',
                top: 10,
                itemHeight: 4,
                itemWidth: 12,
                itemGap: 12,
                textStyle: {
                    fontSize: fontSizeRem(11),
                    color: '#8D8D8D',
                    lineHeight: 0,
                },
                icon: 'rect',
            };
            option.grid = {
                top: 40,
                left: 10,
                right: 10,
                bottom: 30,
                containLabel: true,
            };
        }
        if (options.dataZoom === false) {
            delete option.dataZoom;
        }
        if (options.showyAxis === false) {
            option.yAxis = {
                show: false,
            };
        }
        option.series = options.data;
        return option;
    },
    pie:(options) =>{
        const data = options.pieData;
        const option = {
            grid: {
                top:'center',
                left: '5%',
                right: '5%',
                bottom:0,
            },
            tooltip: {
                trigger: 'item',
                confine:true,
                backgroundColor: 'rgba(0, 0, 0, 0.5)',
                borderWidth: 0,
                textStyle: {
                    color: '#ccc',
                },
            },
            legend:{
                left: '49%',
                top:'30%',
                align:'left',
                orient: 'vertical',
                itemHeight: 10,
                itemWidth: 10,
                itemGap:10,
                textStyle: {
                    fontSize: fontSizeRem(12),
                    color:'#fff',
                    rich:{
                        name:{
                            color: '#D0DEEE',
                            padding:[0,10,0,10],
                            fontSize: fontSizeRem(12),
                        },
                        value:{
                            color: '#fff',
                            width:40,
                            fontWeight:600,
                            fontSize: fontSizeRem(12),
                        }
                    }
                },
                backgroundColor:'#101D33',
                icon:'rect',
                formatter:function(name){
                    const legendItem = data.find((item) => item.name === name);
                    const Proportion = data.reduce((a,b) => a + b.value,0);
                    const value = legendItem.value;
                    let percentage = '-';
                    if(Proportion > 0 && value != '-'){
                        percentage = ((value/Proportion) * 100 ).toFixed(2)+ '%';
                    }
                    return`{name|${name}}{value|${value}}{name|占比}{value|${percentage}}` 
                },
            },
            graphic:[
                {
                    type:'image',
                    style:{
                        image:require('../../../img/analyzeDetails/pie.png'),
                        width:fontSizeRem(140),
                        height:fontSizeRem(140),
                    },
                    right:'57.5%',
                    top:'2%',
                }
            ],
            series:[
                {
                    name: '性别',
                    type: 'pie',
                    top:8,
                    left:38,
                    right:'56%',
                    radius: ['65%', '85%'],
                    avoidLabelOverlap: false,
                    hoverAnimation:false,
                    startAngle:80,
                    label: {
                        show: false,
                    },
                    labelLine: {
                        show: false,
                        length1:0,
                        length2:0,
                    },
                    data: options.pieData,
                },
            ]
        }
        return option;
    }
};

export { ChartsCon };
