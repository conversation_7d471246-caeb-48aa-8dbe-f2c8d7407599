<template>
    <el-dialog
        class="earth-dialog dialog-wrapper"
        :width="width"
        :visible="visible"
        :close-on-click-modal="false"
        @open="handleOpen"
        @close="handleClose"
    >
        <span slot="title" class="dialog-wrapper__title">{{title}}</span>
        <div class="dialog-wrapper__main">
            <div class="dialog-wrapper__main-body">
                <slot></slot>
            </div>
            <div class="dialog-wrapper__main-bottom">
                <el-button v-show="isShowCancel" class="width-60 cancel-btn" size="mini" @click="handleClose"
                    >取消</el-button
                >
                <el-button v-for="(item,index) in btnList" :key="index" :class="`width-60 ${item.class}`" size="mini" type="primary" 
                    v-on="item.listeners"
                    >{{item.name}}</el-button
                >
            </div>
        </div>
    </el-dialog>
</template>

<script>

export default {
    name: 'myDialog',
    components: {},
    props: {
        width:{
            type:String,
            default:'400px'
        },
        visible: {
            type: Boolean,
            default: false,
        },
        title:{
            type: String,
            default: '',
        },
        btnList:{
            type:Array,
            default:() => [],
        },
        type:{
            type:String,
            default:''
        },
        tips:{
            type:String,
            default:''
        },
        isShowCancel:{
            type:Boolean,
            default:false,
        }
    },
    data() {
        return {
        };
    },
    computed: {
       
    },
    methods: {
        handleOpen() {},
        handleClose() {
            this.$emit('update:visible', false);
        },
    },
};
</script>

<style lang="less" scoped>
.width-60 {
    width: 60px;
}
.dialog-wrapper {
    &__title {
        color: #fff;
        font-size: 15px;
        font-weight: bold;
    }
    &__main {
        display: flex;
        flex-direction: column;
        height: 100%;
        &-body {
            
        }
        &-bottom {
            display: flex;
            justify-content: flex-end;
            margin-top:20px;
        }
    }

    /deep/ .el-dialog {
        display: flex;
        flex-direction: column;
        border-radius: 10px;
        // padding:24px;
    }
    /deep/ .el-dialog__header {
        position: relative;
        padding: 13px 24px;
        border-bottom:1px solid RGBA(52, 69, 90, 1);
        .el-dialog__title {
            font-size: 15px;
            font-weight: bold;
        }
        .el-dialog__headerbtn {
            top: 14px;
        }
    }
    /deep/ .el-dialog__body {
        padding:24px ;
        flex: 1;
        height: 0;
        color:#fff;
    }
    /deep/ .el-dialog__footer {
        padding: 10px 16px;
    }
}
.cancel-btn{
    background: #E7E7E7;
    border-radius: 4px;
    color: rgba(0,0,0,0.9);
    border:none;
    &:hover{
        opacity:0.7;
    }
}
.red-btn{
    background: #FF4D4F;
    border-radius: 4px;
    color: rgba(255, 255, 255, 0.90);
    border:none;
    &:hover{
        opacity:0.7;
    }
}
.blue-btn{
    background: #3871B3;
    border-radius: 4px;
    color: #fff;
    border:none;
    &:hover{
        opacity:0.7;
    }
}
.bold {
    font-weight: bold;
    color: #454545;
}
.warning{
    display:flex;
    align-items:center;
    img{
        width:16px;
        height:16px;
    }
    .text{
        font-size: 14px;
        color: rgba(0,0,0,0.65);
        line-height: 22px;
        padding-left:10px;
    }
}
</style>
