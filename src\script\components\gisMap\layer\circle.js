export default (g) => {
    let layer = new g.layer();
    layer.visible = true;
    g.gis.scene.add(layer);
    g.meshList.circle.opacity = 0.1;
    class Circle {
        static createCircles(list) {
            const data = [];
            list.forEach((item, inx) => {
                data.push({
                    lat: item.lat,
                    lng: item.lng,
                    ht: inx / 10000 + 0.01,
                    color: 0xff3333,
                    radius: 100,
                    dir: 40,
                    angle: 360,
                    needFrame: true,
                    frameColor: 0xff3333,
                });
            });
            data.needFrame = true;
            data.frameWidth = 1;
            data.frameColor = 0xff3333;

            let mesh = g.meshList.circle.create(data);
            layer.add(mesh);
        }
        static removeAll() {
            layer.removeAll();
        }
    }
    return Circle;
};
