<template>
    <div class="situation-wrapper">
        <Transition name="fade-top">
            <div
                v-show="show"
                class="situation-wrapper-top"
                :style="{ height: isEarthQuakeEvent ? '165px' : '130px' }"
            >
                <div class="title">{{ rowData.taskName }}</div>
                <div class="show-list">
                    <el-row :gutter="10">
                        <el-col
                            class="show-list-item"
                            :span="item.span"
                            v-for="(item, index) in baseInfoList"
                            :key="index"
                        >
                            <span class="name">{{ item.name }}</span>
                            <span
                                v-tooltip
                                class="value earth-text-ellipsis"
                                :class="item.className"
                                :tips="
                                    item.name +
                                    (item.formatter ? item.formatter(rowData) : rowData[item.prop])
                                "
                            >
                                {{
                                    item.formatter
                                        ? item.formatter(rowData)
                                        : rowData[item.prop] || '-'
                                }}
                            </span>
                        </el-col>
                    </el-row>
                    <searchBar class="search-form" :fields="fields" :form="searchForm" size="mini">
                        <el-button
                            class="earth-btn-common"
                            type="primary"
                            size="mini"
                            @click="search()"
                            >查询</el-button
                        >
                    </searchBar>
                </div>
                <div class="return" @click="back">返回</div>
            </div>
        </Transition>
        <div
            class="situation-wrapper-content"
            :style="{ height: `calc(100% - ${isEarthQuakeEvent ? 165 : 130}px)` }"
        >
            <gisMap
                v-loading="isLoading"
                element-loading-text="拼命加载中"
                element-loading-spinner="el-icon-loading"
                element-loading-background="rgba(0, 0, 0, 0.8)"
                @loaded="gisLoaded"
            />
            <div class="legend">
                <mapLegend :legends="legends" type="img" />
            </div>
            <Transition name="fade-left">
                <div v-show="show" class="left-panel">
                    <card :img="require(`../../../img/common/overview2.png`)" title="概览">
                        <div
                            class="flex-row card-item"
                            v-for="(item, index) in cardList"
                            :key="index"
                        >
                            <img :src="require(`../../../img/icon/${item.img}.png`)" alt="" />
                            <div class="flex-column overview-right">
                                <span class="overview-num text-shadow">{{ item.num }}</span>
                                <span class="overview-text">{{ item.text }}</span>
                            </div>
                        </div>
                    </card>
                </div>
            </Transition>
            <Transition name="fade-right">
                <div v-show="show" class="right-panel">
                    <div class="station-status">
                        <searchBar class="search-form" :fields="formCols" :form="form"></searchBar>
                    </div>
                    <card :img="require(`../../../img/common/baseStation.png`)" title="基站列表">
                        <dataTable
                            class="data-table"
                            :columns="columns"
                            :data="tableData"
                            :pagination="paginationData"
                            :total="total"
                            :updateTable="getTableData"
                            :highlight-current-row="true"
                            @cell-click="cellClick"
                        >
                            <!-- 基站状态 -->
                            <template #status="{ row }">
                                <div
                                    v-if="row.status != null"
                                    class="row-list"
                                    :class="['green', 'red'][row.status]"
                                >
                                    ● {{ ['正常', '退服'][row.status] }}
                                </div>
                            </template>
                            <!-- 操作 -->
                            <template #操作="{ row }">
                                <el-popover
                                    placement="left"
                                    title="标题"
                                    width="300"
                                    trigger="click"
                                    popper-class="station-popper"
                                >
                                    <div>
                                        <div class="title">退服详情</div>
                                        <div class="flex-row">
                                            <span class="popper-name">退服总时长：</span>
                                            <span class="popper-value">{{ row.duration }}</span>
                                        </div>
                                    </div>
                                    <el-button slot="reference" style="color: #53ffff" type="text"
                                        >详情</el-button
                                    >
                                </el-popover>
                            </template>
                        </dataTable>
                    </card>
                </div>
            </Transition>
        </div>
    </div>
</template>

<script>
import {
    earthQuakeEventList,
    customEventList,
    getPlatformOverview,
    stationFields,
    tableColumns,
} from '_const/baseStationSituation.js';
import { tooltip } from '@/script/common/directives/tooltip.js';
import gisMap from '_com/gisMap/index.vue';
// import { setFitView } from '@/utils/utils.js';
import initCircular from '_com/gisMap/layer/circular.js';
import initGrid from '_com/gisMap/layer/Grid.js';
import initPoint from '_com/gisMap/layer/point.js';
import mapLegend from '_com/gisMap/mapLegend.vue';
import card from '_com/card/index.vue';
import dataTable from '_com/table/dataTable.vue';
import searchBar from '_com/searchBar/searchBar.vue';
import { toCoordinate } from '@/utils/method.js';
export default {
    name: 'baseStationSituation',
    components: {
        gisMap,
        mapLegend,
        card,
        dataTable,
        searchBar,
    },
    data() {
        return {
            show: false,
            rowData: {},
            legends: [
                { value: '正常基站', img: require('../../../img/icon/station_normal.png') },
                { value: '退服基站', img: require('../../../img/icon/station_suspended.png') },
            ],
            isLoading: false,
            searchForm: {
                queryTime: null,
                status: 3,
            },
            cardList: getPlatformOverview(),
            formCols: stationFields,
            form: {
                status: 3,
            },
            columns: tableColumns,
            tableData: new Array(4).fill({
                lacCell: '310000_6172_96',
                netType: '4G',
                status: 1,
                duration: 5.3,
            }),
            paginationData: {
                curPage: 1,
                pageSize: 20,
            },
            total: 0,
        };
    },
    directives: {
        tooltip,
    },
    computed: {
        fields() {
            return [
                {
                    prop: 'queryTime',
                    label: '查询时间',
                    element: 'el-date-picker',
                    attrs: {
                        clearable: true,
                        type: 'datetime',
                        'popper-class': 'earth-picker',
                        format: 'yyyy-MM-dd HH:mm',
                        'value-format': 'yyyy-MM-dd HH:mm:00',
                    },
                    listeners: {
                        change: this.handleDateChange
                    },
                    span: 4,
                },
                { span: 3 },
            ];
        },
        routeData() {
            return this.$route.params || {};
        },
        isEarthQuakeEvent() {
            return this.rowData.taskCreateSource === 1;
        },
        baseInfoList() {
            return this.isEarthQuakeEvent ? earthQuakeEventList : customEventList;
        },
    },
    watch: {
        'form.status': {
            handler() {
                this.paginationData.curPage = 1;
                this.getTableData();
            },
        },
    },
    created() {
        this.initRowData();
        // this.searchForm.queryTime = this.rowData.analysisStartTime;
    },
    mounted() {
        this.$nextTick(() => {
            this.show = true;
        });
    },
    methods: {
        initRowData() {
            const params = sessionStorage.getItem('stationData') || '{}';
            if (JSON.stringify(this.routeData) !== '{}') {
                this.rowData = this.routeData;
            } else if (JSON.stringify(params) !== '{}') {
                this.rowData = JSON.parse(params);
            }
            sessionStorage.setItem('stationData', JSON.stringify(this.rowData));
        },
        gisLoaded(g) {
            this.g = g;
            this.circular = initCircular(g, {});
            this.Polygon = initGrid(g);
            this.NormalBasePoint = initPoint(g, {
                img: require('../../../img/icon/map_baseStation_normal.png'),
                activeImg: require('../../../img/icon/map_baseStation_normal_active.png'),
                globalSize: 40,
            });
            this.SuspendedBasePoint = initPoint(g, {
                img: require('../../../img/icon/map_baseStation_suspended.png'),
                activeImg: require('../../../img/icon/map_baseStation_suspended_active.png'),
                globalSize: 40,
            });
            this.drawOutline();
            this.search();
        },
        search() {
            this.getOverviewAndStation();
        },
        getOverviewAndStation() {
            const { queryTime, status } = this.searchForm;
            this.form.status = status;
            this.getPost(
                'post',
                'getBaseStationOverview',
                {
                    areaType: 1, // 1: 自定义 2: 行政区
                    taskId: this.rowData.taskId,
                    queryTime: queryTime || null,
                    status,
                },
                '退服基站概览',
                (res = {}) => {
                    const { stationTotal = 0, stationList = [] } = res;
                    // 概览
                    this.cardList = getPlatformOverview(res);
                    // 基站列表
                    this.tableData = stationList.slice(0, 20);
                    this.total = stationTotal;
                    // 渲染基站 todo
                    const withdrawServiceStations = [];
                    const normalBaseStations = [];
                    for (const item of stationList) {
                        const { status, cgiLat, cgiLng } = item;
                        const stations =
                            status === 0 ? normalBaseStations : withdrawServiceStations;
                        stations.push({
                            latLng: {
                                lat: cgiLat,
                                lng: cgiLng,
                            },
                            data: item,
                        });
                    }
                    this.drawBaseStation(normalBaseStations, withdrawServiceStations);
                }
            );
        },
        getTableData(paginationData = {}) {
            const { curPage = 1, pageSize = 20 } = paginationData;
            const { queryTime } = this.searchForm;
            const { status } = this.form;
            let params = {
                areaType: 1, // 1: 自定义 2: 行政区
                taskId: this.rowData.taskId,
                queryTime: queryTime || null,
                status,
                pageSize,
                currentPage: curPage,
            };
            this.getPost('post', 'getBaseStationPage', params, '退服基站分页查询', (res) => {
                console.log('getTableData', res);
                const { stationList = [], totalPageNum = 0 } = res || {};
                this.tableData = stationList;
                this.total = totalPageNum;
            });
        },
        // 渲染基站
        drawBaseStation(normalBaseStations = [], withdrawServiceStations = []) {
            this.NormalBasePoint.createPoints(normalBaseStations, true, false);
            this.SuspendedBasePoint.createPoints(withdrawServiceStations, true, false);
        },
        cellClick(row) {
            const normalGraphs = this.NormalBasePoint.getGraphs();
            const suspendedGraphs = this.SuspendedBasePoint.getGraphs();
            const graphs = [...normalGraphs, ...suspendedGraphs];
            const graph = graphs.find((item) => item.data.lacCell === row.lacCell);
            if (graph) {
                graph.toMovePoint(graph.latLng);
            }
        },
        drawOutline() {
            const { shapeType, regionCoors, centerLon, centerLat, analysisRadius } = this.rowData;
            const isPolygon = Number(shapeType) === 2;
            if (isPolygon) {
                const data = {
                    centerPoint: {},
                    points: toCoordinate(regionCoors),
                    config: {
                        color: 0x7dacdc,
                    },
                    data: {},
                };
                this.Polygon && this.Polygon.createGrids([data], null, true, true);
            } else {
                if (!centerLon || !centerLat) {
                    return;
                }
                const startPoint = { lat: centerLat, lng: centerLon };
                this.circular.createCircular(
                    {
                        circleColor: 0x7dacdc,
                        circleOpacity: 0.1,
                        circleFrame: true,
                        circleFrameColor: 0x7dacdc,
                        cirCleShowClose: true,
                        circleShowRadius: false,
                        radius: Number(analysisRadius),
                        startPoint: startPoint,
                    },
                    {
                        dom: `<div class="epicenter"></div>`,
                        point: startPoint,
                    }
                );
            }
        },
        handleDateChange(val) {
            const date = new Date(val);
            const minutes = date.getMinutes();
            if (minutes % 10 !== 0) {
                this.$message({
                    message: '时间选择的分钟只能是00, 10, 20, 30, 40, 50',
                    type: 'warning',
                });
                this.searchForm.queryTime = '';
            }
        },
        back() {
            this.$router.back();
        },
    },
};
</script>

<style lang="less" scoped>
.situation-wrapper {
    width: 100%;
    height: 100%;
    &-top {
        // height: 170px;
        background: linear-gradient(270deg, #101620 0%, #1b2f4d 100%);
        padding: 15px 40px 10px;
        position: relative;
    }
    .left-panel {
        position: absolute;
        left: 24px;
        top: 24px;
        width: 300px;
        height: calc(100% - 48px);
    }
    .right-panel {
        position: absolute;
        right: 24px;
        top: 24px;
        width: 512px;
        height: calc(100% - 48px);
    }
    .show-list {
        width: 100%;
        .el-row {
            display: flex;
            flex-wrap: wrap;
        }
        &-item {
            display: flex;
            align-items: center;
        }
        .name {
            font-weight: 400;
            font-size: 16px;
            color: #ffffff;
            width: auto;
            white-space: nowrap;
        }
        .value {
            font-weight: 400;
            font-size: 16px;
            color: #ffffff;
        }
        .level-text {
            font-size: 18px;
            font-weight: 600;
            color: #ffa040;
            font-style: italic;
        }
        .search-form {
            margin-top: 4px;
        }
        /deep/.el-col {
            padding-top: 10px;
        }
    }
    &-content {
        width: 100%;
        // height: calc(100% - 170px);
        position: relative;
        .legend {
            position: absolute;
            right: 550px;
            bottom: 24px;
        }
    }
}
.title {
    font-size: @font-size-h1;
    font-weight: bold;
    color: #ffffff;
    line-height: 28px;
    text-shadow: 0px 0px 4px rgba(62, 136, 233, 0.64);
    position: relative;
    &::after {
        position: absolute;
        content: '';
        left: -15px;
        top: 10px;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: #53ffff;
    }
}
.return {
    position: absolute;
    right: 20px;
    top: 10px;
    background: url('../../../img/common/return.png') no-repeat center center / 100% 100%;
    width: 84px;
    height: 40px;
    color: #fff;
    font-size: 14px;
    text-align: center;
    line-height: 40px;
    cursor: pointer;
}
/deep/.epicenter {
    position: relative;
    background: url('../../../img/analyzeDetails/epicenter.png') no-repeat center center / 100% 100%;
    width: 48px;
    height: 52px;
    transform: translate(-50%, -50%);
}
.card-item {
    width: 100%;
    height: 88px;
    img {
        width: 78px;
        height: 78px;
    }
    .overview-num {
        font-size: 24px;
        color: #ffffff;
        line-height: 40px;
    }
    .overview-text {
        font-size: 16px;
        color: #ffffff;
    }
    .overview-right {
        padding-left: 15px;
    }
}
.station-status {
    position: absolute;
    width: 200px;
    height: 40px;
    right: 30px;
    top: 10px;
    z-index: 3;
}
.row-list {
    display: flex;
    align-items: center;
    width: 90%;
    height: 20px;
    border-radius: 2px;
    padding: 0 10px;
    &.green {
        color: #3df258;
    }
    &.red {
        color: #fe3333;
    }
}
.popper-name {
    font-size: 14px;
    color: #9aafc1;
    line-height: 20px;
    padding-top: 10px;
}
.popper-value {
    font-size: 14px;
    color: #ffffff;
    line-height: 20px;
    padding-top: 10px;
}
</style>
<style lang="less">
.station-popper {
    &.el-popover {
        width: 300px;
        height: 100px;
        background: rgba(23, 42, 65, 0.3);
        box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.5), 0px 2px 6px 0px rgba(0, 0, 0, 0.5);
        border-radius: 12px;
        border: 1px solid;
        border-image: linear-gradient(135deg, rgba(176, 188, 255, 1), rgba(95, 107, 168, 1)) 1 1;
        backdrop-filter: blur(32px);
        padding: 20px 30px;
    }

    .el-popover__title {
        display: none;
    }
    .popper__arrow {
        display: none;
    }
}
</style>
