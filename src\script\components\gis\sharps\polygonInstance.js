import {
    isExistWithinCurArea,
    addLastStatus,
    toCoordinate,
    toPointSequence,
    formatBaseStations,
    getDiffSet,
    getIntersectionSet,
    isImportCreated,
    getCoordinateExtremum,
} from '@/utils/method.js';
import { menuList } from '@/script/constant/gisOption.js';
import * as gisUtils from '@/utils/gisUtils';
export default () => {
    return class Polygon {
    constructor(i, option = {}) {
        const h = i || 1;
        const baseVal = h / 10000;
        this.region = {
            prop: 'region',
            points: [], // 区域对应的轮廓坐标
            plane: null,
            innerBaseStations: [],
            maxH: option.basal || baseVal + (1 / 200), // 动态设置图形高度，防止多个图形高度一致导致颜色渲染不均匀
            color: option.color || 0x0085f9,
            frameColor: option.color || 0x1a7cff,
        };
        this.hole = {
            prop: 'hole',
            points: [],
            plane: [],
            color: 0xffffff,
            maxH: baseVal + (1 / 100),
        };
        this.radiate = {
            prop: 'radiate',
            points: [],
            plane: null,
            color: 0xe59f00,
            maxH: baseVal + (1 / 300),
        };
        this.i = i;
        this.isJustShow = option.isJustShow;
        this.baseStations = [];
        this.distance = 3;  // 默认辐射距离
    }
    static initGisInfo(g, that) {
        if (Polygon.isInitialized) {
            return;
        }
        Polygon.isInitialized = true;

        Polygon.g = g;
        Polygon.that = that;
        Polygon.currentClickHole = null;
        Polygon.i = 1;

        Polygon.layer = new g.layer();
        Polygon.radiationLayer = new g.layer();
        Polygon.holeLayer = new g.layer();

        Polygon.layer.visible = true;
        Polygon.radiationLayer.visible = true;
        Polygon.holeLayer.visible = true;

        g.gis.scene.add(Polygon.layer);
        g.gis.scene.add(Polygon.radiationLayer);
        g.gis.scene.add(Polygon.holeLayer);

        // 多边形编辑完成事件
        g.layerList.areaEdit.onEditFinish.addEvent(Polygon.areaEditFinish, 'Polygon');
        // 空洞点击事件
        g.event.addClick(Polygon.holeLayer, (data, event) => {
            const curHole = data.object.operateType;
            if (that.isJustShowLayer || curHole.isJustShow) return;

            Polygon.currentClickHole = curHole.index;
            that.layerObj = that.planeObjs[curHole.i];
            if (event.button === 0) {
                // 鼠标左键
                Polygon.setCurOperateItem(curHole);
                // 清除plane
                Polygon.holeLayer.remove(data.object);
                // 绘制可编辑的多边形
                const mesh = gisUtils.areaEditCreate(g, {
                    name: '编辑图层',
                    color: curHole.color,
                    points: curHole.points,
                    justShow: that.isJustShowLayer,
                });
                g.layerList.areaEdit.start(mesh);
            } else if (event.button === 2) {
                that.menuList = menuList.filter((item) => item.prop === 'delHole');
                that.isShowMenu = true;
            }
        });
        // 多边形点击事件, 点击plane，获取points进行二次编辑
        g.event.addClick(Polygon.layer, (data, event) => {
            const curRegion = data.object.operateType;
            if (that.isJustShowLayer || curRegion.isJustShow) return;

            that.layerObj = that.planeObjs[curRegion.i];

            if (event.button === 0) {
                // 鼠标左键
                Polygon.setCurOperateItem(curRegion);
                // 清除plane
                Polygon.layer.remove(data.object);
                // 绘制可编辑的多边形。  
                const mesh = gisUtils.areaEditCreate(g, {
                    name: '编辑图层',
                    color: curRegion.color,
                    points: curRegion.points,
                    justShow: that.isJustShowLayer,
                });
                g.layerList.areaEdit.start(mesh);
            } else if (event.button === 2) {
                // 鼠标右键
                const radiateName =
                    that.layerObj && that.layerObj.radiate.plane ? 'cancelRadiation' : 'outwardRadiate';
                that.menuList = menuList.filter((item) => {
                    return [radiateName, 'hole', 'auxiliaryCircle', 'delPolygon'].includes(item.prop);
                });
                if (that.isStreetManage) {
                   that.menuList = menuList.filter((item) => ['delPolygon'].includes(item.prop));
                }
                that.isShowMenu = true;
            }
        });
        // 多边形点击事件, 点击plane，获取points进行二次编辑
        g.event.addDoubleClick(Polygon.layer, (data) => {
            const curRegion = data.object.operateType;
            curRegion.setCurDetail && curRegion.setCurDetail();
        });
        // 辐射多边形点击事件
        g.event.addClick(Polygon.radiationLayer, (data, event) => {
            if (that.isJustShowLayer) return;
            const operateItem = data.object.operateType;
            if (!that.layerObj || operateItem.i !== that.layerObj.i) {
                that.layerObj = that.planeObjs[operateItem.i];
            }
            if (event.button === 2) {
                const { clientX, clientY } = event;
                that.coordinate = { x: clientX, y: clientY };
                that.$refs.radiationRangeRef.distance = that.layerObj.distance;
                that.isShowRadiation = true;
            }
        });
    }
    static async initRegionCoors(regionCoors, isClearAll = false, isResetCheckBase = false, isEntry = false) {
        const { g, that, getHollowPolygon, initBaseStations } = Polygon;
        if (isClearAll) that.clearAll();
        if (isResetCheckBase) that.isCheckBase = false;
        // 已创建的复合区域，获取复合区域区域内基站
        if (that.isShowHideManage && that.isMultiRegion && that.baseInfo.regionId) {
            const { regionInnerList, hollowOutCellList } = await that.getCreatedInnerStations(that.baseInfo.regionId) || {};
            const hollowOutCellStations = formatBaseStations(hollowOutCellList || [], 'noAdd');
            let regionInnerListData = regionInnerList.filter((item) => item.cellType === 2);
            if (!regionInnerListData.length) {
                regionInnerListData = regionInnerList;
            }
            const regionInnerStations = formatBaseStations(regionInnerListData, 'added');
            that.multiCreatedStations = [...hollowOutCellStations, ...regionInnerStations];
        }
        // 处理空洞数据
        const hollowPolygon = getHollowPolygon(that.baseInfo, isEntry);

        const mergeRegions = [];
        const initBaseStationsPromises = regionCoors.map((points, index) => {
            // 赋值
            const hollowPolygonItem = hollowPolygon[index];
            that.planeObjs[index] = new Polygon(index);
            const region = that.planeObjs[index].region;
            region.points = toCoordinate(points);
            // todo:随意选择多边形的坐标点作为定位点
            mergeRegions.push(...region.points);

            if (hollowPolygonItem) {
                const holeObj = that.planeObjs[index].hole;
                holeObj.points = hollowPolygonItem;
            }

            that.layerObj = that.planeObjs[index];
            that.layerObj.drawRegion(region.points, that.layerObj.i, (points, hole) => {
                if (points.length) {
                    points.forEach((item) => {
                        that.layerObj.drawHole(item, hole);
                    });
                }
            });

            if (that.isShowHideManage) {
                return initBaseStations(that.layerObj, that.isMultiRegion, that.baseInfo.regionId);
            }
            return Promise.resolve();
        });
        await Promise.all(initBaseStationsPromises);

        that.setAllBaseStations();

        if (that.multiCreatedStations && that.multiCreatedStations.length) {
            addLastStatus(that.multiCreatedStations, that.allBaseStations, (item, tarItem) => {
                item.cellBoundary = tarItem.cellBoundary;
            });
        }
        // 绘制辅助圆
        that.drawCircleChoice();
        // 移动到地图中心
        if (mergeRegions) {
            const extremum = getCoordinateExtremum(mergeRegions);
            g.cameraControl.zoomByPoints(extremum, 1);
        }
    }
    // 用于渲染区域组
    static initRegions(polygons, option = {}) {
        const { row, config, setCurDetail } = option;
        const { that, getHollowPolygon } = Polygon;
         if (!polygons || !polygons.length) {
            that.$message.error('该多边形区域不存在');
            return;
        }
        // 处理空洞数据
        const hollowPolygon = getHollowPolygon(row);

        const mergeRegions = [];
        const sharps = [];

        polygons.forEach((points, index) => {
            // 赋值
            const hollowPolygonItem = hollowPolygon[index];
            const curSharp = new Polygon(index, config);
            const region = curSharp.region;
            region.points = toCoordinate(points);
            setCurDetail && (region.setCurDetail = setCurDetail);

            if (hollowPolygonItem) {
                const holeObj = curSharp.hole;
                holeObj.points = hollowPolygonItem;
            }

            curSharp.drawRegion(region.points, curSharp.i, (points, hole) => {
                if (points.length) {
                    points.forEach((item) => {
                        curSharp.drawHole(item, hole);
                    });
                }
            });

            sharps.push(curSharp);
            mergeRegions.push(...region.points);
        });
        return { mergeRegions, sharps };
    }
    static getHollowPolygon(baseInfo, isEntry = false) {
        const { g, that, addInxForHollowList } = Polygon;
        // 处理空洞数据
        const hollowPolygon = {};
        let holePolygonList = [];
        let { hollowPolygonList, multiPolygonList, regionCoors } = baseInfo;
        if (isImportCreated(hollowPolygonList)) {
            if (!multiPolygonList || !multiPolygonList.length) {
                multiPolygonList = regionCoors ? [{ areaType: 2, polygon: regionCoors }] : [];
            }
            holePolygonList = addInxForHollowList(multiPolygonList, hollowPolygonList, g).holeList;
        } else {
            holePolygonList = hollowPolygonList;
        }

        if (isEntry) {
            holePolygonList = that.entryHoleList;
        }

        holePolygonList && holePolygonList.forEach((item) => {
            if (hollowPolygon[item.region]) {
                hollowPolygon[item.region].push(toCoordinate(item.polygon));
            } else {
                hollowPolygon[item.region] = [toCoordinate(item.polygon)];
            }
        });
        return Object.values(hollowPolygon).map((item) => item);
    }
    static addInxForHollowList(regionList = [], holeList = [], g) {
        const newHoleList = [];
        const copyHoleList = [...holeList];
        for (const [inx, region] of regionList.entries()) {
            let index = 0;
            for (const [holeInx, hole] of holeList.entries()) {
                const isExist = isExistWithinCurArea(hole.polygon, region.polygon, g);
                if (isExist) {
                    newHoleList.push({
                        ...hole,
                        index: index++,
                        region: inx,
                    });
                    copyHoleList[holeInx] = null;
                }
            }
        }
        return {
            holeList: newHoleList,
            invalidHoleList: copyHoleList.filter(Boolean),
        }
    }
    static setCurOperateItem(curOperateItem) {
        Polygon.curOperateItem = curOperateItem;
    }
    static async areaEditFinish(data) {
        const { g, that } = Polygon;
        if (that.shapeType !== 2) return;
        const editPoints = gisUtils.three2world(g, data.points);
        data.name = '$123';
        g.layerList.areaEdit.removeByName('$123');

        const { prop } = Polygon.curOperateItem;
        // plane渲染多边形
        if (prop === 'region') {
            that.layerObj.drawRegion(editPoints);
            // 
            if (that.isStreetManage) return;
            // 判断是否要渲染辐射多边形和打点,更新基站到总的菜单
            const { radiate, region, distance, baseStations: preBaseStations } = that.layerObj;
            const isExitRadiate = radiate && radiate.points.length && radiate.plane;
            const { innerBaseStations, outPoints, baseStations } = await Polygon.getBaseStations(
                region.points,
                distance,
            );
            if (preBaseStations.length) {
                radiate.points = outPoints;
            } else {
                that.layerObj.baseStations = baseStations;
            }

            if (isExitRadiate) {
                Polygon.radiationLayer.remove(radiate.plane);
                that.layerObj.drawRadiate(outPoints);
            }
            // 区域多边形收缩
            if (innerBaseStations.length < region.innerBaseStations.length) {
                const diffBaseStations = getDiffSet(region.innerBaseStations, innerBaseStations);
                diffBaseStations.forEach((item) => Object.assign(item, {
                    status: 'noAdd',
                    isArea: '否',
                }));
                addLastStatus(diffBaseStations, preBaseStations);
                // 修改其他区域有差集diffBaseStations相关的点
                const otherLayers = that.planeObjs.filter((item) => item && (item.i !== that.layerObj.i));
                otherLayers.forEach((item) => {
                    const intersection = getIntersectionSet(item.baseStations, diffBaseStations);
                    intersection.forEach((val) => Object.assign(val, {
                        status: 'noAdd',
                    }));
                    addLastStatus(intersection, item.baseStations);
                });
            }
            region.innerBaseStations = innerBaseStations;

            addLastStatus(innerBaseStations, preBaseStations);

            that.setAllBaseStations();
            that.setBasePoints(true);
            that.$eventBus.$emit('updateSharpInfo');
        } else if (prop === 'hole') {
            that.layerObj.drawHole(editPoints);
            const { plane, points } = that.layerObj.region;
            Polygon.layer.remove(plane); // 删除上一次的多边形图形
            that.layerObj.drawRegion(points);
            that.setBasePoints(true);
        }
    }
    static initBaseStations(layerObj, isMulti = false, regionId) {
        const { that, getBaseStations } = Polygon;
        const params = {};
        if (!isMulti) {
            Object.assign(params, {
                regionId,
                type: regionId ? 0 : 1,
            })
        }
        return getBaseStations(layerObj.region.points, that.defExpansion, params, isMulti)
            .then((res) => {
                const { innerBaseStations, baseStations, outPoints } = res;
                const { region, radiate } = layerObj;
                layerObj.baseStations = baseStations;
                region.innerBaseStations = innerBaseStations;
                radiate.points = outPoints;
            })
            .catch((err) => {
                console.error(err);
                that.$message.error('绘制区域时获取基站数据报错');
            });
    }
    static clearAll() {
        if (!Polygon.isInitialized) return;
        Polygon.layer.removeAll();
        Polygon.holeLayer.removeAll();
        Polygon.radiationLayer.removeAll();
    }
    static unbindEvent() {
        if (!Polygon.isInitialized) return;
        const { g } = Polygon;
        g.event.removeClick(Polygon.layer);
        g.event.removeClick(Polygon.holeLayer);
        g.event.removeClick(Polygon.radiationLayer);
        if (g.layerList.areaEdit) {
            g.layerList.areaEdit.onEditFinish.removeEvent('Polygon');
        }
        Polygon.isInitialized = false;
    }
    // isInit 是否初始化数据
    static async getBaseStations(points, expansion, outsideParams = {}, isMulti) {
        const that = Polygon.that;
        if (!points.length) {
            that.$message.warning('区域不存在');
            return;
        }
        const params = {
            type: 1,
            shapeType: 2,
            isMultiRegion: 0,
            expansion, // 辐射距离，10：表示查看区域所有基站
            regionCoors: toPointSequence(points),
        };
        Object.assign(params, outsideParams);

        const res = await that.$post('getRegionExpansionCells', params);
        const { regionOutList, regionExpansionCoors, regionInnerList, hollowOutCellList } = res;
        let regionInnerListData = regionInnerList;
        if (params.regionId) {
            regionInnerListData = regionInnerList.filter((item) => item.cellType === 2);
            if (!regionInnerListData.length) {
                regionInnerListData = regionInnerList;
            }
        }
        const innerBaseStations = formatBaseStations(regionInnerListData, isMulti ? 'noAdd' : 'added');
        const outBaseStations = formatBaseStations(regionOutList, 'noAdd');
        const hollowOutCellStations = formatBaseStations(hollowOutCellList || [], 'noAdd');
        const baseStations = [...innerBaseStations, ...outBaseStations, ...hollowOutCellStations];
        return {
            innerBaseStations,
            outBaseStations,
            outPoints: toCoordinate(regionExpansionCoors),
            baseStations,
        };
    }
    drawRegion(editPoints = this.region.points, inx, callback) {
        const g = Polygon.g;
        const { color, maxH, frameColor } = this.region;
        const hole = this.hole;
        const regionPoints = editPoints.map((item) => [item.lng, item.lat]);

        g.meshList.plane.opacity = 0.3;
        //传入数据创建平面
        const item = {
            ls: g.math.lsRegular(regionPoints) || [],
            layers: [{ maxH: maxH * g.HM, color }],
            needFrame: true,
            frameColor,
        };

        const points = hole.points;
        if (hole && points && points.length) {
            const holes = [];
            points.forEach((item) => {
                const tranHolePoint = item.map((val) => [val.lng, val.lat]);
                holes.push(g.math.lsRegular(tranHolePoint));
            });
            item.holes = holes;
        }

        const data = [item];
        Object.assign(data, {
            needFrame: true,
            frameColor,
        });
        this.region.plane = g.meshList.plane.create(data);
        // 自定义数据
        Object.assign(this.region, {
            points: editPoints,
            i: Number.isFinite(inx) ? inx : this.i,
            isJustShow: this.isJustShow
        });
        this.region.plane.operateType = this.region;
        //图层添加模型
        Polygon.layer.add(this.region.plane);
        if (callback && hole && hole.points && hole.points.length) {
            callback(hole.points, hole);
        }
        g.gis.needUpdate = true;
    }
    drawHole(editPoints) {
        const g = Polygon.g;
        const { color, maxH } = this.hole;
        const points = editPoints.map((item) => [item.lng, item.lat]);
        const holePoints = g.math.lsRegular(points) || [];

        const data = [
            {
                ls: holePoints,
                layers: [{ maxH, color }],
            },
        ];
        Object.assign(data, {
            needFrame: true,
            frameColor: 0x666666,
        });
        const holePlane = g.meshList.plane.create(data);
        const holeObjs = Object.assign({}, this.hole, {
            points: editPoints,
            i: this.i,
            index: this.hole.plane.length,//一个区域多个空洞标识
            isJustShow: this.isJustShow
        });
        holePlane.operateType = holeObjs;
        this.hole.plane.push(holePlane);
        Polygon.holeLayer.add(holePlane);
        g.gis.needUpdate = true;
    }
    drawRadiate(editPoints) {
        const g = Polygon.g;
        const { color, maxH } = this.radiate;
        // todo 测试
        const points = editPoints.map((item) => [item.lng, item.lat]);
        const radiatePoints = g.math.lsRegular(points) || [];
        const data = [
            {
                ls: radiatePoints,
                layers: [{ maxH, color }],
            },
        ];
        Object.assign(data, {
            needFrame: true,
            frameColor: 0xf39003,
        });
        this.radiate.plane = g.meshList.plane.create(data);
        Object.assign(this.radiate, {
            points: points.map(([lng, lat]) => ({ lng, lat })),
            i: this.i,
        });
        this.radiate.plane.operateType = this.radiate;
        Polygon.radiationLayer.add(this.radiate.plane);
        g.gis.needUpdate = true;
    }
    setColor(color = this.color) {
        Polygon.g.meshList.plane.changeColor(this.region.plane, 0, 0, color);
    }
    clear() {
        if (this.region.plane) {
            Polygon.layer.remove(this.region.plane);
            this.hole.plane.forEach(item => {
                Polygon.holeLayer.remove(item);
            });
        }
    }
    async setRadiateInfo() {
        const { region, radiate, distance } = this;
        const that = Polygon.that;
        const { outPoints, baseStations } = await Polygon.getBaseStations(region.points, distance);
        addLastStatus(this.baseStations, baseStations);
        this.baseStations = baseStations;
        radiate.points = outPoints;
        radiate.plane && Polygon.radiationLayer.remove(radiate.plane);
        this.drawRadiate(outPoints);
        that.setAllBaseStations();
        that.setBasePoints();
    }
    updateRadiation(distance) {
        if (distance && this.distance !== distance) {
            this.distance = distance;
            this.setRadiateInfo();
        }
    }
    async selectMenu(prop) {
        const { g, that } = Polygon;
        if (prop === 'cancelRadiation') {
            Polygon.radiationLayer.remove(this.radiate.plane);
            this.radiate.plane = null;
            that.isShowRadiation = false;
        } else if (prop === 'hole') {
            Polygon.setCurOperateItem(this.hole);
            that.start('areaEdit'); // todo、areaEdit -> 多边形
        } else if (prop === 'delHole') {
            // todo 删除空洞
            const { region, hole } = this;
            hole.points = [];
            const currentClickHole = Polygon.currentClickHole || 0;
            Polygon.holeLayer.remove(hole.plane[currentClickHole]);
            Polygon.currentClickHole = null;
            Polygon.layer.remove(region.plane);
            this.drawRegion(region.points);
            that.setBasePoints(true);
        } else if (prop === 'auxiliaryCircle') {
            that.start('circleChoice');
        } else if (prop === 'delCircle') {
            // 删除圈选
            g.layerList['圈选'].remove(that.curCircleChoiceObj);
            const name = that.curCircleChoiceObj.name;
            that.curCircleChoiceObj = null;
            const circlePoints = that.circleChoices[name]['choicePoints'];
            if (circlePoints && circlePoints.length) {
                circlePoints.forEach((item) => {
                    item.status = 'noAdd'; // todo：非待添加，而是彻底删除
                });
                that.setBasePoints();
            }
            delete that.circleChoices[name];
        } else if (prop === 'outwardRadiate') {
            Polygon.setCurOperateItem(this.radiate);
            const { points } = this.radiate;
            if (points.length) {
                this.drawRadiate(points);
            } else {
                this.setRadiateInfo();
            }
        } else if (prop === 'delPolygon') {
            const { region, hole, radiate } = this;
            Polygon.layer.remove(region.plane);
            hole.plane.forEach((item) => {
                Polygon.holeLayer.remove(item);
            });
            Polygon.currentClickHole = null;
            radiate.plane && Polygon.radiationLayer.remove(radiate.plane);
            that.planeObjs[this.i] = null;
            that.layerObj = {};
            that.setAllBaseStations();
            that.setBasePoints();
            that.$eventBus.$emit('updateSharpInfo');
        }
    }
    // 修改空洞里的基站
    handlerHolePoints(allBaseStations) {
        const g = Polygon.g;
        const preAreaList = [...this.region.points];
        const prePointList = [...allBaseStations];
        const prePositionTest = g.math.positionTest(prePointList, preAreaList);
        prePositionTest.forEach((item, index) => {
            const val = allBaseStations[index];
            if (item && val.status === 'noAdd') {
                val.status = 'added';
            }
        });
        const holePlane = Polygon.holeLayer.Group.children;
        holePlane && holePlane.forEach((item) => {
            if (!item.operateType) {
                return;
            }
            const pointList = [...allBaseStations];
            const areaList = [...item.operateType.points];
            const positionTest = g.math.positionTest(pointList, areaList);
            positionTest.forEach((value, index) => {
                const val = allBaseStations[index];
                if (value && val.status === 'added') {
                    val.status = 'noAdd';
                }
            });
        });
    }
    // 外部调用params
    static getParams(allAreas, hollowOutRegionList) {
        let regionParams;
        if (allAreas.length === 1) {
            regionParams = {
                isMultiRegion: 0,
                regionCoors: toPointSequence(allAreas[0].region.points),
                hollowOutRegionList,
            }
        } else if (allAreas.length > 1) {
            regionParams = {
                isMultiRegion: 1,
                regionList: allAreas
                    .map((area) => {
                        const regionCoors = toPointSequence(area.region.points);
                        return {
                            areaType: 2,
                            polygon: regionCoors,
                        };
                    }).filter((item) => Boolean(item.polygon)),
                hollowOutRegionList,
            }
        }
        return regionParams;
    }
    };
};


