<!-- 基站列表 -->
<template>
  <div class="base-station">
    <dataTable
      class="data-table"
      :columns="columns"
      :data="tableData"
      :total="total"
      :row-class-name="activeRowClassName"
      :max-height="442"
      :updateTable="setTableData"
      layout="total, prev, pager, next"
      @row-click="handleRowClk"
    >
      <template #LACCELL="{ row }"> {{ row.lac }}-{{ row.cell }} </template>
      <template #gen="{ row }">
        {{ String(row.gen).toUpperCase() }}
      </template>
      <template #cellBoundary="{ row }">
        {{ getBoundary(row.cellBoundary) }}
      </template>
    </dataTable>
  </div>
</template>
<script>
import { baseStationTableCols } from '@/script/constant/resourceManifest.js';
import dataTable from '@/script/components/dataTableLast.vue';
import mixin from './mixin.js';
export default {
  name: 'station',
  components: {
    dataTable,
  },
  mixins: [mixin],
  inject: ['root'],
  data() {
    return {
      columns: baseStationTableCols(this.renderHeader, false),
    };
  },
  methods: {
    handleRowClk(row) {
      if (!this.root.isCheckBase) {
        this.$message.warning('请先打开查看基站！');
        return;
      }
      if (this.curRow === row) return;
      this.curRow = row;
      this.$emit('setLocation', row);
    },
    async search() {
      const isMultiRegion = Boolean(
        this.baseInfo.multiPolygonList && this.baseInfo.multiPolygonList.length
      );
      const { resourceId, regionId } = this.outsideRow;
      const params = {
        expansion: 0,
        type: 0, //
        shapeType: this.outsideRow.shapeType, //
        regionId: resourceId || regionId,
        isMultiRegion: Number(isMultiRegion),
      };
      const { regionInnerList, regionOutList } =
        await this.$post('getRegionExpansionCells', params);
      let filterData = regionInnerList.filter((item) => item.cellType === 2);
      if (!filterData.length) {
        filterData = regionInnerList;
      }
      const innerList = filterData.map((item) => ({
        ...item,
        status: 'added',
        isArea: '是',
      }));
      const outList = regionOutList.map((item) => ({
        ...item,
        status: 'noAdd',
        isArea: '否',
      }));
      this.allTableData = innerList.concat(outList);
      this.$emit('getCurAllBases', this.allTableData);
      this.setTableData();

      console.log(this.tableData);
    },
  },
};
</script>
<style lang="less" scoped>
.base-station {
  .data-table {
    margin-top: 11px;
    /deep/ .el-table .el-table__row:hover > td {
      background-color: transparent !important;
      cursor: pointer;
    }
  }
  /deep/.custom-select {
    .el-input {
      font-size: 14px;
      & > .el-input__inner {
        height: 24px;
        line-height: 24px;
        padding-left: 0px;
        padding-right: 22px;
        border: none;
        background-color: transparent;
      }
    }
  }
}
</style>
