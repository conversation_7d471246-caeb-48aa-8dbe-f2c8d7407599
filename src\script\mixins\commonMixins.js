import { request, post, postFile, downloadFile } from '@/utils/request.js';
import { mapGetters } from 'vuex';
export default {
    computed: {
        ...mapGetters(['getUserName']),
    },
    methods: {
        /**
         * 接口请求封装
         * @param method 请求方式
         * @param postName 接口名
         * @param _param 入参
         * @param errContent 错误提示内容
         * @param callBack 成功逻辑业务代码callBack
         * @param notMainApi 是否是不关键接口，是不关键接口报错弹窗换成element
         * @param catchCallback catch时回调
         */
        async getPost(method, postName, _param, errContent, callBack,catchCallback) {
            await request(method, postName, _param, undefined, true).then((rcvData) => {
                if (this.isLoading) {
                    this.isLoading = false; //去loading
                }
                //此处成功逻辑业务代码
                callBack && callBack(rcvData);
            })
            .catch((err) => {
                if (this.isLoading) {
                    this.isLoading = false; //去loading
                }
                this.$message.error(err || errContent + '失败，请联系管理员！');
                catchCallback && catchCallback();
            });
        },
        $post: post.bind(this),
        $postFile: postFile.bind(this),
        $downloadFile: downloadFile.bind(this),
        openDialog(msg, confirmFn, showCancelButton = true, cancelFn) {
            this.$confirm(
                `<div class="dialog-title">提醒</div>
            <div>${msg}</div>`,
                {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    dangerouslyUseHTMLString: true,
                    showClose: false,
                    customClass: 'my-task-dialog',
                    type: 'warning',
                    showCancelButton: showCancelButton,
                }
            )
                .then(confirmFn)
                .catch(cancelFn);
        },
    },
};
