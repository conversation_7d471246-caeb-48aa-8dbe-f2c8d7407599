const listDisplay = [
    {
        name: '区域名称：',
        props: 'earthquakeName',
        width:283,
        span:6
    },
    {
        name: '灾害类型：',
        props: 'disasterType',
        width:256,
        span:3,
        formatter: (val) => {
            const mapLabel = {
                1: '地震',
                2: '泥石流',
                3: '水灾',
                4: '山体滑坡',
                5: '火灾',
                6: '其他'
            }
            return mapLabel[val]
        }
    },
    {
        name: '发生时间：',
        props: 'occurTime',
        width:283,
        span:5
    },
    {
        name: '时间粒度：',
        props: 'timeType',
        width:210,
        span:3
    },
    {
        name: '分析时间：',
        props: 'time',
        width:424,
        span:6
    },
];

export {
    listDisplay
}