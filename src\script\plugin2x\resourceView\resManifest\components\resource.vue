<template>
    <div class="resource">
        <div class="resource__search">
            <searchBar class="search" :form="form" :fields="formCols">
                <div class="tools">
                    <el-checkbox v-if="isExpand" v-model="form.isCollect">{{
                        checkboxTip
                    }}</el-checkbox>
                    <el-button
                        class="earth-btn-purple"
                        type="primary"
                        @click="searchResList(undefined, true)"
                        >查询</el-button
                    >
                    <el-button type="text" @click="isExpand = !isExpand">
                        <span>{{ isExpand ? '收起' : '展开' }}</span>
                        <i :class="expandIcon"></i>
                    </el-button>
                </div>
            </searchBar>
        </div>
        <div class="resource__main">
            <div class="resource-info">
                <resourceInfo
                    v-loading="isLoading"
                    element-loading-background="rgba(0, 0, 0, 0.8)"
                    ref="resList"
                    :tableData="tableData"
                    :paginationData="paginationData"
                    :isResPackage="isResPackage"
                    :isPublicRes="isPublicRes"
                    :isRegionGroup="isRegionGroup"
                    v-on="$listeners"
                    @updateTable="searchResList"
                    @clickRow="clickRow"
                    @toggleChecked="handleChecked"
                    @updateBulkType="updateBulkType"
                />
            </div>
            <div class="map">
                <gisMap
                    ref="resGis"
                    :layerList="layerList"
                    :baseInfo="isSingleResource ? baseInfo : {}"
                    :shapeType="curClkShapeType"
                    :defExpansion="3"
                    :isResPackage="isResPackage"
                    isShowInnerCity
                    isJustShowLayer
                    :regionList="checkList"
                    :isShowHideManage="isSingleResource && !isResPackage && !isBulkCheck"
                />
            </div>
        </div>
    </div>
</template>
<script>
import posImg from '@/img/space/icons/pos.png';
import imgInfo from '@/script/constant/popoverImgs.js';
import searchBar from '@/script/components/searchBar/searchBar.vue';
import gisMap from '@/script/components/gis/gisComLast.vue';
import detailInfo from '@/script/components/gis/layerResource/components/detailInfo.vue';
import operateInfo from '@/script/components/gis/layerResource/components/operateInfo.vue';
import resourceList from '@/script/components/gis/layerResource/components/resourceList.vue';
import sharpInfo from '@/script/components/gis/layerResource/components/sharpInfo.vue';
import overlapsArea from '@/script/components/gis/layerResource/components/overlapsArea.vue';
import baseStation from '@/script/components/gis/layerResource/components/baseStation/index.vue';
import resourceInfo from './resourceInfo.vue';
import { mapGetters } from 'vuex';
import {
    privateResFormCols,
    publicResFormCols,
    resPackageFormCols,
} from '@/script/constant/resourceManifest.js';
import { getAreasToCity, getLabel, getLabels, getRegions } from '@/utils/method.js';
export default {
    name: 'resource',
    components: {
        searchBar,
        gisMap,
        resourceInfo,
    },
    data() {
        return {
            form: {
                shapeType: null, // 轮廓类型
                citys: [],
                resourceId: '',
                resourcePackageId: '',
                resourcePackageName: '',
                dataSource: 1,
                resourceName: '',
                regionTypeIds: [],
                layerIds: [],
                type: 1, // 资源属性
                createTime: [],
                isCollect: false,
                describe: '', // 新增参数
                isImport: null,
            },
            tableData: [],
            paginationData: {
                pageIndex: 1,
                rowsPage: 5,
                total: 0,
            },
            isExpand: false,
            curRow: {},
            mapRequest: {
                1: {
                    method: 'queryOneRegion',
                    regionIdKey: 'regionId',
                    regionIdVal: 'resourceId',
                    regionNameVal: 'resourceName',
                },
                2: {
                    method: 'queryRegionGroupDetail',
                    regionIdKey: 'regionGroupid',
                    regionIdVal: 'resourceId',
                    regionNameVal: 'resourceName',
                },
                resPackage: {
                    method: 'queryResPackageDetail',
                    regionIdKey: 'resourcePackageId',
                    regionIdVal: 'resourcePackageId',
                    regionNameVal: 'resourcePackageName',
                },
            },
            baseInfo: {},
            curClkShapeType: 2,
            mapEvaluateType: {
                1: 'good',
                2: 'bad',
            },
            isSingleResource: false, //  标识当前点击的资源是不是单个资源
            bulkType: '',
            checkList: [],
            mapGraph: {},
            isLoading: false,
        };
    },
    computed: {
        ...mapGetters(['spaceLayers']),
        queryData() {
            return this.$route.query;
        },
        isPublic() {
            return this.queryData.isPublic === 'true';
        },
        isPrivate() {
            return this.queryData.layerName === '专有资源';
        },
        isResPackage() {
            return this.queryData.layerName === '资源包';
        },
        isPublicRes() {
            return this.queryData.isPublic === 'true';
        },
        isRegionGroup() {
            return this.form.type === 2;
        },
        isBulkCheck() {
            return this.bulkType === '批量查看';
        },
        layerList() {
            const { type: resType, isSub, shapeType } = this.curRow || {};
            const isLocatePoint = shapeType === 5;
            const isExitRow = Boolean(this.curRow && Object.keys(this.curRow).length);
            const isSingleResource = resType === 1;
            const isResPackage = resType === 'resPackage';
            const isPolygonOrCircle = [1, 2].includes(shapeType);

            if (this.isBulkCheck) {
                return [
                    {
                        label: '详细信息',
                        prop: detailInfo,
                        icons: [imgInfo.details, imgInfo.detailsActive],
                        attrs: {
                            baseInfo: this.baseInfo,
                        },
                        isShow: isExitRow,
                    },
                    {
                        label: '资源列表',
                        prop: resourceList,
                        icons: [imgInfo.areas, imgInfo.areasActive],
                        attrs: {},
                        listeners: {
                            locateToRegion: (row) => {
                                const extremum = this.mapGraph[row.resourceId].extremum;
                                this.$refs.resGis.moveCenter(extremum);
                            },
                        },
                        isShow: isExitRow,
                    },
                ];
            }
            return [
                {
                    label: '详细信息',
                    prop: detailInfo,
                    icons: [imgInfo.details, imgInfo.detailsActive],
                    attrs: {
                        baseInfo: this.baseInfo,
                    },
                    isShow: isExitRow,
                },
                {
                    label: '操作信息',
                    prop: operateInfo,
                    icons: [imgInfo.operate, imgInfo.operateActive],
                    attrs: {
                        baseInfo: this.baseInfo,
                        row: this.curRow,
                        isPrivate: this.isPrivate,
                    },
                    listeners: {
                        renderCurLayer: () => {
                            this.clickRow(this.curRow, false);
                        },
                    },
                    isShow: this.isPrivate && isSingleResource,
                },
                {
                    label: '重叠区域',
                    prop: overlapsArea,
                    icons: [imgInfo.layer, imgInfo.layerActive],
                    attrs: {
                        row: this.curRow,
                    },
                    isShow: !isResPackage && isExitRow && isPolygonOrCircle,
                },
                {
                    label: '轮廓信息',
                    prop: sharpInfo,
                    icons: [imgInfo.sharp, imgInfo.sharpActive],
                    isShow: isSingleResource && (!this.isResPackage || !isSub),
                    attrs: {
                        sharps: (this.mapGraph[this.curRow.resourceId] || {}).sharps,
                        row: {
                            ...this.curRow,
                            resourceName: this.baseInfo.resourceName,
                        },
                    },
                },
                {
                    label: '基站信息',
                    prop: baseStation,
                    icons: [imgInfo.baseStation, imgInfo.baseStationActive],
                    isShow:
                        !isLocatePoint &&
                        !this.isPublicRes &&
                        !this.isResPackage &&
                        isSingleResource &&
                        !isSub,
                    attrs: {
                        baseInfo: this.baseInfo,
                        outsideRow: { ...this.curRow, icon: posImg },
                    },
                },
            ];
        },
        detailInfo() {
            return this.layerList.find((layer) => layer.label === '详细信息');
        },
        formCols({ isExpand, form }) {
            const { spaceAreas, spaceLayers, spaceDistricts } = this.$store.getters;
            const mapOpts = {
                citys: getAreasToCity(spaceDistricts, true),
                regionTypeIds: spaceAreas,
                layerIds: spaceLayers,
            };
            if (this.isPrivate) {
                return privateResFormCols(isExpand, form.type, mapOpts);
            } else if (this.isPublic) {
                return publicResFormCols(isExpand, mapOpts);
            } else if (this.isResPackage) {
                return resPackageFormCols(isExpand);
            }
            return [];
        },
        expandIcon() {
            return this.isExpand ? 'el-icon-arrow-up' : 'el-icon-arrow-down';
        },
        userInfo() {
            return {
                id: 1,
                name: 'admin',
                describe: '系统管理员',
            };
        },
        checkboxTip() {
            return this.isResPackage ? '只查看收藏资源包' : '收藏区域';
        },
    },
    watch: {
        'form.type': {
            handler() {
                this.form.regionTypeIds = [];
                this.isSingleResource = false;
                this.searchResList();
                if (this.$refs.resGis) {
                    this.$refs.resGis.clearAll();
                }
            },
            immediate: true,
        },
    },
    mounted() {
        this.searchResList();
    },
    methods: {
        async searchResList(paginationData = { pageIndex: 1, rowsPage: 15 }, isManualClick) {
            this.isLoading = true;
            if (isManualClick) {
                this.$eventBus.$emit('closeLayer', () => {
                    if (this.isResPackage || this.isBulkCheck) {
                        this.removeAllGraph();
                    } else {
                        this.$refs.resGis.clearAll(true);
                    }
                });
            }

            Object.assign(this.paginationData, paginationData);
            const [createStartTime, createEndTime] = this.form.createTime || [];
            const { pageIndex, rowsPage } = this.paginationData;
            const citys = this.form.citys;
            const cities = citys.length ? citys.filter((val) => !val.includes('all')) : [];
            const params = {
                pageIndex,
                rowsPage,
                createStartTime,
                createEndTime,
                ...this.form,
                resourcePackageId: parseInt(this.form.resourcePackageId) || null,
                citys: cities,
                isCollect: Number(this.form.isCollect) || null,
                createTime: undefined, // 删除createTime属性
            };

            let urlName;
            if (this.isPublic) {
                Object.assign(params, {
                    layerId: this.queryData.layerId,
                    layerIds: undefined,
                });
                if (this.queryData.resourceId) {
                    params.resourceId = this.queryData.resourceId;
                }
                urlName = 'getNewPublicResourceList';
            } else if (this.isPrivate) {
                const shapeType = this.form.shapeType;
                // 专有资源特殊入参
                Object.assign(params, {
                    resourcePackageId: undefined,
                    resourcePackageName: undefined,
                    dataSource: undefined,
                    isValid: 1,
                    regionType: 1,
                    shapeType: shapeType ? [shapeType] : null,
                });
                urlName = 'getNewPrivateResourceList';
            } else if (this.isResPackage) {
                Object.assign(params, {
                    shapeType: undefined,
                    layerIds: undefined,
                });
                urlName = 'getPackageResourceList';
            }

            const { total, list } = await this.$post(urlName, params);
            const { type } = this.form;

            this.isLoading = false;

            this.tableData = list.map((item) => {
                const curGraph = this.mapGraph[item.resourceId];
                const newItem = {
                    ...item,
                    isCollect: Boolean(item.isCollect),
                    isChecked: Boolean(curGraph && curGraph.isChecked),
                };
                if (this.isPrivate) {
                    newItem.type = type;
                } else if (this.isPublic) {
                    newItem.type = 1; // 公共资源只有单个资源
                    const key = this.mapEvaluateType[item.evaluateType];
                    if (key) {
                        newItem[key] = true;
                    }
                } else if (this.isResPackage) {
                    Object.assign(newItem, {
                        type: 'resPackage',
                        children: [],
                    });
                }
                return newItem;
            });
            this.paginationData.total = total;
            // 如果是公共资源 && 来自运营分析-资源使用排名
            if (this.queryData.isFromResUsageRank) {
                let timer = setTimeout(() => {
                    this.$refs.resList.handleRowClk(this.tableData[0]);
                    clearTimeout(timer);
                }, 600);
            }
        },
        handleChecked(status, row) {
            const curGraph = this.mapGraph[row.resourceId];
            // 是否选中已缓存、是否选中为缓存, 是否选中已点击项
            // 选中
            if (status) {
                // 已缓存
                if (curGraph) {
                    // 选中已点击项且未渲染
                    if (this.curRow === row && !curGraph.isRender) {
                        curGraph.sharps.forEach((sharp) => sharp.drawRegion());
                        curGraph.isRender = true;
                        this.$eventBus.$emit('openLayer', this.detailInfo);
                        this.$refs.resGis.moveCenter(curGraph.extremum);
                    } else {
                        // 新项或已渲染
                        this.clickRow(row);
                    }
                } else {
                    // 未缓存
                    this.clickRow(row);
                }
            } else {
                // 未选中（一定缓存过）
                curGraph.sharps.forEach((sharp) => sharp.clear());
                curGraph.isRender = false;
            }
            if (curGraph) {
                curGraph.isChecked = status;
            }
            this.checkList = this.tableData.filter((item) => item.isChecked);
        },
        async clickRow(row, isValidRepeat = true) {
            if (isValidRepeat && row === this.curRow) return;
            this.isSingleResource = row.type === 1; // gisMap获取baseInfo，与基站显示相关
            if (!this.isBulkCheck) {
                this.curClkShapeType = this.isSingleResource ? +row.shapeType : 2;
            }

            const isResPackageChild = 'parentId' in row;
            if (isResPackageChild) {
                if (this.curRow.parentId !== row.parentId) {
                    this.mapGraph = {};
                    this.removeAllGraph();
                }
            }
            // 上一次点击项未选中复选框
            const curGraph = this.mapGraph[(this.curRow || {}).resourceId] || {};
            if (!this.curRow.isChecked && curGraph.baseInfo) {
                if (curGraph.isRender) {
                    curGraph.sharps.forEach((sharp) => sharp.clear());
                    curGraph.isRender = false;
                }
            }
            this.curRow = row;
            if (this.isBulkCheck && this.mapGraph[row.resourceId]) {
                const curGraph = this.mapGraph[row.resourceId];
                const { baseInfo, sharps, extremum, isRender } = curGraph;
                if (!isRender) {
                    sharps.forEach((sharp) => sharp.drawRegion());
                    curGraph.isRender = true;
                }
                this.baseInfo = baseInfo;
                this.$eventBus.$emit('closeLayer');
                this.$eventBus.$emit('openLayer', this.detailInfo);
                this.$refs.resGis.moveCenter(extremum);
            } else {
                this.setDetailInfo(row, async (baseInfo) => {
                    let regionInfo;
                    const resType = row.type;
                    if (resType === 1) {
                        // 单个资源
                        if (this.isBulkCheck || isResPackageChild) {
                            regionInfo = this.$refs.resGis.setRegionGroup([baseInfo], {
                                setCurDetail: () => this.setCurDetail(row, baseInfo),
                            });
                        } else {
                            const regions = getRegions(baseInfo, row.shapeType);
                            this.$refs.resGis.DrawArea.initRegionCoors(regions, true, true);
                        }
                    } else if (resType === 2) {
                        // 区域组时
                        const { id, name } = this.userInfo;
                        const res = await this.$post('queryZoneGroupDetail', {
                            regionGroupid: row.resourceId,
                            pageSize: 999999999,
                            userId: id,
                            userName: name,
                        });
                        const regionList = res.regionGroupList[0].regionList;
                        regionInfo = this.$refs.resGis.setRegionGroup(regionList);
                    }
                    if (regionInfo && (this.isBulkCheck || isResPackageChild)) {
                        this.mapGraph[row.resourceId] = {
                            sharps: regionInfo.sharps || [],
                            extremum: regionInfo.extremum,
                            baseInfo,
                            isRender: true,
                            isChecked: Boolean(row.isChecked),
                        };
                    }
                    this.$eventBus.$emit('closeLayer');
                    this.$nextTick(() => {
                        this.$eventBus.$emit('openLayer', this.detailInfo);
                    })
                });
            }
        },
        async setDetailInfo(row, callback) {
            const {
                resourceId,
                resourcePackageId,
                regionId,
                resourceName,
                resourcePackageName,
                regionName,
                shapeType,
                type,
                regionType,
            } = row;
            const { id, name } = this.userInfo;
            const { method, regionIdKey } = this.mapRequest[type || regionType];
            const params = {
                userId: id,
                userName: name,
                [regionIdKey]: resourceId || resourcePackageId || regionId,
            };

            this.baseInfo = {
                type,
                shapeType,
                resourceName: resourceName || resourcePackageName || regionName,
            };
            const res = await this.$post(method, params);
            this.setBaseInfo(this.baseInfo, res, row.type);

            callback(this.baseInfo);
        },
        setBaseInfo(baseInfo, res, resType) {
            if (resType === 1) {
                Object.assign(baseInfo, {
                    ...res,
                    ...res.attributes,
                    createTime: (res.createTime || '').substr(0, 19),
                    lastUpdateTime: (res.lastUpdateTime || '').substr(0, 19),
                });
            } else if (resType === 2) {
                const result = (res.regionGroupList && res.regionGroupList[0]) || {};
                Object.assign(baseInfo, {
                    regionId: result.regionGroupId,
                    layerIds: getLabel(result.layerId, this.spaceLayers),
                    regionArea: result.regionGroupArea,
                    layerStatus: result.openStatus,
                    describe: result.regionGroupDescribe,
                    ...result,
                });
            } else if (resType === 'resPackage') {
                // todo- 资源包-详情信息
                Object.assign(baseInfo, {
                    regionId: res.resourcePackageId,
                    layerIDs: getLabels(res.layerId, this.spaceLayers),
                    shapeType: res.shapeType,
                    resourceDescribe: res.resourceDescribe,
                    resourceSource: res.resourceSource,
                });
            }
        },
        removeAllGraph() {
            this.$refs.resGis.removeAllSharps();
        },
        setCurDetail(row, baseInfo) {
            this.curRow = row;
            this.$refs.resList.curRow = row;
            this.baseInfo = baseInfo;
            this.$eventBus.$emit('closeLayer');
            this.$eventBus.$emit('openLayer', this.detailInfo);
        },
        updateBulkType(newVal) {
            if (['批量查看', ''].includes(this.bulkType)) {
                this.removeAllGraph();
            }
            this.bulkType = newVal;
            this.curRow = {};
            this.$eventBus.$emit('closeLayer');
            // 图层信息重置
            this.mapGraph = {};
            this.tableData.forEach((item) => {
                item.isChecked = false;
            });
        },
        back() {
            this.$router.back();
        },
    },
};
</script>
<style lang="less" scoped>
.resource {
    display: flex;
    flex-direction: column;
    height: 100%;
    &__search {
        padding: 0 16px;
        background: linear-gradient( 270deg, #101620 0%, #1B2F4D 100%);
        .search {
            padding-top: 8px;
        }
        .tools {
            display: flex;
            .el-checkbox {
                margin: 0 16px 0 0;
                /deep/ .el-checkbox__label {
                    padding-left: 6px;
                    vertical-align: middle;
                }
                /deep/ .el-checkbox__input {
                    vertical-align: sub;
                }
            }
        }
    }
    &__main {
        margin: 18px;
        display: flex;
        flex: 1;
        height: 0;
        border-radius: 6px;
        .resource-info {
            height: 100%;
            width: 360px;
        }
        .map {
            position: relative;
            width: calc(100% - 360px);
            &::before {
                content: '';
                display: block;
                position: absolute;
                left: 0;
                top: 0;
                width: 9px;
                height: 100%;
                background: linear-gradient(90deg, rgba(0, 0, 0, 0.16) 0%, rgba(0, 0, 0, 0) 100%);
                opacity: 0.74;
                z-index: 1;
            }
        }
    }
}
.w-auto {
    width: auto;
}
</style>
