import router from './script/router';
import NProgress from 'nprogress';
import 'nprogress/nprogress.css';
import { request } from '@/utils/request.js';
export default router;
window.permission = process.env.VUE_APP_PERMISSION;
if(window.permission === 'true'){
	router.beforeEach((to, from, next) => {
		NProgress.start();
		let storage = localStorage.getItem('token');
		let { page,token } = to.query;
		if(to.path === '/errorPage'){
			next();
		}
		else if(token || storage){
			token && (localStorage.setItem('token',token))
			request('get', 'verify', {token:localStorage.getItem('token')}).then((rcvData) => {
				const query = { ...to.query };
				delete query.page;
				delete query.token;
				if (page) {
					const route = {
						name: page,
						replace: true
					};
					route.query = { outside: true, ...query };
					next(route);
					NProgress.done();
				} else {
					next();
				}
			})
			.catch((err) => {
				next('/errorPage');
				NProgress.done();
			});
		}
		else{
			next('/errorPage');
			NProgress.done();
		}
	})
}else{
	router.beforeEach((to, from, next) => {
		NProgress.start();
		let { page } = to.query;
		const query = { ...to.query };
		delete query.page;
		if (page) {
			const route = {
				name: page,
				replace: true
			};
			route.query = { outside: true, ...query };
			next(route);
			NProgress.done();
		} else {
			next();
		}
	});
}

/**
 * 路由跳转之后执行该函数
 */
router.afterEach(() => {
  	NProgress.done()
})