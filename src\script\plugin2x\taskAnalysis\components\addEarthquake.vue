<template>
    <el-dialog
        class="bulk-edit"
        top="250px"
        width="700px"
        append-to-body
        :visible="visible"
        :close-on-click-modal="false"
        @open="handleOpen"
        @close="handleClose"
    >
        <template #title>
            <div class="bulk-edit__title">
                <span class="title">新增地震</span>
            </div>
        </template>
        <div class="bulk-edit__body">
            <searchBar class="search-form" :fields="fields" :form="form"> </searchBar>
        </div>
        <!-- 底部 -->
        <div slot="footer" class="dialog-footer">
            <el-button size="small" @click="handleClose">取消</el-button>
            <el-button type="primary" size="small" @click="save">保存</el-button>
        </div>
    </el-dialog>
</template>
<script>
import searchBar from '_com/searchBar/searchBar.vue';
import { addEarthquakeFields } from '_const/creation.js';
export default {
    name: 'add-earthquake',
    components: {
        searchBar,
    },
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            form: {
                cataId: '',
                location: '',
                epiLon: '',
                epiLat: '',
                epiDepth: '',
                level: '',
                time: '',
            },
        };
    },
    computed: {
        fields() {
            return addEarthquakeFields(this.handleDepth, this.handleLevel);
        },
    },
    mounted() {},
    methods: {
        handleOpen() {},
        handleClose() {
            this.$emit('update:visible', false);
        },
        async save() {
            // 校验必填字段
            // 校验必填字段
            if (
                !this.form.cataId ||
                !this.form.location ||
                !this.form.epiLon ||
                !this.form.epiLat ||
                !this.form.level ||
                !this.form.epiDepth ||
                !this.form.time
            ) {
                this.$message.error('请填写完整信息');
                return;
            }
            this.getPost('post', 'addEarthquake', this.form, '创建任务接口请求', () => {
                this.$message({
                    message: '新增地震成功！',
                    type: 'success',
                });
                this.handleClose();
                this.$emit('updateTable');
            });
        },
        handleDepth(val) {
            // 限制只能输入数字
            if (val && !/^\d*\.?\d*$/.test(val)) {
                this.form.epiDepth = val.replace(/[^\d.]/g, '');
            }
        },
        handleLevel(val) {
            // 限制只能输入数字且小数点后只能有一位
            if (val) {
                // 先移除非数字和小数点字符
                let newVal = val.replace(/[^\d.]/g, '');
                // 确保只有一个小数点
                const parts = newVal.split('.');
                if (parts.length > 2) {
                    newVal = parts[0] + '.' + parts.slice(1).join('');
                }
                // 限制小数点后只能有一位
                if (parts.length === 2 && parts[1].length > 1) {
                    newVal = parts[0] + '.' + parts[1].substring(0, 1);
                }
                this.form.level = newVal;
            }
        },
    },
};
</script>

<style lang="less" scoped>
.bulk-edit {
    &__title {
        display: flex;
        align-items: center;
        .title {
            font-size: 18px;
            font-family: AlibabaPuHuiTi, AlibabaPuHuiTi;
            font-weight: 600;
            color: #fff;
            line-height: 22px;
            margin-right: 10px;
        }

        img {
            margin-right: 5px;
        }

        .subTitle {
            font-family: AlibabaPuHuiTi, AlibabaPuHuiTi;
            font-weight: 400;
            font-size: 12px;
            color: #666666;
            line-height: 16px;
        }
    }
    &__body {
        .form {
            .item {
                display: flex;
                align-items: center;
                height: 32px;
                .name {
                    display: inline-block;
                    width: 157px;
                    font-family: AlibabaPuHuiTi, AlibabaPuHuiTi;
                    font-weight: 400;
                    font-size: 14px;
                    color: rgba(255, 255, 255, 0.85);
                }
                .custom-select {
                    flex: 1;
                    /deep/ .el-input__inner {
                        background: rgba(0, 20, 46, 0.3);
                        box-shadow: inset 0px 0px 8px 0px #4984ff;
                        border-radius: 4px;
                        border: 1px solid #a8bfe8;
                    }
                }
            }
            .tip {
                display: flex;
                align-items: center;
                margin-bottom: 20px;
                line-height: 32px;
                font-weight: 400;
                font-size: 14px;
                color: rgba(255, 255, 0255, 0.65);
                font-family: AlibabaPuHuiTi, AlibabaPuHuiTi;
                background: linear-gradient(
                    180deg,
                    rgba(0, 145, 255, 0.1) 0%,
                    rgba(0, 145, 255, 0.25) 100%
                );
                border-radius: 4px;
                i {
                    margin: 0 4px 0 10px;
                    color: #0091ff;
                }
            }
        }
    }
    /deep/ .el-dialog {
        box-shadow: 0px 6px 30px 0px rgba(0, 0, 0, 0.05), 0px 16px 24px 2px rgba(0, 0, 0, 0.04),
            0px 8px 10px -5px rgba(0, 0, 0, 0.08);
        border-radius: 10px;
        background: rgba(31, 52, 79, 0.8);
        box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.65);
        border-radius: 8px;
        backdrop-filter: blur(8px);
    }
    /deep/ .el-dialog__header {
        color: #fff;
        padding: 16px 20px 16px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.3);
    }
    /deep/ .el-dialog__body {
        padding: 20px 20px 8px 20px;
    }
    /deep/ .el-dialog__footer {
        padding: 12px 16px 18px;
    }
}
</style>
