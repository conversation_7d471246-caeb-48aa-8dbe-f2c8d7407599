<!-- 数据表格 -->
<template>
    <div class="data-table" ref="tables">
        <el-table
            ref="elTableRef"
            :size="size"
            :header-cell-style="theadColor"
            style="width: 100%"
            v-bind="$attrs"
            v-on="$listeners"
        >
            <!-- 自定义空内容 -->
            <template #empty>
                <slot name="empty"></slot>
            </template>
            <!-- 列 -->
            <el-table-column v-if="isSelectable" type="selection" reserve-selection width="36" />
            <el-table-column
                v-for="item in columns"
                v-bind="item"
                v-slot="{ row, $index }"
                :key="item.prop"
            >
                <slot :name="item.prop" :row="row" :inx="$index">
                    {{ row[item.prop] }}
                </slot>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <div
            v-if="total > pagination.pageSizes[0]"
            class="wrap-pagination"
            :class="{ hideUpLine: isHideUpLine }"
        >
            <slot name="pagination">
                <el-pagination
                    class="pagination"
                    :current-page="pagination.curPage"
                    :page-sizes="pagination.pageSizes"
                    :page-size="pagination.pageSize"
                    :total="total"
                    :layout="layout"
                    :pager-count="pagination.pagerCount"
                    background
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                />
            </slot>
        </div>
    </div>
</template>

<script>
export default {
    name: 'data-table',
    inheritAttrs: false,
    props: {
        columns: {
            type: Array,
            default: () => [],
            required: true,
        },
        size: {
            type: String,
            default: 'small',
        },
        theadStyle: {
            type: Object,
            default: () => ({}),
        },
        updateTable: {
            type: Function,
            default: () => {},
            validator(value) {
                return typeof value === 'function';
            },
        },
        total: {
            type: Number,
            validator: (value) => {
                return value >= 0;
            },
        },
        layout: {
            type: String,
            default: 'total, prev, pager, next, sizes, jumper',
        },
        isSelectable: {
            type: Boolean,
            default: false,
        },
        pagination: {
            type: Object,
            default: () => ({
                curPage: 1,
                pageSize: 15,
            }),
        },
        isHideUpLine: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {};
    },
    computed: {
        theadColor() {
            return {
                background: '#1B293B',
                color: '#9AAFC1',
                fontWeight: 'bold',
                'border-radius': 0,
                boxShadow: 'inset 0px -1px 0px 0px rgba(0,0,0,0.08)',
                borderRadius: '2px 2px 0px 0px',
                fontSize: '14px',
                ...this.theadStyle,
            };
        },
    },
    created() {
        this.initPagination();
    },
    methods: {
        initPagination() {
            const { pageSize, pageSizes, pagerCount } = this.pagination;
            const mapPageSizes = {
                10: [10, 15, 25, 40],
                15: [15, 20, 30, 50],
            };
            if (!pageSizes || !pageSizes.length) {
                this.pagination.pageSizes = mapPageSizes[pageSize];
            }
            if (!pagerCount) {
                this.pagination.pagerCount = 7;
            }
        },
        handleSizeChange(pageSize) {
            Object.assign(this.pagination, {
                curPage: 1,
                pageSize,
            });
            this.updateTable({
                curPage: 1,
                pageSize,
            });
        },
        handleCurrentChange(curPage) {
            this.pagination.curPage = curPage;
            this.updateTable({
                curPage,
                pageSize: this.pagination.pageSize,
            });
        },
        scrollToRow(className = '.success-row') {
            this.$nextTick(() => {
                const tableRef = this.$refs.elTableRef;
                // 找到对应的 tr 元素
                const rowEl = tableRef.$el.querySelector(className);
                if (rowEl) {
                    // 滚动到该行
                    rowEl.scrollIntoView({ block: 'center', inline: 'nearest' });
                }
            });
        },
    },
};
</script>

<style lang="less" scoped>
.data-table {
    height: 100%;
    display: flex;
    flex-flow: column;
    .el-table {
        flex: 1;
        width: 100%;
        background-color: transparent;
        &::before {
            display: none;
        }
        .el-table__header {
        }
        /*    display: flex;
    flex-direction: column; */
        /deep/ .el-table__header-wrapper .gutter {
            background: unset;
        }
        /deep/.el-table__body-wrapper {
            // flex: 1;
            height: calc(100% - 40px);
            overflow-y: auto;
            &::-webkit-scrollbar {
                width: 3px;
                height: 3px;
            }
            &::-webkit-scrollbar-thumb {
                border-radius: 10px;
                box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
                background: #5c6f92;
            }
            &::-webkit-scrollbar-track {
                /* 滚动条里面轨道 */
                box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
                border-radius: 10px;
                background: transparent;
            }
            &::-webkit-scrollbar-corner {
                background: rgba(0, 0, 0, 0);
            }
            .el-table__row {
                box-shadow: inset 0px -1px 0px 0px rgba(0, 0, 0, 0.08);
                opacity: 0.7;
                &:nth-child(odd) > td {
                    background: #101d33;
                }
                &:nth-child(even) > td {
                    background: #1b293b;
                }
                &:hover > td {
                    background-color: #040a1e !important;
                    cursor: pointer;
                }
                &.success-row {
                    .el-table__cell {
                        background-color: #040a1e !important;
                    }
                    &:hover > td {
                        background-color: #040a1e !important;
                        cursor: pointer;
                    }
                }
                .cell {
                    font-size: 14px;
                }
                &.custom-cell {
                    padding: 4px 0;
                    .cell {
                        padding-left: 4px;
                        padding-right: 4px;
                        line-height: 20px;
                    }
                }
            }
            .el-table__body {
                width: 100% !important;
            }
        }
        /deep/ td.el-table__cell,
        /deep/ th.el-table__cell {
            color: #ffffff;
            border-top: none !important;
            border-bottom: none !important;
        }
    }
    .wrap-pagination {
        text-align: right;
        height: 40px;
        .pagination {
            margin: 8px 0;
            padding-right: 0;
            /deep/.btn-prev,
            /deep/ .btn-next {
                color: #fff;
                background: unset;
                border: none;
            }
            /deep/ .el-pager {
                background-color: transparent;
                .number,
                .el-icon {
                    color: #ffffff;
                    background: unset;
                    font-weight: 500;
                    &.active {
                        background: rgba(117, 163, 223, 0);
                        border-radius: 3px;
                        border: 1px solid #a8bfe8;
                    }
                }
            }
            /deep/ .el-pagination__sizes {
                margin-right: 0;
                .el-input__inner {
                    font-size: 12px;
                    color: #fff;
                    background: #1f3459;
                    box-shadow: inset 0px 0px 7px 0px rgba(73, 132, 255, 0.5);
                    border-radius: 3px;
                    border: 1px solid #a8bfe8;
                }
            }
            /deep/ .el-pagination__total {
                color: #fff;
            }
        }
    }
    .hideUpLine {
        margin-top: 5px;
        border-top: none;
    }
}
</style>
