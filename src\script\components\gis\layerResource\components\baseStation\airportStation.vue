<!-- 基站列表 -->
<template>
  <div class="airport-station">
    <dataTable
      class="data-table"
      :columns="columns"
      :data="tableData"
      :total="total"
      :row-class-name="activeRowClassName"
      :max-height="442"
      :updateTable="setTableData"
      layout="total, prev, pager, next"
      @row-click="handleRowClk"
    >
      <template #LACCELL="{ row }"> {{ row.lac }}-{{ row.cell }} </template>
      <template #gen="{ row }">
        {{ String(row.netType).toUpperCase() }}
      </template>
      <template #cellBoundary="{ row }">
        {{ getBoundary(row.cellBoundary) }}
      </template>
    </dataTable>
  </div>
</template>
<script>
import { baseStationTableCols } from '@/script/constant/resourceManifest.js';
import dataTable from '@/script/components/dataTableLast.vue';
import mixin from './mixin.js';
import _ from 'lodash';
export default {
  name: 'station',
  components: {
    dataTable,
  },
  mixins: [mixin],
  inject: ['root', 'setAirportPoints'],
  data() {
    return {
      columns: baseStationTableCols(this.renderHeader, false),
    };
  },
  methods: {
    handleRowClk(row) {
      if (!this.root.isCheckBase) {
        this.$message.warning('请先打开查看基站！');
        return;
      }
      if (this.curRow === row) return;
      this.curRow = row;
      this.$emit('setLocation', row, this.allTableData, true);
    },
    async search() {
      const { resourceId, regionId } = this.outsideRow;
      const res = await this.$post('getAirportLac', {
        regionId: String(resourceId || regionId || '').replace(/^\D+/, ''),
        needName: 0,
      });
      this.allTableData = (res || []).map((item) => {
        const codes = (item.cgiCode || '').split('_');
        const [lac, cell] = codes.slice(1);
        return {
          ...item,
          lac,
          cell,
          lat: item.cgiLat,
          lng: item.cgiLng,
          cellLongitude: item.cgiLng,
          cellLatitude: item.cgiLat,
          ht: 20,
          dir: 0,
          size: 30,
          isArea: '是',
        };
      });
      this.$emit('getCurAllBases', this.allTableData);
      this.setAirportPoints(
        _.uniqWith(this.allTableData, (a, b) => {
          return a.lat === b.lat && a.lng === b.lng;
        })
      );
      this.setTableData();
    },
  },
};
</script>
<style lang="less" scoped>
.airport-station {
  .data-table {
    margin-top: 11px;
    /deep/ .el-table .el-table__row:hover > td {
      background-color: transparent !important;
      cursor: pointer;
    }
  }
  /deep/.custom-select {
    .el-input {
      font-size: 14px;
      & > .el-input__inner {
        height: 24px;
        line-height: 24px;
        padding-left: 0px;
        padding-right: 22px;
        border: none;
        background-color: transparent;
      }
    }
  }
}
</style>
