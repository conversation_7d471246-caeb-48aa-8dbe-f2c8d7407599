<template>
    <div class="time-line-wrapper">
        <div class="time">
            <img :src="require('../../../img/icon/time.png')" alt="" /> &nbsp;&nbsp; 热力图时间选择
        </div>
        <div class="line">
            <div
                :style="{ width: 100 / data.length + '%' }"
                class="line-item"
                :class="{ 'is-active': hasActive(index) }"
                v-for="(item, index) in data"
                :key="index"
                @click="timeLineChangeAction(item, index)"
            >
                <div class="colorBlocks"></div>
                <span class="text" :class="{ hight: currentIndex === index }">{{
                    timeSplit(item)
                }}</span>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'timeLine',
    props: {
        data: {
            type: Array,
            default: () => [],
        },
    },
    data() {
        return {
            currentIndex: 0,
        };
    },
    computed: {
        timeSplit() {
            return function (val) {
                const data = val.split(' ');
                if (data.length > 1) {
                    return `${data[0]}\n ${data[1]}`;
                }
                return data[0];
            };
        },
        hasActive() {
            return function (val) {
                if (val === this.currentIndex) {
                    return true;
                }
                return false;
            };
        },
    },
    methods: {
        timeLineChangeAction(item, index) {
            this.$emit('timeLineChangeAction', item, () => {
                this.currentIndex = index;
            });
        },
    },
};
</script>

<style lang="less" scoped>
.time-line-wrapper {
    width: 80%;
    height: 100%;
    padding: 20px 20px 10px;
    background: rgba(255, 255, 255, 0.9);
    box-shadow: 0px 4px 14px 4px rgba(0, 0, 0, 0.3);
    border-radius: 2px;
    backdrop-filter: blur(18px);
    .time {
        display: flex;
    }
    .line {
        width: 100%;
        height: calc(100% - 20px);
        margin-top: 10px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        overflow-x: auto;
        &::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }
        &::-webkit-scrollbar-thumb {
            border-radius: 10px;
            box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
            background: #ccc;
        }
        &::-webkit-scrollbar-track {
            /* 滚动条里面轨道 */
            box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
            border-radius: 10px;
            background: transparent;
        }
        &::-webkit-scrollbar-corner {
            background: rgba(0, 0, 0, 0);
        }
        &-item {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            min-width: 80px;
            cursor: pointer;
            &.is-active {
                .colorBlocks {
                    background: #60aeff;
                }
            }
            .colorBlocks {
                width: 90%;
                height: 10px;
                border-radius: 4px;
                background: #e7e7e7;
            }
            .text {
                color: #979b98;
                font-size: 11px;
                margin-top: 5px;
                white-space: pre-wrap;
                &.hight {
                    color: #60aeff;
                }
            }
        }
    }
}
</style>
