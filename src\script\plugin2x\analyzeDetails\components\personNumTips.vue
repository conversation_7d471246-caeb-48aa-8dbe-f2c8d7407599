<template>
    <div class="num-tips-body">
        <div class="tips-item" v-for="(tips, index) in tipsData" :key="index">
            <img :src="tips.icon" alt="" />
            <span class="tips-gray-text">{{ tips.label }}:</span>
            <span class="tips-white-text">{{ tips.value }}</span>
            <span class="tips-gray-text">人</span>
        </div>
    </div>
</template>

<script>
export default {
    name: 'personNumTips',
    props: {
        tipsData: {
            type: Array,
            default: () => [],
        },
    },
    data() {
        return {};
    },
};
</script>

<style lang="less" scoped>
.num-tips-body {
    align-items: center;
    gap: 20px;
    display: flex;
    background: rgba(23, 42, 65, 0.6);
    box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.65);
    border-radius: 8px;
    padding: 7px 12px;

    .tips-item {
        display: flex;
        align-items: center;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 16px;
        color: #9aafc1;
        gap: 8px;

        img {
            width: 28px;
            height: 33px;
        }

        .tips-white-text {
            font-weight: 500;
            color: #ffffff;
        }
    }
}
</style>
