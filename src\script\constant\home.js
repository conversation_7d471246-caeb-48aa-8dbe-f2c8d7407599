const tableColumn = [
    {
        prop: 'earthquakeName',
        label: '参考位置',
        // width: 200,
    },
    {
        prop: 'centerLon',
        label: '经度（°）',
    },
    {
        prop: 'centerLat',
        label: '纬度（°）',
    },
    {
        prop: 'earthquakeLevel',
        label: '震级（M）',
        width:90
    },
    {
        prop: 'earthquakeDepth',
        label: '深度(千米)',
        width:60
    },
    {
        prop: 'occurTime',
        label: '发震时刻（UTC+8）',
        width: 180,
        showOverflowTooltip: false,
    },
    {
        prop:'操作',
        label: '操作',
    }
];
const quickEntrance = [
    {
        name: '任务创建',
        value: 'creation',
        icon: 'taskCreate',
    },
    {
        name: '我的任务',
        value: 'myTask',
        icon: 'myCreated',
    },
    {
        name: '分析报告',
        value: 'analyzeProduct',
        icon: 'analyze',
    },
];

const platformOverview = [
    {
        name: '近一月地震发生次数',
        count: '-',
        unit: '次',
        props: 'earthquakeCnt',
    },
    {
        name: '正在分析任务数',
        count: '-',
        unit: '个',
        props: 'analysisTaskCnt',
    },
    {
        name: '已完成任务数',
        count: '-',
        unit: '个',
        props: 'finishedTaskCnt',
    },
];
const expandData = [
    {
        prop: 'earthquakeScaleLevel',
        label: '地震震级',
        attrs: {
            clearable: true,
            placeholder: '请选择',
        },
        element: 'el-select',
        slot: {
            element: 'el-option',
            enums: [
                { label: '0-3级', value: 1 },
                { label: '3-6级', value: 2 },
                { label: '6级以上', value: 3 },
            ],
        },
        span: 7,
    },
    {
        prop: 'time',
        label: '发震时刻',
        element: 'el-date-picker',
        attrs: {
            clearable: true,
            type: 'daterange',
            'range-separator': '-',
            'start-placeholder': '开始日期',
            'popper-class':"earth-picker",
            'end-placeholder': '结束日期',
            format: 'yyyy-MM-dd',
            'value-format': 'yyyy-MM-dd 00:00:00',
        },
        span: 12,
    },
];
const formCols = (isOpen) => {
    const list = [
        {
            element: 'el-input',
            prop: 'earthquakeName',
            label: '参考位置',
            attrs: {
                clearable: true,
                placeholder: '请输入参考位置',
            },
            span: 9,
        },
        {
            prop: 'earthquakeDepthLevel',
            label: '震源深度',
            attrs: {
                clearable: true,
                placeholder: '请选择',
            },
            element: 'el-select',
            slot: {
                element: 'el-option',
                enums: [
                    { label: '0-10千米', value: 1 },
                    { label: '10-20千米', value: 2 },
                    { label: '20-30千米', value: 3 },
                    { label: '30千米以上', value: 4 },
                ],
            },
            span: 8,
        },
        { span: 3 },
        { span: 3,prop: 'showExpand',itemClassName:'not-border'},
    ];
    if(isOpen){
        list.splice(2, 0, expandData[0],expandData[1]);
    }
    return list;
};

export { tableColumn, quickEntrance, platformOverview, formCols };
