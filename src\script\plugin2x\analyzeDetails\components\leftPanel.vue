<template>
    <card :img="require(`../../../../img/common/keyAreas.png`)" title="重点网格">
        <dataTable
            class="data-table"
            :columns="columns"
            stripe
            :data="tableData"
            @row-click="rowClick"
        >
            <template #top="{ row }">
                <div class="TOP">
                    <img :src="require(`../../../../img/common/TOP${row.top<=3?row.top:4}.png`)">
                    <span>TOP.{{row.top}}</span>
                </div>
            </template>
            <template #centralLatitudeAndLongitude="{ row }">
                <div v-if="row.centerX && row.centerY">
                    <span>{{row.centerX}},{{row.centerY}}</span>
                </div>
            </template>
        </dataTable>
    </card>
</template>

<script>
import card from '_com/card/index.vue';
import dataTable from '_com/table/dataTable.vue';
export default {
    name:'leftPanel',
    components:{
        card,
        dataTable
    },
    props:{
        tableData:{
            type:Array,
            default:() => new Array(10).fill(
                {
                    address:'汶川县城',
                    centralLatitudeAndLongitude:'134.32,37.55',
                    laccellCnt:'22',
                    accPepCnt:'876543'
                }
            ).map((item,index) => {
                return{
                    ...item,
                    top:index+1,
                }
            })
        },
        type:{
            type:String,
            default:'受影响人群',
        }
    },
    data(){
        return{
            columnsDefault:[
                {
                    prop: 'top',
                    label: 'TOP',
                    widthT:60,
                },
                {
                    prop: 'address',
                    label: '网格名称',
                },
                {
                    prop: 'centralLatitudeAndLongitude',
                    label: '中心经纬度',
                    widthT:130,
                },
                {
                    prop: 'laccellCnt',
                    label: '基站数量',
                    widthT:55,
                },
                {
                    prop: 'accPepCnt',
                    label: '受影响人群',
                    widthT:60,
                },
            ]
        }
    },
    computed:{
        columns(){
            const data = JSON.parse(JSON.stringify(this.columnsDefault));
            data[4].label =this.type;
            return data;
        }
    },
    methods:{
        rowClick(row){
            this.$emit('rowClick',row);
        }
    }
}
</script>

<style lang="less" scoped>
.data-table{
    width:100%;
    height:100%;
    .TOP{
        position:relative;
        img{
            margin-top:6px;
            width:65px;
            height:25px;
        }
        span{
            position:absolute;
            left:8px;
            top:6px;
            width:40px;
        }
    }
}
</style>