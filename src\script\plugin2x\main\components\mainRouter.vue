<template>
    <div class="main-router">
        <div class="logo-text"></div>
        <div class="router-out">
            <el-menu
                :default-active="activeIndex"
                class="el-menu-demo"
                mode="horizontal"
                text-color="#DFE1EC"
                active-text-color="#fff"
                @select="selectMenu"
            >
                <template v-for="item in menuList">
                    <el-menu-item
                        v-if="!item.children"
                        :key="item.value"
                        :index="item.value"
                        :disabled="item.disabled"
                    >
                        {{ item.label }}
                    </el-menu-item>
                    <template v-else>
                        <el-submenu popper-class="custom-group-submenu" :index="item.value" :key="item.value" :disabled="item.disabled">
                        <template slot="title">{{ item.label }}</template>
                        <el-menu-item
                            v-for="(child, index) in item.children"
                            :index="child.value"
                            :key="index"
                            :disabled="child.disabled"
                            >{{ child.label }}</el-menu-item
                        >
                        </el-submenu>
                    </template>
                    </template>
            </el-menu>
        </div>
    </div>
</template>

<script>
import { getMenuList } from '@/script/constant/menu.js';
export default {
    name:'mainRouter',
    data() {
        return {
            activeIndex: 'home',
            menuList: getMenuList(),
        };
    },
    watch: {
        $route(value) {
            this.setActive(value);
        },
    },
    mounted() {
        this.setActive();
    },
    methods: {
        selectMenu(value) {
            this.$emit('jumpRouter', value);
        },
        formatMenu(menuList) {
            const res = [];
            for (const item of menuList) {
                if(!item.isHide) {
                    res.push(item);
                    if (item.children && item.children.length) {
                        item.children = this.formatMenu(item.children);
                    }
                }
            }
            return res;
        },
        layout() {},
        setActive(value) {
            if(value && value.name){
                this.activeIndex = value.name || '';
            }
        },
    },
}
</script>

<style lang="less" scoped>
.main-router{
    width:100%;
    height:5.52rem;
    background:url('../../../../img/header.png') no-repeat center center / 100% 100%; 
    display:flex;
    justify-content: space-between;
    .logo-text{
        width:646px;
        height:88px;
        background:url('../../../../img/logo_text.png') no-repeat center center / 100% 100%; 
    }
    .router-out{
        width:60%;
        height:100%;
        .el-menu-demo {
            border-bottom:0px !important;
            background: transparent !important;
            margin-left:15px;
            margin-top:20px;
        }
    }
}
:deep {
    .el-menu-item{
        border:none;
    }
  .el-menu-item,
  .el-submenu__title {
    font-size: 16px;
  }
  .el-menu--horizontal > .el-menu-item,
  .el-menu--horizontal > .el-submenu .el-submenu__title {
    width:136px;
    height:44px;
    line-height:44px;
    text-align:center;
    border:none;
    &:hover {
      background: url('../../../../img/tab.png') no-repeat center center / 100% 100% !important;
    }
  }
  .el-submenu.is-active {
    background:url('../../../../img/tab.png') no-repeat center center / 100% 100%;
    color: #fff;
    border-bottom-color:transparent !important;
  }
  .el-menu--horizontal > .el-menu-item.is-active {
    background: url('../../../../img/tab.png') no-repeat center center / 100% 100%;
    color: #fff;
    border-bottom:0px !important;
  }
  .el-menu--horizontal>.el-menu-item:not(.is-disabled):focus, .el-menu--horizontal>.el-menu-item:not(.is-disabled):hover, .el-menu--horizontal>.el-submenu .el-submenu__title:hover{
    background-color:transparent;
  }
  .el-menu--horizontal>.el-submenu.is-active .el-submenu__title{
    border-bottom:0px !important;
  }
  .el-submenu__title i {
    color: #fff;
    font-size: 16px;
  }
}
</style>
<style lang="less">
.custom-group-submenu{
    &.el-menu--horizontal .el-menu .el-menu-item {
        background-color: #1F2E45;
        height: 30px;
        line-height: 30px;
        &:hover {
            background: #101B24;
        }
    }
    &.el-menu--horizontal .el-menu .el-menu-item.is-active {
        color:#53FFFF !important;
    }
    &.el-menu--horizontal .el-menu {
        background: #1F2E45;
        padding: 8px;
        box-shadow: 8px 8px 8px 0px rgba(1, 6, 14, 0.5);
    }
}
</style>