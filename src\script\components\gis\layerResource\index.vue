<template>
    <div class="layer-resource">
        <el-popover
            v-model="isCollapse"
            ref="layerPopover"
            :visible-arrow="false"
            placement="right-start"
            popper-class="popover-box"
            trigger="manual"
        >
            <!-- label -->
            <div
                v-if="layerList.filter((item) => item.isShow).length"
                class="popover-btn"
                slot="reference"
            >
                <!-- <img class="collapse" :src="curCollapseImg" @click="toggleShowTag" /> -->
                <div>
                    <template v-for="item in layerList">
                        <div
                            v-if="item.isShow"
                            class="item"
                            :class="{ isActive: curLayer.prop === item.prop }"
                            :key="item.label"
                            @click="toggleState(item)"
                        >
                            <img
                                class="layer w-16"
                                :src="getIcon(item.prop, item.icons)"
                                alt=""
                                srcset=""
                            />
                            <span class="name">
                                {{ item.label }}
                            </span>
                        </div>
                    </template>
                </div>
            </div>
            <!-- content -->
            <div class="popover-com">
                <keep-alive
                    :exclude="['operateInfo', 'baseStationShow', 'sharpInfo', 'regionList']"
                >
                    <component
                        class="w-350"
                        :is="curLayer.prop"
                        :curLayer="curLayer"
                        :isCollapse="isCollapse"
                        v-bind="{ ...$attrs, ...curLayer.attrs }"
                        v-on="{ ...$listeners, ...curLayer.listeners }"
                    />
                </keep-alive>
            </div>
        </el-popover>
    </div>
</template>

<script>
import noCollapseImg from '@/img/space/icons/noCollapse.png';
import vectorImg from '@/img/space/icons/vector.png';
export default {
    name: 'layer-resource',
    provide() {
        return {
            layerRoot: this,
        };
    },
    props: {
        layerList: {
            type: Array,
        },
    },
    data() {
        return {
            isCollapse: false,
            isShowTag: false,
            resources: [],
            curLayer: {},
            isManualOpen: false, // 是否手动（非点击事件）打开图层
            layerInfo: {},
        };
    },
    computed: {
        curCollapseImg() {
            return this.isShowTag ? vectorImg : noCollapseImg;
        },
    },
    mounted() {
        this.$eventBus.$on('openLayer', (item) => {
            if (!this.isCollapse && item.label !== this.curLayer.label) {
                this.toggleState(item);
            }
        });
        this.$eventBus.$on('closeLayer', (cb) => {
            this.resetAll(true);
            cb && cb();
        });
    },
    methods: {
        toggleState(item) {
            if (!item) return;
            if (!this.isCollapse) {
                this.isCollapse = true;
            } else if (item === this.curLayer) {
                this.isCollapse = false;
            }
            this.isShowTag = true;
            this.curLayer = item;

            if (item.toggleLayer) {
                item.toggleLayer(this.resetAll.bind(this));
            }

            this.$nextTick(() => {
                if (this.$refs.layerPopover) {
                    this.$refs.layerPopover.updatePopper();
                }
            });
        },
        getIcon(prop, icons) {
            const isActive = this.curLayer.prop === prop;
            return icons[Number(isActive)];
        },
        resetAll(isCollapse) {
            if (isCollapse) {
                this.isCollapse = false;
                this.curLayer = {};
            }
        },
    },
};
</script>

<style lang="less" scoped>
.popover-box {
    padding: 0px;
}
.popover-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 10;
    display: inline-flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 52px;
    background: #1A283D;
    font-size: 12px;
    color: white;
    box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.16);
    border-radius: 4px;
    cursor: pointer;
    img {
        display: inline-block;
    }
    .item {
        padding: 10px 9px;
        text-align: center;
        .name {
            display: inline-block;
            text-align: center;
            font-size: 13px;
            line-height: 18px;
            font-weight: 500;
            transform: scale(0.9);
        }
        &.isActive {
            background-color: #1F3459;
            border-radius: 4px;
            .name {
                color: #0091ff;
            }
        }
    }
}
.w-350 {
    width: 350px;
}
</style>
<style lang="less">
.popover-box {
    margin-right: 10px !important;
    background: rgba(23, 42, 65, 0.6) !important;
    box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.65);
    border-radius: 12px;
    backdrop-filter: blur(8px);
    border: none;
}
</style>
