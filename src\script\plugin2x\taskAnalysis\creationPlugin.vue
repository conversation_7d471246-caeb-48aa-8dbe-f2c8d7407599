<template>
    <div class="creation">
        <div class="creation-task">
            <div class="title-top">
                <div class="title">任务创建</div>
            </div>
            <div class="content">
                <div class="content-left">
                    <card :img="require(`../../../img/base_config_header.png`)">
                        <searchBar
                            ref="creationParams"
                            class="form earth-overflow-y"
                            :fields="formCols"
                            :form="form"
                        >
                            <div class="km" slot="KM">
                                &nbsp;&nbsp;
                                <span>m</span>
                                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                <el-tooltip
                                    effect="dark"
                                    content="填写震中为圆心时需要分析的半径范围。"
                                    placement="right"
                                >
                                    <el-button
                                        icon="el-icon-question"
                                        type="text"
                                        size="medium"
                                    ></el-button>
                                </el-tooltip>
                            </div>
                            <el-button style="width:15rem;transform:translateX(5.94rem)" class="earth-btn-common" type="primary" size="small" @click="creatTask"
                                >创建任务</el-button
                            >
                        </searchBar>
                    </card>
                </div>
                <gisMap id="createGIS" @loaded="gisLoaded"/>
            </div>
        </div>
        <el-dialog title="地震选择弹窗" class="earth-dialog" :visible.sync="showDailog">
            <searchBar class="search-form" :fields="dialogFormCols" :form="dialogForm">
                <div class="tool">
                    <el-button
                        class="earth-btn-common"
                        type="primary"
                        size="small"
                        @click="search()"
                        >查询</el-button
                    >
                    <el-button class="addEvent earth-btn-dark" @click="handleAdd()">新增</el-button>
                </div>
            </searchBar>
            <div class="table-wraper">
                <dataTable
                    :columns="columns"
                    :data="tableData"
                    :pagination="paginationData"
                    :total="total"
                    :updateTable="getTableData"
                    @row-click="rowClick"
                >
                    <template #radio="{ row }">
                        <el-radio v-model="radioSelect" :label="String(row.earthquakeCode)"
                            >&nbsp;</el-radio
                        >
                    </template>
                </dataTable>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button class="earth-btn-common" @click="showDailog = false">取 消</el-button>
                <el-button class="earth-btn-common" type="primary" @click="dataTableSureAction">确 定</el-button>
            </span>
        </el-dialog>
        <axiosProgress ref="progressBar" :visible="isLoading"></axiosProgress>
        <myDialog :visible.sync="isShowTips.visible" title="提示" :btnList="isShowTips.btnList">
            <div>圈选区域范围过小，暂无基站数，重新选择范围</div>
        </myDialog>
        <!--  -->
        <addEarthquake
            v-if="earthquakeVisible"
            :visible.sync="earthquakeVisible"
            @updateTable="search"
        />
    </div>
</template>

<script>
import { creationFormCols, detailData, initForm,tableColumn,formCols as homeFormCols } from '_const/creation.js';
import searchBar from '_com/searchBar/searchBar.vue';
import dataTable from '_com/table/dataTable.vue';
import gisMap from '_com/gisMap/index.vue';
import initCircular from '_com/gisMap/layer/circular.js';
import card from '_com/card/index.vue';
import axiosProgress from '_com/progressBar/axiosProgress.vue';
import myDialog from '_com/dialog/index.vue';

export default {
    name: 'creation',
    components: {
        searchBar,
        dataTable,
        gisMap,
        card,
        axiosProgress,
        myDialog,
        addEarthquake: () => import('./components/addEarthquake.vue'),
    },
    data() {
        return {
            form: JSON.parse(JSON.stringify(initForm)),
            formCols: creationFormCols(this),
            dialogFormCols: homeFormCols(),
            showDailog: false,
            dialogForm: {
                earthquakeName: '',
                earthquakeDepthLevel: '',
                earthquakeScaleLevel: '',
                time: [],
            },
            tableData: [
                // {
                //     earthquakeCode: 'CD20240710031760.00',
                //     earthquakeName: 'xx省xx地震',
                //     centerLon: 114.53,
                //     centerLat: 43.452,
                //     earthquakeLevel: 5.2,
                //     earthquakeDepth: 5,
                //     occurTime: '2024-05-06 15:23:50',
                // },
            ],
            paginationData: {
                curPage: 1,
                pageSize: 10,
            },
            total: 0,
            selectRowEarthquakeCode: '',
            radioSelect: '',
            tableRow: {},
            detailData: detailData,
            isShowTips:{
                visible:false,
                btnList:[
                    {
                        name:'确定',
                        class: 'blue-btn',
                        listeners: {
                            click: () => {
                                this.isShowTips.visible = false;
                            },
                        },
                    }
                ]
            },
            isLoading:false,
            earthquakeVisible: false,
        };
    },
    computed: {
        columns() {
            const column = tableColumn;
            return [{ prop: 'radio', label: '', width: 36 }].concat(column);
        },
    },
    watch: {
        radioSelect: {
            handler(newV) {
                if (newV && this.tableData.length) {
                    this.tableRow = this.tableData.find((item) => item.earthquakeCode == newV);
                    Object.assign(this.form,{
                        earthquakeLevel:this.tableRow.earthquakeLevel,
                        earthquakeDepth:this.tableRow.earthquakeDepth,
                        occurTime:this.tableRow.occurTime
                    })
                }
            },
        },
        showDailog: {
            handler(newV) {
                if (!newV && this.radioSelect) {
                    this.handlerForm(this.radioSelect);
                }
            },
            deep: true,
        },
        'form.timeType': {
            handler(newV) {
                if (newV) {
                    this.formCols = creationFormCols(this, this.radioSelect, newV);
                }
            },
            deep: true,
        },
        'form.analysisRadius': {
            handler(newV) {
                if (newV) {
                    this.initCircular();
                }
            },
            deep: true,
        },
    },
    activated() {
        this.initGis(this.$route.params);
    },
    methods: {
        handleDateChange(value) {
            const startDate = new Date(value[0]);
            const startMinutes = startDate.getMinutes();
            const endDate = new Date(value[1]);
            const endMinutes = endDate.getMinutes();
            if (this.form.timeType === 3) {
                if (
                    !(
                        [0, 30].includes(startMinutes) &&
                        [0, 30].includes(endMinutes)
                    )
                ) {
                    this.$message({
                        message: '时间粒度为30分钟时,时间选择的分钟只能是00, 30',
                        type: 'error',
                    });
                    return false;
                }
            } else if (this.form.timeType === 4) {
                if (
                   !(
                        [0, 15, 30, 45].includes(startMinutes) &&
                        [0, 15, 30, 45].includes(endMinutes)
                    ) 
                ) {
                    this.$message({
                        message: '时间粒度为15分钟时,时间选择的分钟只能是00,15,30,40',
                        type: 'error',
                    });
                }
            }
            return true;
        },
        handlerForm(radioSelect) {
            this.form.earthquakeSelection = this.tableRow.earthquakeName || '';
            this.formCols = creationFormCols(this, radioSelect);
            // this.form.analysisRadius = Number(this.tableRow.earthquakeDepth) || '';
        },
        gisLoaded(g){
            this.circular = initCircular(g,{});
            this.dialogGIS = g;
        },
        initGis(params) {
            if (JSON.stringify(params) !== '{}') {
                this.$nextTick(() => {
                    this.tableData = [];
                    this.radioSelect = String(params.earthquakeCode);
                    this.tableRow = params || {};
                    Object.assign(this.form,{
                        earthquakeLevel:this.tableRow.earthquakeLevel,
                        earthquakeDepth:this.tableRow.earthquakeDepth,
                        occurTime:this.tableRow.occurTime
                    })
                    this.handlerForm(true);
                    this.initCircular();
                });
            }
        },
        search() {
            this.paginationData.curPage = 1;
            this.getTableData({ curPage: 1 });
        },
        async getTableData(paginationData = {}) {
            const { curPage = 1, pageSize = 10 } = paginationData;
            const { earthquakeName, earthquakeDepthLevel, earthquakeScaleLevel, time } =
                this.dialogForm;
            let params = {
                pageSize,
                currentPage: curPage,
                burialPepTaskPara: {
                    earthquakeName,
                    earthquakeDepthLevel,
                    earthquakeScaleLevel,
                },
            };
            if (time && time[1]) {
                params.endTime = time && time[1];
            }
            if (time && time[0]) {
                params.startTime = time && time[0];
            }
            this.getPost('post', 'getPage', params, '获取地震列表信息', (res) => {
                this.total = res.totalPageNum;
                this.tableData = res.earthquakeList;
            });
        },
        rowClick(row) {
            this.selectRowEarthquakeCode = String(row.earthquakeCode);
        },
        dataTableSureAction() {
            this.radioSelect = this.selectRowEarthquakeCode;
            this.showDailog = false;
            this.$nextTick(() => {
                this.initCircular();
            })
        },
        initCircular() {
            if (!this.tableRow.centerLon || !this.circular) {
                return;
            }
            const { analysisRadius } = this.form;
            const { centerLon, centerLat } = this.tableRow;
            const startPoint = { lat: centerLat, lng: centerLon };
            this.circular.createCircular({
                circleColor:0x7DACDC,
                circleOpacity: 0.1,
                circleFrame: true,
                circleFrameColor: 0x7DACDC,
                cirCleShowClose: true,
                circleShowRadius: true,
                radius: Number(analysisRadius),
                startPoint: startPoint,
            },{
                dom: `<div class="ripple">
                          <div class="dot" style="z-index: 5;"></div>
                          <div class="text" style="transform:translateX(-45%);background:#EF6868;border-radius:.25rem;box-shadow: 0px 2px 4px 0px rgba(0,0,0,0.19);">震中（${centerLon},${centerLat}）</div>
                      </div>`,
                point: startPoint,
            });
        },
        getExternalSquare(centerPoint, radius) {
            const LAT_PIECE = 0.00001;
            const LNG_PIECE = 0.000009;
            const { lat, lng } = centerPoint;
            return [
                { lat: lat + radius * LAT_PIECE, lng: lng - radius * LNG_PIECE },
                { lat: lat + radius * LAT_PIECE, lng: lng + radius * LNG_PIECE },
                { lat: lat - radius * LAT_PIECE, lng: lng + radius * LNG_PIECE },
                { lat: lat - radius * LAT_PIECE, lng: lng - radius * LNG_PIECE },
            ];
        },
        async creatTask() {
            if (!this.handleDateChange(this.form.executionTime)) {
                return;
            }
            const res = await this.$refs.creationParams.validForm();
            if (res === true) {
                this.checkBaseStation();//基站校验
            }
        },
        requestTaskCreate(params){
            this.getPost(
                'post',
                'taskCreate',
                params,
                '创建任务接口请求',
                () => {
                    this.$message({
                        message: '创建任务成功！',
                        type: 'success',
                    });
                    this.initData();
                    this.jumpToTask();
                }
            );
        },
        checkBaseStation(){
            const { taskName, timeType, executionTime, analysisRadius, populationTargetId,continuousAnalysis } = this.form;
            const { centerLon, centerLat, occurTime } = this.tableRow;
            const params = {
                taskName,
                startTime: (executionTime && executionTime[0]) || '',
                endTime: (executionTime && executionTime[1]) || '',
                roleType: 6,
                burialPepTaskPara: {
                    earthquakeCode: this.tableRow.earthquakeCode,
                    earthquakeName: this.tableRow.earthquakeName,
                    timeType,
                    populationTargetId: populationTargetId.join(','),
                    // analysisRadius,
                    continuousAnalysis,
                    occurTime
                },
                region: {
                    shapeType: 1,
                    circle: {
                        centerLongitude: centerLon,
                        centerLatitude: centerLat,
                        radius: analysisRadius,
                    }
                },
                regionType: 1,
                disasterType: 1,
                heatMapType: 1,
                taskCreateSource: 1,
            }
            this.isLoading = true;
            this.getPost(
                'post',
                'checkBaseStation',
                params,
                '基站校验',
                (res) => {
                    this.isLoading = false;
                    if(!res.allLacCont){
                        this.isShowTips.visible = true;
                    }else{
                        this.requestTaskCreate(params);
                    }
                }
            );
        },
        initData() {
            this.form = JSON.parse(JSON.stringify(initForm));
            this.radioSelect = '';
            this.formCols = creationFormCols(this, this.radioSelect);
            this.tableRow = {};
            const GIS = this.dialogGIS;
            if (GIS) {
                GIS.layerList.圈选.removeAll();
                GIS.layerList.divLayer.removeAll();
            }
        },
        handleAdd() {
            this.earthquakeVisible = true;
        },
        jumpToTask() {
            this.$router.push({
                name: 'myTask',
            });
        },
    },
};
</script>

<style lang="less" scoped>
.creation {
    width: 100%;
    height: 100%;
}
.creation-task{
    width:100%;
    height:100%;
}
.content {
    width: 100%;
    height:calc(100% - 76px);
    border-radius: @border-radius-base;
    position:relative;
    &-left {
        position:absolute;
        top:24px;
        left:24px;
        width: 430px;
        height: calc(100% - 48px);
        z-index:5;
    }
    #createGIS{
        width: 100%;
        height: 100%;
    }
    .form {
        width: 100%;
        height: calc(100% - 2.22rem);
        overflow-x: hidden;
        /deep/ .el-input-group__append {
            background-color: rgba(117, 163, 223, 0);
            border: none !important;
            border-left: 1px solid #A8BFE8 !important;
        }
    }
    .km {
        width: 2.5rem;
        display: flex;
        align-items: center;
        color:#fff;
    }
}
.tool {
    width: 100%;
    display: flex;
    .addEvent {
        margin-left: auto;
        width: 6.56rem;
    }
}
@keyframes scale0 {
    from {
        transform: scale(1.3);
        opacity: 0.8;
    }
    to {
        transform: scale(1.6);
        opacity: 0.5;
    }
}
@keyframes scale1 {
    from {
        transform: scale(1.6);
        opacity: 0.5;
    }
    to {
        transform: scale(1.9);
        opacity: 0.3;
    }
}
@keyframes scale2 {
    from {
        transform: scale(1.9);
        opacity: 0.3;
    }
    to {
        transform: scale(2.2);
        opacity: 0;
    }
}
/deep/.ripple {
    // border-radius:50%;
    position: relative;
    top: -0.455rem;
    .wave0 {
        animation: scale0 1s;
        -webkit-animation: scale0 1s;
        animation-iteration-count: infinite;
        -webkit-animation-iteration-count: infinite;
        animation-timing-function: linear;
        -webkit-animation-timing-function: linear; /* Safari and Chrome */
    }
    .wave1 {
        animation: scale1 1s;
        -webkit-animation: scale1 1s;
        animation-iteration-count: infinite;
        -webkit-animation-iteration-count: infinite;
        animation-timing-function: linear;
        -webkit-animation-timing-function: linear; /* Safari and Chrome */
    }
    .wave2 {
        animation: scale2 1s;
        -webkit-animation: scale2 1s;
        animation-iteration-count: infinite;
        -webkit-animation-iteration-count: infinite;
        animation-timing-function: linear;
        -webkit-animation-timing-function: linear; /* Safari and Chrome */
    }
    .dot {
        float: left;
        width: 1rem;
        height: 1rem;
        border-radius: 50%;
        background: red;
        position: absolute;
        box-shadow: 0px 2px 10px 1px rgba(0,0,0,0.21);
        border: 3px solid #FFFFFF;
    }
    .dot_left {
        margin-left: -50%;
    }
    .text {
        position: absolute;
        top: 2rem;
        left: -50%;
        width: 10rem;
        color: #fff;
        font-size:14px;
        padding:3px 10px;
    }
}
.table-wraper {
    height: 33.33rem;
}
.btn {
    width: 100%;
}
/deep/.el-icon-question{
    font-size:16px;
}
</style>
