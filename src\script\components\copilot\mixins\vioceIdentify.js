import Recorder from 'js-audio-recorder';
import { formatTime, mergeDataViews, uuidv4, b64encode } from '../utils';
import { Decrypt } from '@/utils/encrypted.js';
import { EventBus } from '../common/eventBus.js';


export default {
    name: 'vioceIdentify',
    props: {
        appKey: {
            type: String,
            default: Decrypt(
                '9E8D1FCCEB3C2E13028F294D9C1D6AEECE5F6DC857F8E1A18BC1805263DF32E8D43D231759DE77A9375F4692F407793FF17CB8153AE1C0CEE195DFF070DFAF32F6551A144128F4EE39C650B36B97DB69'
            )
        }
    },
    data() {
        return {
            recorder: null,
            interval: null,
            intervalMS: 100,
            lastPcm: new DataView(new ArrayBuffer(0)), // 不到1280字节的pcm片帧
            websock: null, // websocket的实例
            appId: 'tingjian',
            wsURL: process.env.VUE_APP_IDENTIFY + '/ast-ed/websocket',
            isRecording: false,
            keyWord: '小川小川',
            similarWords: [
                '小窗小窗',
                '哮喘哮喘',
                '小床小床',
                '小肠小肠',
                '小张小张',
                '小双小双',
                '小船小船'
            ],
            openWords: ['打开', '切换', '跳转', '查看', '如何', '什么', '关闭', '退出', '分析'],
            requestWords: ['请问', '请告诉我', '请帮我', '我想', '帮我', '我'],
            emptyCount: 0,
            curTimeStamp: null,
            inputText: '',
            orderTimeStamp: null,
            voiceOrder: false
        };
    },
    computed: {},
    created() {
        this.recorder = new Recorder({
            sampleBits: 16, // 采样位数，支持 8 或 16，默认是16
            sampleRate: 16000, // 采样率，支持11025、16000、22050、24000、44100、48000
            numChannels: 1, //声道数
            compiling: true //(0.x版本中生效,1.x增加中)  // 是否边录边转换，默认是false
        });
    },
    mounted() {
      /*   document.addEventListener('visibilitychange', () => {
            if (document.visibilityState === 'visible') {
                this.startRecord();
            } else {
                this.stopRecord();
            }
        }); */
        // this.init();
    },
    methods: {
        init() {
            this.firstLoading = true;
            this.startRecord(this.firstLoading);
        },
        startRecord() {
            if (this.isRecording) return;
            Recorder.getPermission().then(
                () => {
                    //启动录音的控件
                    this.recorder.start().then(
                        () => {
                            this.isRecording = true;
                            this.initWebSocket();
                            console.log('【语音关键词识别】开始录音');
                            // this.$emit('onStartRecord');
                        },
                        (error) => {
                            console.log(`【语音关键词识别】录音启动出错了`);
                            this.isRecording = false;
                        }
                    );
                },
                (error) => {
                    if (location.protocol == 'http:') {
                        this.$alert(
                            '<strong>http网站默认不能调用麦克风，因此需要修改浏览器的安全策略</strong><br><strong>谷歌浏览器配置修改：</strong><br>1、在谷歌浏览器的地址栏中输入：chrome://flags/#unsafely-treat-insecure-origin-as-secure，将该 Disabled 切换成 enable 状态；<br>2、在 Insecure origins treated as secure 输入栏中输入需要获取麦克风权限的白名单网址（域名或地址，非80需要带上端口号），如果有多个，则以逗号分隔；<br>3、点击右下角的Relaunch按钮，重启浏览器后生效。<br><strong>火狐浏览器配置修改：</strong><br>1、在火狐浏览器的地址栏中输入：about:config，点击“接受风险并继续”；<br>2、搜索框输入insecure，然后回车搜索相关设置选项；<br>3、将media.devices.insecure.enabled和media.getusermedia.insecure.enabled都改为true；4、刷新您的网页重试即可。<br><strong>Edge浏览器配置修改：</strong><br>1、在浏览器地址栏输入：edge://flags/#unsafely-treat-insecure-origin-as-secure；<br>在 Insecure origins treated as secure 输入栏中输入需要获取麦克风权限的白名单网址；<br>将右侧 已禁用 状态改成 已启用；<br>点击浏览器右下角 重启 按钮重启浏览器。',
                            '浏览器配置修改提示',
                            {
                                dangerouslyUseHTMLString: true
                            }
                        );
                    } else {
                        this.$message.info(
                            this.isEngVer
                                ? 'Please allow this webpage to use a microphone first'
                                : '请先允许该网页使用麦克风'
                        );
                    }
                    console.error(`${error.name} : ${error.message}`);
                }
            );
        },
        // 构建websocketUrl
        getWebSocketUrl() {
            const CryptoJS = require('crypto-js');
            const appKey = this.appKey;
            let appName = this.wsURL.split('/')[4];
            const len = 24 - appName.length;
            for (let i = 0; i < len; i++) {
                appName += '0';
            }
            const uuid = uuidv4().toString().split('-').join('');
            const csid = this.appId + appName + uuid;
            const tmp_xServerParam = { appid: this.appId, csid: csid };
            const curTime = Math.floor(new Date().getTime() / 1000);
            const serverParam = b64encode(JSON.stringify(tmp_xServerParam)).toString();
            const checkSum = CryptoJS.MD5(appKey + curTime + serverParam).toString(
                CryptoJS.enc.Hex
            );
            return `${this.wsURL}?appKey=${appKey}&XServerParam=${serverParam}&XCurTime=${curTime}&XCheckSum=${checkSum}`;
        },
        //初始化weosocket
        initWebSocket() {
            console.log('【语音关键词识别】初始化weosocket');
            // 关闭websocket
            if (this.websock != null) {
                this.websock.close();
                this.websock = null;
            }
            const websocketUrl = this.getWebSocketUrl();
            // 连接服务端
            if ('WebSocket' in window) {
                this.websock = new WebSocket(websocketUrl);
            } else if ('MozWebSocket' in window) {
                this.websock = new MozWebSocket(websocketUrl);
            } else {
                console.error('浏览器不支持WebSocket');
                return;
            }
            //指定事件回调
            this.websock.onmessage = this.websocketOnMessage;
            this.websock.onopen = this.websocketOnOpen;
            this.websock.onerror = this.websocketOnError;
            this.websock.onclose = this.websocketClose;
        },
        websocketOnOpen() {
            // 连接建立之后执行send方法发送begin 请求
            let actions = {
                action: 'begin',
                param: `hotword=小川小川`
            };
            if (this.isRecording) {
                console.log('【语音关键词识别】***向 websocket 发送 begin链接请求***');
                this.websocketSend(JSON.stringify(actions));
            }
        },
        websocketSendStop() {
            // 断开连接发送end 指令
            console.log('【语音关键词识别】***向  websocket 发送 end指令***');
            const actions = {
                action: 'end'
            };
            this.websocketSend(JSON.stringify(actions));
        },
        websocketOnError() {
            // 连接建立失败重连
            if (this.isRecording) {
                console.log('【语音关键词识别】连接建立失败重连');
                setTimeout(() => {
                    this.initWebSocket();
                }, 1000);
            }
        },
        websocketOnMessage(e) {
            //数据接收
            const redata = JSON.parse(e.data);
            if (redata.code === 0) {
                switch (redata.action) {
                    case 'begin':
                        // 服务端返回begin，可以开始发送音频信息了
                        if (this.interval != null) {
                            clearInterval(this.interval);
                        }
                        // this.isRecording = true;
                        this.curTimeStamp = Date.now();
                        this.interval = setInterval(() => {
                            this.getWavAndSend();
                            if (
                                this.voiceOrder &&
                                Date.now() - this.curTimeStamp > 2500 &&
                                this.requestWords.findIndex((v) => this.inputText.includes(v)) != -1
                            ) {
                                let text = '';
                                if (this.inputText.includes('小川小川')) {
                                    const textList = this.inputText.split('小川小川');
                                    text = textList[1];
                                }
                                // this.$parent.sendText(text || this.inputText);
                                EventBus.$emit('sendText', text || this.inputText);
                                this.inputText = '';
                                // this.$store.commit('SET_VOICE_ORDER', false);
                                this.voiceOrder = false;
                                // localStorage.setItem('voiceOrder', 'false');
                            }
                        }, this.intervalMS);
                        break;
                    case 'result':
                        this.curTimeStamp = Date.now();
                        const content = JSON.parse(redata.content);
                        let text = '';
                        if ('ws' in content) {
                            text = content.ws.reduce(
                                (pre, cur) => pre + cur.cw.reduce((p, c) => p + c.w, ''),
                                ''
                            );
                        }
                        this.similarWords.map((word) => {
                            text = text.replace(word, this.keyWord);
                        });
                        text = text.replace('咨询课', '智寻客');
                        if (text == '。') text = '';
                        if (text[0] == '。') text = text.slice(1);

                        if (text.replace('.', '') == '') this.emptyCount = this.emptyCount + 1;
                        else this.emptyCount = 0;
                        if (
                            this.emptyCount > 1 ||
                            (this.voiceOrder && Date.now() - this.orderTimeStamp > 15000)
                        ) {
                            this.voiceOrder = false;
                            this.inputText = '';
                        }
                        console.log('【语音关键词识别】：' + text);
                        if (content.msgtype == 'sentence') {
                            this.inputText += text;
                            EventBus.$emit('initInputText', text);
                            if (this.inputText.includes(this.keyWord)) {
                                console.log('识别到关键词', this.inputText);
                                this.inputText = '';
                                this.orderTimeStamp = Date.now();
                                this.voiceOrder = true;
                            }
                        }
                        break;
                    case 'end':
                        this.isRecording = false;
                        break;
                    default:
                        break;
                }
            }
        },
        // 获取音频信息，并发送
        getWavAndSend() {
            // const pcm = this.recorder.getPCM();
            // this.recorder.start();
            const pcm = this.recorder
                .getNextData()
                .reduce((pre, cur) => mergeDataViews(pre, cur), new DataView(new ArrayBuffer(0)));
            const currentPcm = mergeDataViews(this.lastPcm, pcm);
            const num = Math.floor(currentPcm.byteLength / 1280);
            for (var i = 0; i < num; i++) {
                const buffer = new ArrayBuffer(1288);
                const view = new DataView(buffer);
                for (var j = 0; j < 1280; j++) {
                    view.setUint8(j + 8, currentPcm.getUint8(i * 1280 + j, true), true);
                }
                this.websocketSend(buffer);
            }
            const lastLength = currentPcm.byteLength - 1280 * num;
            const lastBuffer = new ArrayBuffer(lastLength);
            this.lastPcm = new DataView(lastBuffer);
            for (var i = 0; i < lastLength; i++) {
                this.lastPcm.setUint8(i, currentPcm.getUint8(num * 1280 + i, true), true);
            }
        },
        websocketSend(Data) {
            // 判断是否连接成功,连接成功再发送数据过去
            if (this.websock != null && this.websock.readyState === 1) {
                this.websock.send(Data);
            } else {
                console.log('【语音关键词识别】websock未连接-------------------');
            }
        },
        websocketClose(e) {
            //关闭
            console.log(
                '【语音关键词识别】websocketClose断开连接' +
                    formatTime(new Date(), 'yyyy-MM-dd hh:mm:ss.S'),
                e
            );
            if (this.interval != null) {
                this.recorder.stop(); //关闭录音
                clearInterval(this.interval);
                this.interval = null;
            }
        },
        stopRecord() {
            if (this.isRecording) {
                console.log('【语音关键词识别】录音结束');
                this.recorder.stop(); //关闭录音
                setTimeout(() => {
                    clearInterval(this.interval);
                    this.interval = null;
                    this.websocketSendStop(); //发送Stop指令
                }, this.intervalMS);
                this.isRecording = false;
            }
        }
    },
    beforeDestroy() {
        this.stopRecord();
    }
};
