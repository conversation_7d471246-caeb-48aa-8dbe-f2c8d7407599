<template>
    <div class="detail-info">
        <div class="detail-info__head">
            <span class="title">
                <span class="title-dot"></span>
                <span>{{ baseInfo.resourceName }}</span>
            </span>
        </div>
        <div class="detail-info__body">
            <el-collapse v-model="activeNames">
                <el-collapse-item v-for="item in detailFields" :name="item.name" :key="item.name">
                    <template slot="title">
                        <img :src="mapTitleIcon[item.name]" alt="image" />
                        <span class="collapse-title">{{ titleAddr }}{{ item.title }}</span>
                    </template>
                    <ul class="ul">
                        <template v-for="(it, inx) in item.fields">
                            <li v-if="it.isShow" class="item" :key="inx">
                                <span class="name">{{ it.label }}：</span>
                                <span class="value">
                                    <span class="copy_dom">{{ getBaseInfo(it.prop, it) }}</span>
                                    <i
                                        v-if="it.prop === 'regionId' && baseInfo[it.prop]"
                                        class="el-icon-copy-document"
                                        title="复制"
                                        @click="handleCopy(baseInfo[it.prop])"
                                    ></i>
                                </span>
                            </li>
                        </template>
                    </ul>
                </el-collapse-item>
            </el-collapse>
        </div>
    </div>
</template>
<script>
import baseInfoIcon from '@/img/space/icons/baseInfo.png';
import otherInfoIcon from '@/img/space/icons/otherInfo.png';
import { detailFields } from '@/script/constant/resourceManifest.js';
import { mapFields } from '@/script/constant/common.js';
import { mapGetters } from 'vuex';
import { getRegionLabels } from '@/utils/method.js';
export default {
    name: 'detail-info',
    props: {
        baseInfo: {
            type: Object,
            default: () => ({}),
        },
    },
    data() {
        return {
            activeNames: ['baseInfo', 'otherInfo'],
            mapTitleIcon: {
                baseInfo: baseInfoIcon,
                otherInfo: otherInfoIcon,
            },
        };
    },
    computed: {
        ...mapGetters(['spaceOriginDistricts', 'spaceLayers', 'spaceAreas']),
        detailFields() {
            const { shapeType, type } = this.baseInfo;
            return detailFields(shapeType, type, this.isPublic).filter((item) => item.isShow);
        },
        userInfo() {
            return {};
        },
        titleAddr() {
            if (this.isResPackage) {
                return '资源包';
            }
            return '';
        },
        isPublic() {
            return this.$route.query.isPublic === 'true';
        },
        isResPackage() {
            return this.baseInfo.type === 'resPackage';
        },
    },
    methods: {
        // todo
        getBaseInfo(prop) {
            const srcVal = this.baseInfo[prop];
            if (mapFields[prop]) {
                const mapDetailValue = mapFields[prop];
                return mapDetailValue[srcVal];
            } else if (prop === 'city') {
                const curItem = this.spaceOriginDistricts.find((item) => item.cityCode === srcVal);
                if (curItem) {
                    return curItem.cityName;
                }
            } else if (prop === 'layerIds') {
                const res = [];
                const { layerId, layerIDs } = this.baseInfo;
                const values = layerIDs || (layerId && [layerId]);
                if (values && Array.isArray(values)) {
                    values.forEach((val) => {
                        const curItem = this.spaceLayers.find((it) => it.value == val);
                        if (curItem) {
                            res.push(curItem.label);
                        }
                    });
                    return res.join('、');
                }
                return values;
            } else if (prop === 'regionArea') {
                return `${Number(srcVal).toFixed(3)}（平方米）`;
            } else if (prop === 'regionTypeIds') {
                return getRegionLabels(this.baseInfo.regionTypeIDs, this.spaceAreas);
            } else if (prop === 'perimeter') {
                return `${Number(srcVal).toFixed(3)}（米）`;
            } else {
                return srcVal;
            }
        },
        // 复制
        handleCopy(text) {
            if (navigator.clipboard) {
                navigator.clipboard
                    .writeText(text)
                    .then(() => {
                        this.$message.success('复制成功！');
                    })
                    .catch(() => {
                        this.$message.error('复制失败！');
                    });
            } else {
                // 备选的复制方法
                let textarea = document.createElement('textarea');
                textarea.value = text;
                document.body.appendChild(textarea);
                textarea.select();
                try {
                    let successful = document.execCommand('copy');
                    let msg = successful ? '成功' : '失败';
                    this.$message.success(`复制${msg}！`);
                } catch (err) {
                    this.$message.error('复制失败！');
                }
                document.body.removeChild(textarea);
            }
        },
    },
};
</script>
<style lang="less" scoped>
.detail-info {
    &__head {
        margin: 0 -12px;
        padding: 10px 16px 10px 16px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.08);
        background: rgba(32, 58, 90, 0.6);
        border-radius: 12px 12px 0px 0px;
        backdrop-filter: blur(8px);
        .title {
            width: 208px;
            height: 22px;
            font-size: 16px;
            font-weight: 500;
            color: #ffffff;
            font-weight: bold;
            .title-dot {
                display: inline-block;
                width: 8px;
                height: 8px;
                background: #53ffff;
                border-radius: 4px;
                vertical-align: middle;
                margin-right: 8px;
            }
        }
    }
    &__body {
        padding: 0 4px;
        .el-collapse {
            border: none;
            .collapse-title {
                margin-left: 8px;
                font-size: 14px;
                font-weight: bold;
            }
            .ul {
                padding-top: 6px;
                .item {
                    font-size: 14px;
                    line-height: 28px;
                    .el-icon-copy-document {
                        margin-left: 4px;
                        color: #53FFFF;
                        &:hover {
                            cursor: pointer;
                        }
                    }
                }
            }
            /deep/ .el-collapse-item__header {
                color: #ffffff;
                background: transparent;
                border-bottom-color: #5d636c;
            }
            /deep/ .el-collapse-item__header.is-active {
                border-bottom-color: #5d636c;
            }
            /deep/.el-collapse-item__wrap {
                background: transparent;
                border: none;
            }
            /deep/ .el-collapse-item__content {
                padding-bottom: 0;
                .item {
                    .name {
                        color: #d1e4ff;
                    }
                    .value {
                        color: #ffffff;
                    }
                }
            }
        }
    }
}
</style>
