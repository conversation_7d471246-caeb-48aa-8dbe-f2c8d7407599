const isProduction = () => {
    let hostName = window.location.hostname;
    // 判断是本地、测试内网还是生产地址。
    if (
        hostName.indexOf('localhost') >= 0 ||
        hostName.indexOf('192.168') >= 0 ||
        hostName.indexOf('127.0') >= 0
    ) {
        return false;
    }
    return true;
};
const gisOptions = {
    bloom: true,
    antialias: true,
    // panoramaLayer: true,
    // mapLayer: isProduction()
    //     ? {
    //           visible: true,
    //           type: 22,
    //           mapType: 'default',
    //       }
    //     : {
    //           visible: false,
    //       },
    areaMap:true,
    baseMapConfig:true,
    preserveDrawingBuffer:true,
    divLayer: true,
    showInfo: true,
    // amapMapLayer: {
    //     visible: true,
    //     type: 22,
    // },
    cameraControl: {
        moveState: true,
        wheelState: true,
        type: '2d',
        wheelLevelChange: false,
        typeButton: true,
        minZoom: 4,
        maxZoom: 19,
        cameraNearCrop: false,
    },
    circleChoice: {
        //圈选
        circleColor: 0x4f2cde,
        circleOpacity: 0.5,
        circleFrame: true,
        circleFrameColor: 0x734ce3,
        circleShowRadius: true, // 该属性会导致辅助圆出现半径巨大的情况
        cirCleShowClose: true,
    },
};
const cityOption = {
    lat: 30.73722,
    lng: 121.32674,
    cityId: 4403,
    cityName: '深圳',
};
export { isProduction, gisOptions, cityOption };
