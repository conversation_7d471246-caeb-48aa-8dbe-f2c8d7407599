import axios from 'axios';
import Vue from 'vue';
import api from '@/script/api';
import router from '@/script/router';
import { getNonce } from '@/utils/method.js';

const POST_SUCCESS = '000001';
const sourceSystemId = 'E9D60167FFDD42B3BFCC300B087A9BBF';
const sourceSystemName = '位置监控';
const operateUserId = '220551014393053187';
window.cancelTokenCollect = {};
const removeCancelToken = (urlName, source) => {
    const targetIndex = window.cancelTokenCollect[urlName].findIndex((item) => item === source);
    window.cancelTokenCollect[urlName].splice(targetIndex, 1);
};
window.cancelRequest = (urlName) => {
    window.cancelTokenCollect[urlName] &&
        window.cancelTokenCollect[urlName].forEach((item) => {
            item.cancel('中断请求：' + urlName);
        });
};
const server = axios.create({
    timeout: 300000,
    baseURL: window.origin + '/',
});
const spaceServer = axios.create({
    timeout: 300000,
    baseURL: window.origin + '/',
});
spaceServer.interceptors.request.use(
    (config) => {
        config.headers['nonce'] = getNonce();
        return config;
    },
    (err) => {
        Promise.reject(err);
    }
);
server.interceptors.request.use(
    (config) => {
        if (config.method.toUpperCase() === 'POST') {
            config.headers['content-type'] = 'application/json';
            config.data = JSON.stringify({ ...config.data });
            const token = localStorage.getItem('token');
            token && (config.headers.token = token);
        }
        config.headers.agentId = 16;
        return config;
    },
    (err) => {
        Promise.reject(err);
    }
);

const errorHandle = {
    999999: '服务调用异常!',
    999998: '系统异常!',
    800000: '请求信息错误或参数不完整！',
    800001: '自定义区域超出公里限制！',
    800002: '操作人ID&服务消费方ID&服务消费方名称必传！',
    800003: '区域内未发现基站，请重新创建区域！',
    400000: '统计时间超出范围！',
};

server.interceptors.response.use(
    (res) => {
        if (res.status === 200) {
            //判断是否正确返回
            if (res.data.returnCode == POST_SUCCESS || res.data.code === 200) {
                return res.data.data;
            }
            return Promise.reject(
                errorHandle[res.data.returnCode] || res.data.returnMsg || res.data.errmsg
            );
        }
        return Promise.reject(res.data.returnMsg);
    },
    (err) => {
        if (err.response.status === 401) {
            Vue.prototype.$message({ message: '权限已失效，请重新登陆！', type: 'error' });
            router.push({ path: '/errorPage' });
        } else {
            return Promise.reject(err);
        }
    }
);

spaceServer.interceptors.response.use(
    (res) => {
        if (res.status === 200) {
            //判断是否正确返回
            return res.data;
        }
        return Promise.reject(res.data);
    },
    (err) => {
        return Promise.reject(err);
    }
);

const request = async (method, urlName, data = {}, downloadProgressFn, outParams = {}, isSpace) => {
    if (!window.cancelTokenCollect[urlName]) {
        window.cancelTokenCollect[urlName] = [];
    }
    const CancelToken = axios.CancelToken;
    const source = CancelToken.source();
    window.cancelTokenCollect[urlName].push(source);
    const newServer = isSpace ? spaceServer : server;
    if (method.toUpperCase() === 'GET') {
        return new Promise(function (resolve, reject) {
            newServer
                .get(api[urlName] || urlName, {
                    params: data,
                    cancelToken: source.token,
                    onDownloadProgress: downloadProgressFn,
                })
                .then((res) => {
                    resolve(res);
                })
                .catch((err) => {
                    if (axios.isCancel(err)) {
                        console.log('Request canceled', err.message);
                    } else {
                        reject(err);
                    }
                });
        }).finally(() => {
            removeCancelToken(urlName, source);
        });
    } else if (method.toUpperCase() === 'POST') {
        return new Promise(function (resolve, reject) {
            const params = {
                sourceSystemId,
                sourceSystemName,
                operateUserId,
                requestData: data,
            };
            Object.assign(params, outParams);
            newServer
                .post(api[urlName] || urlName, params, {
                    cancelToken: source.token,
                    onDownloadProgress: downloadProgressFn,
                })
                .then((res) => {
                    resolve(res);
                })
                .catch((err) => {
                    if (axios.isCancel(err)) {
                        console.log('Request canceled', err.message);
                    } else {
                        reject(err);
                    }
                });
        }).finally(() => {
            removeCancelToken(urlName, source);
        });
    }
};
const post = function (url, params = {}, isOriginData) {
    const token = localStorage.getItem('pageToken');
    return request(
        'POST',
        url,
        params,
        null,
        {
            sourceSystemId: '5FX28SKX7U084O8PKN40DLCMGJTVU913',
            sourceSystemName: '位置国拨项目',
            operateUserId: '1905441725892132864',
            userId: '9999',
            userName: '国拨',
            userNameCN: '国拨',
            webToken: token || '3A20D2E0E42487C01653A016B383C86B',
        },
        true
    )
        .then((res) => {
            if (this && this.isLoading) {
                this.isLoading = false; //去loading
            }
            if (res.returnCode == POST_SUCCESS) {
                return isOriginData ? res : res.data;
            }
            Vue.prototype.$message({ message: res.returnMsg, type: 'error' });
            return res;
        })
        .catch((err) => {
            Vue.prototype.$message({ message: err, type: 'error' });
            return err;
        });
};

const postFile = function (urlName, data = {}, isOriginData, options = {}) {
    if (!window.cancelTokenCollect[urlName]) {
        window.cancelTokenCollect[urlName] = [];
    }
    const CancelToken = axios.CancelToken;
    const source = CancelToken.source();
    window.cancelTokenCollect[urlName].push(source);
    const token = localStorage.getItem('pageToken') || '3A20D2E0E42487C01653A016B383C86B';
    return new Promise(function (resolve, reject) {
        if (data instanceof FormData) {
            data.append('operateUserId', '1905441725892132864');
            data.append('webToken', token);
            data.append('sourceSystemName', '位置国拨项目');
            data.append('sourceSystemId', '5FX28SKX7U084O8PKN40DLCMGJTVU913');
            data.append('userId', '9999');
            data.append('userName', '国拨');
            data.append('userNameCN', '国拨');
        } else {
            const requestData = data;
            data = {
                operateUserId: '1905441725892132864',
                webToken: token,
                sourceSystemName: '位置国拨项目',
                userId: '9999',
                userName: '国拨',
                userNameCN: '国拨',
                requestData,
            };
        }
        // const params = data;
        spaceServer
            .post(api[urlName] || urlName, data, {
                cancelToken: source.token,
                ...options,
            })
            .then((res) => {
                if (this && this.isLoading) {
                    this.isLoading = false; //去loading
                }
                if (res.returnCode == POST_SUCCESS) {
                    resolve(isOriginData ? res : res.data);
                } else {
                    Vue.prototype.$message({ message: res.returnMsg, type: 'error' });
                    resolve(res);
                }
            })
            .catch((err) => {
                if (axios.isCancel(err)) {
                    console.log('Request canceled', err.message);
                } else {
                    Vue.prototype.$message({ message: err, type: 'error' });
                    reject(err);
                }
            });
    }).finally(() => {
        removeCancelToken(urlName, source);
    });
};

const downloadFile = function (url, params = {}) {
    return new Promise((resolve, reject) => {
        postFile(url, params, true, {
            responseType: 'blob',
        })
            .then((res) => {
                resolve(res);
            })
            .catch((err) => {
                Vue.prototype.$message({ message: err.errorMessage, type: 'error' });
                reject(err);
            });
    });
};

export { request, post, postFile, downloadFile };

export default (method, url, data = {}) => {
    if (method.toUpperCase() === 'GET') {
        return server.get(url, { params: data });
    } else if (method.toUpperCase() === 'POST') {
        return server.post(url, data);
    }
};
