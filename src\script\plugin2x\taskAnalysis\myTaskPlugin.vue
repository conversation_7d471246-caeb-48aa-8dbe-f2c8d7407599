<template>
    <div class="my-task">
        <div class="title-top">
            <div class="title">我的任务</div>
        </div>
        <div class="content" slot="content">
            <searchBar class="search-form" :fields="formCols" :form="form">
                <template>
                    <el-button
                        class="earth-btn-common"
                        type="primary"
                        size="small"
                        @click="search()"
                        >查询</el-button
                    >
                </template>
            </searchBar>
            <dataTable
                class="data-table"
                :columns="columns"
                :data="tableData"
                :pagination="paginationData"
                :total="total"
                :updateTable="getTableData"
            >
                <!-- 执行状态 -->
                <template #taskStatus="{ row }">
                    <div
                        v-if="row.taskStatus != null"
                        :class="`row-list state state-${row.taskStatus}`"
                    >
                        <span :class="`circle state-${row.taskStatus}`"></span>
                        <span>{{ executeStatusList[row.taskStatus] }}</span>
                    </div>
                </template>
                <!-- 执行状态 -->
                <template #taskNowSustainTime="{ row }">
                    <div>{{row.taskNowSustainTime || '-'}}</div>
                </template>
                <!-- 任务名称 -->
                <template #taskName="{ row }">
                    <div v-if="row.taskCreateSource === 1 && row.taskName != null" class="row-list">
                        <el-popover
                            title="任务详情"
                            width="340"
                            popper-class="detail-popper"
                            trigger="hover"
                            placement="right"
                        >
                            <div class="earth-overflow-y">
                                <div
                                    class="active-alarm-detail-item"
                                    v-for="(item, index) in taskNameDetail"
                                    :key="index"
                                    :style="{width: '100%', 'line-height':'1.43rem'}"
                                >
                                    <span class="earth-bold-text">{{ item.name }}</span>
                                    <span class="earth-default-text">{{ item.formatter(row[item.props], row)  }}</span>
                                </div>
                            </div>
                            <span slot="reference" class="show-task-name">{{ row.taskName }} <img src="../../../img/icon/icon_question.png" alt=""/></span>
                        </el-popover>
                    </div>
                </template>
                 <!-- 参考位置 -->
                 <template #earthquakeName="{ row }">
                    <div v-if="row.taskCreateSource === 1 && row.taskName != null" class="row-list">
                        <el-popover
                            title="任务详情"
                            width="320"
                            popper-class="detail-popper"
                            trigger="hover"
                            placement="right"
                        >
                            <div class="earth-overflow-y">
                                <div
                                    class="active-alarm-detail-item"
                                    v-for="(item, index) in detailData"
                                    :key="index"
                                    :style="{width:index === 4 ?'100%':'50%','line-height':'1.43rem'}"
                                >
                                    <span class="earth-bold-text">{{ item.name }}</span>
                                    <span class="earth-default-text">{{ row[item.props] || '-' }}</span>
                                </div>
                            </div>
                            <span slot="reference" class="show-task-name">{{ row.earthquakeName }} <img src="../../../img/icon/icon_question.png" alt=""/></span>
                        </el-popover>
                    </div>
                </template>
                <!-- 执行结束时间 -->
                <template #executeEndTime="{ row }">
                    <div>
                        <span>{{ row.taskStatus === 2 ? row.executeEndTime : '' }}</span>
                    </div>
                </template>
                <!-- 操作 -->
                <template #operation="{ row }">
                    <template  v-if="row.taskStatus == 2 || row.taskStatus == 4">
                        <el-button class="btn-item" type="text" @click="openDetail(row)"
                            >查看</el-button
                        >
                        <el-button class="btn-item" type="text" @click="downLoad(row)">下载</el-button>
                    </template>
                    <el-button class="btn-item" style="color:#53FFFF" type="text" @click="openStation(row)">基站情况</el-button>
                </template>
            </dataTable>
        </div>
    </div>
</template>

<script>
import searchBar from '_com/searchBar/searchBar.vue';
import dataTable from '_com/table/dataTable.vue';
import { formCols, tableColumns, executeStatusList, detailData, taskNameDetail } from '_const/myTask.js';
import { downloadOption } from '_const/analyzeProduct.js';
import {createExcel} from '../../../utils/creatExcel.js';
export default {
    name: 'myTask',
    components: {
        searchBar,
        dataTable,
    },
    beforeRouteEnter(to, from, next) {
        next((vm) => {
            if (!vm.isFirstEnter) {
                vm.search();
            }
        });
    },
    data() {
        return {
            formCols: formCols(),
            form: {
                taskId: '',
                taskName: '',
                earthquakeName: '',
                taskStatus: '',
                taskCreateTime: '',
            },
            columns: tableColumns,
            tableData: [],
            paginationData: {
                curPage: 1,
                pageSize: 20,
            },
            total: 0,
            executeStatusList: executeStatusList,
            detailData: detailData,
            taskNameDetail,
            isFirstEnter: true,
        };
    },
    mounted() {
        this.search();
        this.isFirstEnter = false;
    },
    methods: {
        search() {
            this.paginationData.curPage = 1;
            this.getTableData({ curPage: 1 });
        },
        async getTableData(paginationData = {}) {
            const { curPage = 1, pageSize = 20 } = paginationData;
            const { taskId, taskName, earthquakeName, taskStatus, taskCreateTime } = this.form;
            let params = {
                pageSize,
                currentPage: curPage,
                taskId,
                taskName,
                taskStatus: taskStatus.join(','),
                taskCreateTime,
                burialPepTaskPara: {
                    earthquakeName,
                },
            };
            this.getPost('post', 'getTaskList', params, '获取任务列表信息', (res) => {
                this.total = res.totalPageNum;
                this.tableData = res.detailList;
                // 计算任务名称最大列宽
                this.columns[1].width = this.getMaxWidth(this.tableData);
            });
        },
        openDetail(row) {
            if (row.taskCreateSource === 2) {
                const name = row.heatMapType === 1 ? 'analyzeDetails' : 'resultProduct';
                this.$router.push({
                    name,
                    params: row,
                });
            } else {
                this.$router.push({
                    name: 'analyzeDetails',
                    params: row,
                });
            }
        },
        downLoad(row){
            this.getPost('post', 'downloadExcel', {
                taskId:row.taskId
            }, 'Excel数据报告下载', (res) => {
                const {analyzeDetail,influencedList,lossContactList} = res;
                const excelData = [];
                if(influencedList.length) {
                    let addRow = []; 
                    for(let i= 0;i < influencedList.length ;i++){
                        const {dataTime,allPopCnt,gridDetail} = influencedList[i];
                        gridDetail.forEach((item,index) => {
                            if(index == 0){
                                addRow.push([dataTime,allPopCnt,item.gridCenterInglat,item.popCnt]);
                            }else{
                                addRow.push(['','',item.gridCenterInglat,item.popCnt]);
                            }
                        })
                        
                    }
                    excelData.push({
                        A1Value:analyzeDetail,
                        addRow:addRow,
                        key:1
                    })
                }
                if(lossContactList.length){
                    let addRow = []; 
                    for(let i= 0;i < lossContactList.length ;i++){
                        const {dataTime,allPopCnt,gridDetail} = lossContactList[i];
                        gridDetail.forEach((item,index) => {
                            if(index == 0){
                                addRow.push([dataTime,allPopCnt,item.gridCenterInglat,item.popCnt]);
                            }else{
                                addRow.push(['','',item.gridCenterInglat,item.popCnt]);
                            }
                        })
                        
                    }
                    excelData.push({
                        A1Value:analyzeDetail,
                        addRow:addRow,
                        key:2
                    })
                }
                const data = downloadOption(excelData);
                createExcel(row.taskName,data);

            });
        },
        getMaxWidth(tableData) {
            let maxWidth = 0;
            tableData.forEach(item => {
                maxWidth = Math.max(maxWidth, item.taskName.length * 15);
            });
            return maxWidth;
        },
        openStation(row){
            this.$router.push({
                name: 'baseStationSituation',
                params: row,
            });
        },
    },
};
</script>

<style lang="less" scoped>
.my-task{
    width:100%;
    height:100%;
    padding-bottom:20px;
}
.content {
    width: 100%;
    height: calc(100% - 76px);
    padding:15px;
    display: flex;
    flex-direction: column;
    .data-table {
        height: calc(100% - 46px);
        border-radius: 0.44rem;
    }
}
.row-list {
    display: flex;
    align-items: center;
    &.state {
        padding: 0.56rem;
        display: inline;
    }
    &.state-0 {
        color: @to-be-color;
        background: rgba(255, 158, 1, 0.15);
        .circle {
            background: @to-be-color;
        }
    }
    &.state-1 {
        color: @being-color;
        background: rgba(98, 249, 255, 0.15);
        .circle {
            background: @being-color;
        }
    }
    &.state-2 {
        color: @done-color;
        background: rgba(61, 242, 88, 0.15);
        .circle {
            background: @done-color;
        }
    }
    &.state-3 {
        color: @fail-color;
        background: rgba(238, 10, 36, 0.15);
        .circle {
            background: @fail-color;
        }
    }
    &.state-4 {
        color: #FFF517;
        background: rgba(255, 245, 23, 0.15);
        .circle {
            background: #FFF517;
        }
    }
    .circle {
        width: 6px;
        height: 6px;
        border-radius: 50%;
        margin-right: 10px;
        display: inline-block;
        margin-bottom: 2px;
    }
}
.table-btn-item {
    position: relative;
    margin: 0 0.56rem;
    &::after {
        position: absolute;
        right: -0.72rem;
        top: 2px;
        width: 1px;
        background: #ebeef5;
        height: 0.61rem;
        content: '';
    }
    &:last-child {
        &::after {
            position: absolute;
            width: 0px;
            height: 0px;
            content: '';
        }
    }
}
.show-task-name{
    display:flex;
    align-items:center;
    img{
        padding-left:5px;
    }
}
.earth-overflow-y{
    width:100%;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
}
.btn-item{
    color:#53FFFF;
    position: relative;
    &::after {
        position: absolute;
        right: -13px;
        top: 13px;
        width: 1px;
        background: #D8D8D8;
        height: 11px;
        content: '';
    }
    &:last-child {
        &::after {
            position: absolute;
            right: -13px;
            top: 13px;
            width: 1px;
            background: #D8D8D8;
            height: 0px;
            content: '';
        }
    }
}
.btn-item + .btn-item{
    padding-left:15px;
}
</style>
<style lang="less">
.detail-popper{
    &.el-popover{
        background: rgba(39, 69, 106, 0.8);
        box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.65);
        backdrop-filter: blur(8px);
        border:none;
        color:#fff;
    }

    .el-popover__title{
        display:none;
    }
    .popper__arrow{
        display:none;
    }
}
</style>
