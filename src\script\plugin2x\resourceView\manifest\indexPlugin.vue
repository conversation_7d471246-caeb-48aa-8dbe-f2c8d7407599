<template>
    <div class="manifest">
        <div class="manifest__head">
            <span class="title">
                <span class="dot"></span>
                预设区域管理
            </span>
            <div class="select-layer">
                <el-select
                    v-model="searchVal"
                    size="small"
                    filterable
                    popper-class="custom-select"
                    placeholder="请选择"
                    clearable
                >
                    <el-option v-for="item in historyRecords" v-bind="item" :key="item.value" />
                </el-select>
                <el-button
                    class="search-btn"
                    icon="el-icon-search"
                    size="small"
                    @click="search()"
                />
            </div>
            <el-button class="check-creation" type="primary" size="small" @click="jumpResCreate"
                >资源创建</el-button
            >
        </div>
        <div class="manifest__body">
            <el-tabs v-model="activeName">
                <el-tab-pane v-for="(tab, inx) in tabList" v-bind="tab" :key="inx">
                    <keep-alive>
                        <el-row
                            v-loading="loading"
                            element-loading-background="rgba(0, 0, 0, 0.8)"
                            v-if="tab.name === activeName"
                            class="manifest__content"
                            :gutter="12"
                        >
                            <el-col
                                v-for="(resource, inx) in tab.resources"
                                :xs="8"
                                :sm="8"
                                :md="6"
                                :lg="5"
                                :xl="4"
                                :key="inx"
                            >
                                <layerCard
                                    :resource="resource"
                                    :isPublic="isPublic"
                                    @collect="handleCollect"
                                    @jump="handleJump"
                                >
                                    <template #btn> </template>
                                </layerCard>
                            </el-col>
                        </el-row>
                    </keep-alive>
                </el-tab-pane>
            </el-tabs>
            <!-- 过滤条件 -->
            <div v-if="isPublic" class="filter-condition">
                <el-checkbox v-model="isCollect">{{ dynamicTitle }}</el-checkbox>
                <!-- 排序方式 -->
                <el-select
                    v-model="sortMethod"
                    class="sort-method earth-dark-select"
                    size="mini"
                    popper-class="custom-select"
                    placeholder="请选择"
                >
                    <div class="prefix" slot="prefix">
                        <span class="mr-8 black">排序方式</span>
                        <span> | </span>
                    </div>
                    <el-option v-for="item in sortOpts" v-bind="item" :key="item.value" />
                </el-select>
            </div>
        </div>
    </div>
</template>

<script>
import layerCard from '@/script/components/layerCard.vue';
import { sortOpts, tabList, mapLayerNames, layerImgs } from '@/script/constant/resourceManifest.js';
import { formatAreas, formatField } from '@/utils/method.js';
export default {
    name: 'manifest',
    components: {
        layerCard,
    },
    data() {
        return {
            activeName: 'private',
            searchVal: '',
            historyRecords: [],
            tabList,
            isCollect: false,
            sortMethod: '',
            sortOpts,
            taskId: '',
            loading: false,
        };
    },
    computed: {
        curTab() {
            return this.tabList.find((item) => item.name === this.activeName);
        },
        isPublic() {
            return this.activeName === 'public';
        },
        userInfo() {
            return {
                id: 1,
                name: 'admin',
                describe: '系统管理员',
            };
        },
        dynamicTitle() {
            return this.isCollect ? '查看全部' : '我的收藏';
        },
        queryData() {
            return this.$route.query;
        },
        paramsData() {
            return this.$route.params;
        },
    },
    watch: {
        activeName: {
            handler() {
                this.queryList();
            },
        },
        isCollect() {
            this.queryList();
        },
        sortMethod() {
            this.queryList();
        },
    },
    created() {
        const resType = this.queryData.resType || this.paramsData.resType;
        if (resType) {
            this.activeName = resType;
        }
        this.initBaseData();
        this.queryList();
    },
    methods: {
        initBaseData() {
            Promise.all([
                this.$post('getDistrict'),
                this.$post('getAreas'),
                this.$post('getLayers'),
            ]).then((res) => {
                const [srcDistricts, srcAreas, srcLayers] = res;
                const originDistricts = srcDistricts.districtList;
                // 获取地市
                const districts = formatAreas(originDistricts);
                this.$store.commit('setSpaceDistricts', districts);
                this.$store.commit('setSpaceOriginDistricts', Object.freeze(originDistricts));
                console.log('districts~61', districts);
                // 获取地域
                const areas = formatField(srcAreas);
                this.$store.commit('setSpaceAreas', areas);
                this.$store.commit('setSpaceOriginAreas', Object.freeze(srcAreas));
                // 获取图层
                const layers = srcLayers.layerList.map((item) => {
                    return {
                        ...item,
                        label: item.layerName,
                        value: item.layerId,
                    };
                });
                this.$store.commit('setSpaceLayers', layers);
            });
        },
        search() {
            this.queryList();
        },
        async queryList(type = this.activeName) {
            this.loading = true;
            let params = { layerName: this.searchVal };
            let res = [];
            if (type === 'public') {
                Object.assign(params, {
                    sortType: this.sortMethod,
                    isCollect: this.isCollect ? Number(this.isCollect) : undefined,
                });
                res = await this.$post('getCategoryPage', params);
            } else if (type === 'private') {
                res = await this.$post('getPrivateCategory', params);
            }
            this.loading = false;
            this.curTab.resources = (res || [])
                .map((item) => {
                    const layerEName = mapLayerNames[item.layerId || item.layerName];
                    return {
                        ...item,
                        img: layerImgs[layerEName] || layerImgs.park,
                    };
                })
                .filter((item) => item.layerName !== '资源包');
            this.historyRecords = this.setHistoryRecords(res);
        },
        setHistoryRecords(list) {
            return list.map((item) => ({
                label: item.layerName,
                value: item.layerName,
            }));
        },
        async pushBaseStation() {
            const res = await this.$post(
                'pushBaseStations',
                {
                    roleType: 0,
                },
                true
            );
            if (res && res.data && res.data.taskId) {
                this.taskId = res.data.taskId;
            }
            this.$message.success(res.returnMsg);
        },
        async viewTaskStatus() {
            if (!this.taskId) return;
            const res = await this.$post(
                'pushBaseStations',
                {
                    roleType: 1,
                    taskId: this.taskId,
                },
                true
            );
            console.log('res~check-status', res);
        },
        handleCollect(row) {
            const { id, name } = this.userInfo;
            this.$post(
                'collectResource',
                {
                    userId: id,
                    userName: name,
                    collectType: row.isCollect ? 0 : 1,
                    resourceType: 4, // 图层
                    resourceId: row.layerId,
                },
                true
            ).then((res) => {
                if (res.serviceFlag === 'TRUE') {
                    this.$message.success(res.returnMsg);
                    this.queryList();
                } else {
                    this.$message.error(res.returnMsg);
                }
            });
        },
        handleJump(resource) {
            this.$router.push({
                path: 'resManifest',
                query: {
                    ...resource,
                    isPublic: this.isPublic,
                },
            });
        },
        jumpResCreate() {
            this.$router.push({
                name: 'presetArea',
                query: {},
            });
        },
    },
};
</script>

<style lang="less" scoped>
@media only screen and (min-width: 1200px) {
    .el-col-lg-5 {
        width: 20%;
    }
}
.manifest {
    display: flex;
    flex-direction: column;
    height: 100%;
    &__head {
        position: relative;
        display: flex;
        justify-content: space-between;
        padding: 12px 20px 12px 20px;
        text-align: center;
        box-shadow: 0px 0px 25px 0px rgba(80, 143, 242, 0.1);
        backdrop-filter: blur(15px);
        background: linear-gradient(270deg, #101620 0%, #1b2f4d 100%);
        .title {
            display: flex;
            justify-content: space-between;
            line-height: 32px;
            font-size: 20px;
            font-weight: bold;
            line-height: 32px;
            color: #ffffff;
            text-shadow: 0px 0px 4px rgba(62, 136, 233, 0.64);
            .dot {
                margin-right: 8px;
                width: 8px;
                height: 8px;
                background: #53ffff;
                border-radius: 4px;
                align-self: center;
            }
        }
        .el-select {
            width: 32rem;
            /deep/ .el-input__inner {
                border-radius: 0;
                background: rgba(117, 163, 223, 0);
                box-shadow: inset 0px 0px 8px 0px #4984ff;
                border-radius: 2px;
                border: 1px solid #a8bfe8;
            }
        }
        .search-btn {
            margin-left: -4px;
            padding: 9px 11px;
            color: #fff;
            border-radius: 0;
            background: #3871b3;
            vertical-align: bottom;
            border-left: none;
            border-color: #a8bfe8;
        }
        .check-creation {
            background: #009142;
            border-radius: 4px;
            border: none;
        }
    }
    &__body {
        position: relative;
        flex: 1;
        height: 0;
        margin: 18px;
        background: rgba(23, 42, 65, 0.6);
        box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.65);
        border-radius: 12px;
        backdrop-filter: blur(8px);
        .filter-condition {
            position: absolute;
            right: 32px;
            top: 6px;
            .el-checkbox {
                margin-right: 25px;
            }
            .sort-method {
                .prefix {
                    display: flex;
                    align-items: center;
                    padding-left: 6px;
                    height: 100%;
                }
                /deep/ .el-input__inner {
                    padding-left: 76px;
                    background: rgba(117, 163, 223, 0);
                    box-shadow: inset 0px 0px 8px 0px #4984ff;
                    border-radius: 2px;
                    border: 1px solid #a8bfe8;
                }
            }
        }
        .el-tabs {
            display: flex;
            flex-direction: column;
            height: 100%;
            /deep/ .el-tabs__header {
                .el-tabs__nav-wrap::after {
                    background: #36507c;
                }
                .el-tabs__item {
                    color: #9aafc1;
                    &.is-active {
                        color: #fff;
                    }
                }
                .el-tabs__nav-scroll {
                    padding-left: 30px;
                }
            }
            /deep/ .el-tabs__content {
                padding: 0 15px;
                flex: 1;
                height: 0;
                overflow: auto;
                &::-webkit-scrollbar {
                    width: 3px;
                    height: 3px;
                }
                &::-webkit-scrollbar-thumb {
                    border-radius: 10px;
                    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
                    background: #5c6f92;
                }
                &::-webkit-scrollbar-track {
                    /* 滚动条里面轨道 */
                    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
                    border-radius: 10px;
                    background: transparent;
                }
                &::-webkit-scrollbar-corner {
                    background: rgba(0, 0, 0, 0);
                }
            }
        }
    }
    &__content {
        display: flex;
        flex-wrap: wrap;
    }
}
.black {
    color: #dfe1ec;
}
.mr-8 {
    margin-right: 8px;
}
.ml-10 {
    margin-left: 10px;
}
</style>
<style lang="less">
.custom-select {
    background-color: #101d34;
    border-color: #a8bfe8;
    .el-select-dropdown__item {
        color: #fff;
    }
    .el-select-dropdown__item:hover {
        color: #409eff;
    }
    .el-select-dropdown__item.hover {
        color: #409eff;
    }
    &.is-multiple .el-select-dropdown__item.selected {
        background: rgba(0, 0, 0, 0.5);
    }
}
</style>
