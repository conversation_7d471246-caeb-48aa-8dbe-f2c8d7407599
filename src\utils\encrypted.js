const CryptoJS = require('crypto-js')
export function transPsw(val) {
  return CryptoJS.MD5(val + 'model_infer').toString()
}

export function transUserName(val) {
  return AES_ECB_ENCRYPT(val, "50AiPqLhrpjwGo9JYNIFAX")
}

export function decryptUserName(val) {
  return AES_ECB_DECRYPT(val, "50AiPqLhrpjwGo9JYNIFAX")
}

const key = CryptoJS.enc.Utf8.parse('1234567890ABCDEF') // 十六位十六进制数作为密钥
const iv = CryptoJS.enc.Utf8.parse('ABCDEF1234567890') // 十六位十六进制数作为密钥偏移量
// 解密方法
export function Decrypt (word) {
  const encryptedHexStr = CryptoJS.enc.Hex.parse(word)
  const srcs = CryptoJS.enc.Base64.stringify(encryptedHexStr)
  const decrypt = CryptoJS.AES.decrypt(srcs, key, { iv: iv, mode: CryptoJS.mode.CBC, padding: CryptoJS.pad.Pkcs7 })
  const decryptedStr = decrypt.toString(CryptoJS.enc.Utf8)
  const inside = decryptedStr.toString()
  const i = inside.substr(-36)
  return inside.split(i)[0]
}
// 加密方法
export function Encrypt (word) {
  const srcs = CryptoJS.enc.Utf8.parse(word + uuidv4())
  const encrypted = CryptoJS.AES.encrypt(srcs, key, { iv: iv, mode: CryptoJS.mode.CBC, padding: CryptoJS.pad.Pkcs7 })
  return encrypted.ciphertext.toString().toUpperCase()
}
export function uuidv4() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}


/**
 * AES-256-ECB对称加密
 * @param text {string} 要加密的明文
 * @param secretKey {string} 密钥
 * @returns {string} 加密后的密文，Base64格式
 */
function AES_ECB_ENCRYPT(text, secretKey) {
  const keyHex = CryptoJS.enc.Base64.parse(secretKey);
  const messageHex = CryptoJS.enc.Utf8.parse(text);
  const encrypted = CryptoJS.AES.encrypt(messageHex, keyHex, {
    "mode": CryptoJS.mode.ECB,
    "padding": CryptoJS.pad.Pkcs7
  });
  return encrypted.toString();
}

/**
 * AES-256-ECB对称解密
 * @param textBase64 {string} 要解密的密文，Base64格式
 * @param secretKey {string} 密钥，43位随机大小写与数字
 * @returns {string} 解密后的明文
 */
function AES_ECB_DECRYPT(textBase64, secretKey) {
  let keyHex = CryptoJS.enc.Base64.parse(secretKey);
  let decrypt = CryptoJS.AES.decrypt(textBase64, keyHex, {
    "mode": CryptoJS.mode.ECB,
    "padding": CryptoJS.pad.Pkcs7
  });
  return CryptoJS.enc.Utf8.stringify(decrypt);
}
