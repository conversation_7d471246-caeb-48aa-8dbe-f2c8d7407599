<template>
    <div class="frame-wrapper">
        <div class="frame-wrapper-top">
            <div class="title-wrapper">
                <span class="title">{{title}}</span>
                <slot name="title-right"></slot>
            </div>
        </div>
        <div class="frame-wrapper-content">
            <slot name="content"></slot>
        </div>
    </div>
</template>

<script>
export default {
    name:'frameBox',
    props:{
        title:{
            type:String,
            default:'',
        }
    }

};
</script>

<style lang="less" scoped>
.frame-wrapper{
    width:100%;
    height:100%;
    padding-top:@interval-size-small;
    &-top{
        height: 4rem;
        background: linear-gradient( 90deg, #CEE8FF 0%, #D9EDFF 100%);
        box-shadow: 0rem 0rem 1.39rem 0rem rgba(80,143,242,0.1);
        border-radius: .56rem .56rem 0rem 0rem;
        border: .06rem solid rgba(255,255,255,0.7);
        backdrop-filter: blur(.83rem);
        padding:0 @interval-size-small;
        position:relative;
        z-index:1;
    }
    .title-wrapper{
        display:flex;
        align-items:center;
        width:100%;
        height:3.17rem;
        position:relative;
        justify-content: space-between;
    }
    .title{
        font-size:@font-size-h1;
        font-weight:bold;
    }
    &-content{
        height:calc(100% - 3.17rem);
        margin-top:-0.83rem;
        background: #F3F9FF;
        box-shadow: 0rem 0rem 1.56rem 0rem rgba(80,143,242,0.1);
        border-radius: .56rem .56rem 0rem 0rem;
        border: .06rem solid rgba(255,255,255,0.7);
        position:relative;
        z-index:2;
        padding:@interval-size-small;
    }
}
</style>