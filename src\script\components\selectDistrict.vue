<!-- 普通多选、单选、最后一级多选、带全选的最后一级多选 -->
<template>
    <el-cascader
        ref="selectPaneRef"
        v-model="selectList"
        class="select-district"
        :options="options"
        :props="props"
        :disabled="disabled"
        :size="size || 'mini'"
        :filterable="filterable"
        collapse-tags
        :popper-class="
            isLastMultiSelect ? 'custom-hide-checkbox custom-cascader' : 'custom-cascader'
        "
        :clearable="clearable"
        :placeholder="placeholder"
        @change="handleChange"
        @expand-change="handleExpandChange"
    />
</template>

<script>
export default {
    name: 'select-district',
    props: {
        value: {
            type: [Array, String, Number],
            default: () => [],
        },
        options: {
            type: Array,
            default: () => [],
        },
        props: {
            type: Object,
            default: () => ({
                multiple: true,
                label: 'label',
                value: 'value',
                children: 'children',
            }),
        },
        isLastMultiSelect: {
            type: Boolean,
            default: true,
        },
        disabled: {
            type: Boolean,
            default: false,
        },
        size: {
            type: String,
        },
        clearable: {
            type: Boolean,
            default: true,
        },
        filterable: {
            type: Boolean,
            default: true,
        },
        emitPath: {
            type: Boolean,
            default: true,
        },
        placeholder: {
            type: String,
            default: ''
        },
    },
    model: {
        prop: 'value',
        event: 'change',
    },
    data() {
        return {
            curRootPath: '',
            preCheckedRootPath: '',
            preSelectList: [],
            preSelectAreas: [],
        };
    },
    computed: {
        selectList: {
            get() {
                return this.value;
            },
            set(newList) {
                this.$emit('change', newList);
            },
        },
        // 多选且只要最后一层
        isMultiAndNoPath() {
            const { multiple, emitPath } = this.props;
            return !emitPath && multiple;
        },
        subAreas() {
            const pathArr = this.curRootPath.split('/');
            let curItem = null;
            let options = this.options;
            for (const path of pathArr) {
                curItem = options.find((item) => item.value === path);
                options = curItem.children || [];
            }
            return options.map((item) => item.value);
        },
        allSelect() {
            return `${this.curRootPath}-all`;
        },
    },
    methods: {
        handleChange(values) {
            if (!this.isLastMultiSelect || !this.props.multiple) return;

            const { emitPath } = this.props;
            if (this.preCheckedRootPath !== this.curRootPath) {
                this.preSelectAreas = [];
                this.preCheckedRootPath = this.curRootPath;
                this.$nextTick(() => {
                    // 是否全路径
                    if (emitPath) {
                        this.selectList = this.selectList.filter((items) =>
                            items.join('/').startsWith(this.curRootPath)
                        );
                    } else {
                        this.selectList = this.selectList.filter(
                            (code) => !this.preSelectList.includes(code)
                        );
                        this.setSelectList(this.selectList, this.preSelectAreas);
                    }
                });
            } else {
                if (!emitPath) this.setSelectList(values, this.preSelectAreas);
            }
        },
        setSelectList(values, preSelectAreas) {
            const { isCheck, area } = this.getCurSelectArea(values, preSelectAreas);

            if (!area) return;

            this.$nextTick(() => {
                if (isCheck) {
                    if (area === this.allSelect) {
                        this.selectList = this.subAreas;
                    } else {
                        if (values.length === this.subAreas.length - 1) {
                            this.selectList = this.subAreas;
                        }
                    }
                } else {
                    if (area === this.allSelect) {
                        this.selectList = [];
                    } else {
                        this.selectList = this.selectList.filter(
                            (val) => ![this.allSelect, area].includes(val)
                        );
                    }
                }
                this.$nextTick(() => {
                    this.preSelectAreas = [...this.selectList];
                });
            });
        },
        handleExpandChange(paths) {
            if (!this.isLastMultiSelect) return;
            this.curRootPath = paths.join('/');
            if (this.isMultiAndNoPath && this.curRootPath) {
                this.preSelectList = [...this.selectList];
            }
        },
        getCurSelectArea(bigList, smallList) {
            let isCheck = true;
            if (bigList.length < smallList.length) {
                [bigList, smallList] = [smallList, bigList];
                isCheck = false;
            }
            if (bigList.length !== smallList.length) {
                return {
                    isCheck,
                    area: bigList.filter((val) => !smallList.includes(val))[0],
                };
            }
            return {
                isCheck: false,
                area: '',
            };
        },
    },
};
</script>
<style lang="less" scoped>
.select-district {
    width: 100%;
    .el-cascader-panel {
        width: 100%;

        /deep/ .el-cascader-menu {
            min-width: 100px;

            .el-cascader-node {
                padding-left: 8px;
            }

            &__wrap {
                .el-checkbox {
                    display: none;
                }
            }

            &:nth-of-type(3) {
                .el-cascader-menu__wrap {
                    .el-checkbox {
                        display: block;
                    }
                }
            }
        }
    }
    .el-input .el-input__inner:focus,
    .el-input.is-focus .el-input__inner {
        height: 32px; //这里高度根据需求自己设定
    }
    /deep/ .el-cascader__tags {
        display: inline-flex;
        margin-right: 60px;
        flex-wrap: nowrap;
        .el-cascader__search-input {
            min-width: 20px;
        }
    }
    /deep/ .el-cascader__search-input {
        margin-left: 10px;
        background: unset;
    }
}
</style>
<style lang="less">
.custom-hide-checkbox {
    .el-cascader-panel li[aria-haspopup] .el-checkbox {
        display: none;
    }
}
.custom-cascader {
    background-color: #101d34;
    border-color: #a8bfe8;
    .el-cascader-node {
        color: #dfe1ec;
    }
    .el-cascader-node:not(.is-disabled):hover {
        color: #409eff;
    }
    .popper__arrow {
        &::after {
            border-bottom-color: #a8bfe8 !important;
        }
    }
}
</style>
