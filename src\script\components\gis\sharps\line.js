import { toCoordinate, toPointSequence, formatBaseStations, getCoordinateExtremum } from '@/utils/method.js';

export default () => {
  let mapIdToDetailFn = {};
  class LineSegment {
  constructor(parent) {
    this.parent = parent;
    this.i = this.parent.i;
    this.points = [];
    this.distance = 0;
    this.showLineMesh = null;
  }
  static initGisInfo(g, that) {
    Object.assign(LineSegment, { g, that });

    if (that.isJustShowLayer) {
      // 定义纯展示图层
      const showLayer = new g.layer();
      showLayer.visible = true;
      g.gis.scene.add(showLayer.Group);
      LineSegment.showLayer = showLayer;
      // 双击事件
      g.event.addDoubleClick(LineSegment.showLayer, (data) => {
        const obj = data.object;
        const resourceId = obj.name;
        const setCurDetail = mapIdToDetailFn[resourceId];
        setCurDetail && setCurDetail();
      });
    } else {
      // 定义编辑图层
      LineSegment.initLayerAndEvent(g, that);
    }
  }
  static initLayerAndEvent(g) {
    LineSegment.layer = g.layerList.lineEditLayer;
    LineSegment.layer.lineWidth = 4;
    LineSegment.layer.color = 0x17beb0;
    // 事件
    LineSegment.layer.onEditFinish.addEvent(LineSegment.lineEditFinish, 'line');
  }
  static lineEditFinish(data) {
    const { that, g } = LineSegment;
    const parent = that.layerObj;
    const { points, distance } = parent.lineSegment.setPoints(data.points, g);
    parent.initExpandAndBaseStation(points);
    that.$emit('update:roadLength', distance);
    that.$eventBus.$emit('updateSharpInfo');
  }
  static clearAll() {
    const { that, layer, showLayer } = LineSegment;
    if (that.isJustShowLayer) {
      showLayer.removeAll();
    } else {
      layer.removeAll();
    }
  }
  static unbindEvent() {
    if (LineSegment.layer && LineSegment.layer.onEditFinish) {
      LineSegment.layer.onEditFinish.removeEvent('line');
    }
  }
  setPoints(oriPoints, g) {
    const points = [];
    let totalDistance = 0;
    for (const point_3 of oriPoints) {
      const point = g.math.three2world(point_3);
      points.push(point);
      if (points.length > 1) {
        const distance = g.math.worldDistance(points[points.length - 2], point)
        totalDistance += distance;
      }
    }
    this.points = points;
    this.distance = Number(totalDistance.toFixed(2));
    return {
      points: this.points,
      distance: this.distance,
    }
  }
  draw(points = []) {
    this.points = JSON.parse(JSON.stringify(points));
    LineSegment.layer.createLine({
      name: `line-${this.i}`,
      points,
    });
  }
  // 纯画线
  drawShowLine(points = this.points, id) {
    const { g, showLayer } = LineSegment;
    const data = [];
    points.forEach((point, i) => {
      if (i > 0) {
        data.push({
          start: { ...points[i - 1], ht: 0 },
          end: { ...point, ht: 0 },
          color: 0x17beb0,
        });
      }
    });
    Object.assign(data, {
      autoScale: true,
      width: 4
    });
    this.showLineMesh = g.meshList.line.create(data);
    id && (this.showLineMesh.name = id);
    showLayer.add(this.showLineMesh);
    g.gis.needUpdate = true;
    this.points = points;
  }
  clear() {
    LineSegment.showLayer.remove(this.showLineMesh);
  }
}
class Expansion {
  constructor(parent) {
    this.parent = parent;
    this.i = this.parent.i;
    this.points = [];
    this.plane = null;
    this.color = 0x1A7CFF;
    this.maxH = 0.01;
  }
  static initGisInfo(g, that) {
    Object.assign(Expansion, { g, that });
    Expansion.layer = new g.layer();
    Expansion.layer.visible = true;
    g.gis.scene.add(Expansion.layer);
  }
  static clearAll() {
    Expansion.layer.removeAll();
  }
  draw(editPoints = this.points) {
    this.delPlane();
    this.points = editPoints;

    const g = Expansion.g;
    const { color, maxH } = this;
    const points = editPoints.map((item) => [item.lng, item.lat]);
    const data = [
      {
        ls: g.math.lsRegular(points) || [],
        layers: [{ maxH, color }],
      },
    ];

    g.meshList.plane.opacity = 0.3;
    this.plane = g.meshList.plane.create(data);
    Expansion.layer.add(this.plane);
    g.gis.needUpdate = true;
  }
  delPlane() {
    if (this.plane) {
      Expansion.layer.remove(this.plane);
    }
  }
}

return class Line { // 
  constructor(i) {
    this.i = i;
    this.expansion = new Expansion(this, Line);
    this.lineSegment = new LineSegment(this, Line);
    this.innerBaseStations = [];
    this.baseStations = [];
  }
  static initGisInfo(g, that) {
    if (Line.isInitialized) {
      return;
    }
    Line.isInitialized = true;

    Object.assign(Line, { g, that, i: 1 });
    LineSegment.initGisInfo(g, that);
    Expansion.initGisInfo(g, that);
  }
  /**
   * Initialize the region coordinates.
   * @param {Array} regionCoors 
   */
  static initRegionCoors(regionCoors = [], isClearAll, isResetCheckBase) {
    const { g, that, initRegions } = Line;
    if (isClearAll) that.clearAll();
    if (isResetCheckBase) that.isCheckBase = false;

    const { mergeRegions } = initRegions(regionCoors, {
      cb: (curSharp, points, index) => {
        if (that.isShowHideManage) {
          that.layerObj = that.planeObjs[index] = curSharp;
          that.layerObj.initExpandAndBaseStation(points, that.baseInfo.regionId);
        }
      }
    });
    const extremum = getCoordinateExtremum(mergeRegions);
    g.cameraControl.zoomByPoints(extremum, 1);
  }
  static initRegions(lines = [], option = {}) {
    const { cb, config = {}, setCurDetail, row } = option;
    const { that } = Line;
    const mergeLines = [];
    const sharps = [];

    lines.forEach((line, index) => {
      const curSharp = new Line(index);
      const linePoints = toCoordinate(line);
      const points = JSON.parse(JSON.stringify(linePoints));

      if (that.isJustShowLayer || config.isJustShow) {
        let resourceId;
        if (setCurDetail) {
          resourceId = row.resourceId || row.regionId;
          mapIdToDetailFn[resourceId] = setCurDetail;
        }
        curSharp.drawShowLine(linePoints, resourceId);
      } else {
        curSharp.drawLine(linePoints, true);
      }

      sharps.push(curSharp);
      mergeLines.push(...points);

      typeof cb === 'function' && cb(curSharp, points, index);
    });
    return { mergeRegions: mergeLines, sharps };
  }
  drawRegion(points) {
    this.drawShowLine(points);
  }
  clear() {
    this.lineSegment.clear();
  }
  initExpandAndBaseStation(linePoints, regionId) {
    const { that } = Line;
    this.getBaseStations(linePoints, regionId).then(res => {
      const { innerBaseStations, expandPoints, baseStations } = res;
      Object.assign(this, {
        baseStations,
        innerBaseStations,
      })
      if (that.isCheckBase) {
        this.drawExpansion(expandPoints);
      } else {
        this.setExpandPoints(expandPoints);
      }

      that.setAllBaseStations().setBasePoints();
    })
  }
  async getBaseStations(points, regionId) {
    const { that } = Line;

    if (!points.length) {
      that.$message.warning('区域不存在');
      return;
    }

    const params = {
      type: 1,
      shapeType: 4,
      isMultiRegion: 0,
      regionCoors: toPointSequence(points),
      expand: that.expand,
    };

    if (regionId) {
      Object.assign(params, {
        type: 0,
        regionId,
      })
    }

    const res = await that.$post('getRegionExpansionCells', params);
    const { regionOutList, regionInnerList, roadAreaCoors } = res;
    let regionInnerListData = regionInnerList;
    if (params.regionId) {
      regionInnerListData = regionInnerList.filter((item) => item.cellType === 2);
      if (!regionInnerListData.length) {
        regionInnerListData = regionInnerList;
      }
    }
    const innerBaseStations = formatBaseStations(regionInnerListData, 'added');
    const outBaseStations = formatBaseStations(regionOutList, 'noAdd');
    const baseStations = [...innerBaseStations, ...outBaseStations];
    return {
      innerBaseStations,
      outBaseStations,
      expandPoints: toCoordinate(roadAreaCoors),
      baseStations,
    };
  }
  static setCurOperateItem(curOperateItem) {
    Line.curOperateItem = curOperateItem;
  }
  static clearAll(isClearEditEvent = true) {
    if (!Line.isInitialized) return;
    LineSegment.clearAll(isClearEditEvent);
    Expansion.clearAll();
  }
  static unbindEvent() {
    if (!Line.isInitialized) return;
    LineSegment.unbindEvent();
    Line.isInitialized = false;
  }
  static getParams(allAreas) {
    let regionParams = {};
    if (allAreas.length === 1) {
      regionParams = {
        isMultiRegion: 0,
        regionCoors: toPointSequence(allAreas[0].lineSegment.points),
      }
    }
    return regionParams;
  }
  drawShowLine(points, id) {
    this.lineSegment.drawShowLine(points, id);
  }
  drawLine(points) {
    this.lineSegment.draw(points);
  }
  setExpansion(isCheckBase = false) {
    if (isCheckBase) {
      this.drawExpansion();
    } else {
      this.expansion.delPlane();
    }
  }
  setExpandPoints(points) {
    this.expansion.points = points;
  }
  drawExpansion(points) {
    this.expansion.draw(points);
  }
};
}
