const earthQuakeEventList = [
    {
        name: '地震名称：',
        prop: 'earthquakeName',
        width: 256,
        span: 4,
    },
    {
        name: '震中：',
        prop: 'center',
        width: 256,
        span: 4,
        formatter: (data) => {
            const { centerLon, centerLat } = data;
            if (centerLon && centerLat) {
                return centerLon + ',' + centerLat;
            }
            return '-';
        },
    },
    {
        name: '震级（M）：',
        prop: 'earthquakeLevel',
        width: 424,
        className: 'level-text',
        span: 6,
    },
    {
        name: '深度（千米）：',
        prop: 'earthquakeDepth',
        span: 4,
    },
    {
        name: '发生时间：',
        prop: 'occurTime',
        width: 210,
        span: 6,
    },
    {
        name: '时间粒度：',
        prop: 'timeType',
        width: 210,
        span: 4,
        formatter: (data) => {
            const val = data.timeType;
            return ['', '天', '小时', '30分钟', '15分钟'][val] || '-';
        },
    },
    {
        name: '分析半径（米）：',
        prop: 'analysisRadius',
        width: 214,
        span: 4,
    },
    {
        name: '分析时间：',
        prop: 'time',
        width: 210,
        span: 6,
        formatter: (data) => {
            const { analysisStartTime, analysisEndTime } = data;
            if (analysisStartTime) {
                return analysisStartTime + ' - ' + analysisEndTime;
            }
            return '-';
        },
    },
];
const customEventList = [
    {
        name: '区域名称：',
        prop: 'earthquakeName',
        width: 283,
        span: 6,
    },
    {
        name: '灾害类型：',
        prop: 'disasterType',
        width: 256,
        span: 3,
        formatter: (data) => {
            const val = data.disasterType;
            const mapLabel = {
                1: '地震',
                2: '泥石流',
                3: '水灾',
                4: '山体滑坡',
                5: '火灾',
                6: '其他',
            };
            return mapLabel[val];
        },
    },
    {
        name: '发生时间：',
        prop: 'occurTime',
        width: 283,
        span: 5,
    },
    {
        name: '时间粒度：',
        prop: 'timeType',
        width: 210,
        span: 3,
        formatter: (data) => {
            const val = data.timeType;
            return ['', '天', '小时', '30分钟', '15分钟'][val] || '-';
        },
    },
    {
        name: '分析时间：',
        prop: 'time',
        width: 424,
        span: 6,
        formatter: (data) => {
            const { analysisStartTime, analysisEndTime } = data;
            if (analysisStartTime) {
                return analysisStartTime + ' - ' + analysisEndTime;
            }
            return '-';
        },
    },
];

const getPlatformOverview = (data = {}) => [
    {
        text: '基站总数(个)',
        img: 'baseStationsNum',
        num: data.stationTotal || 0,
    },
    {
        text: '退服基站数(个)',
        img: 'suspendedBaseStationsNum',
        num: data.stationOutNumber || 0,
    },
    {
        text: '退服率(%)',
        img: 'suspensionRate',
        num: data.stationOutProportion ? Number(data.stationOutProportion).toFixed(2) : 0,
    },
    {
        text: '恢复基站数(个)',
        img: 'restoreNum',
        num: data.recoveredStationNumber || 0,
    },
    {
        text: '新增退服基站数(个)',
        img: 'newStationOutNumber',
        num: data.newStationOutNumber || 0,
    },
];
const fields = [
    {
        prop: 'queryTime',
        label: '查询时间',
        element: 'el-date-picker',
        attrs: {
            clearable: true,
            type: 'datetime',
            'popper-class': 'earth-picker',
            format: 'yyyy-MM-dd HH:mm',
            'value-format': 'yyyy-MM-dd HH:mm:00',
            'picker-options': {
                start: '00:00',
                step: '00:10',
                end: '23:50',
            },
        },
        listeners: {
            change: (e) => {
                console.log('change', e);
            },
        },
        span: 4,
    },
    { span: 3 },
];
const stationFields = [
    {
        prop: 'status',
        label: '基站状态',
        attrs: {
            clearable: true,
            placeholder: '请选择',
        },
        size: 'mini',
        element: 'el-select',
        slot: {
            element: 'el-option',
            enums: [
                { label: '退服', value: 1 },
                { label: '正常', value: 2 },
                { label: '全部', value: 3 },
            ],
        },
        span: 24,
    },
];
const tableColumns = [
    {
        prop: 'lacCell',
        label: 'LACCELL',
    },
    {
        prop: 'netType',
        label: '网络制式',
        width: 125,
    },
    {
        prop: 'status',
        label: '基站状态',
        width: 125,
    },
    {
        prop: '操作',
        label: '操作',
        width: 80,
    },
];

export {
    earthQuakeEventList,
    customEventList,
    getPlatformOverview,
    stationFields,
    tableColumns,
    fields,
};
