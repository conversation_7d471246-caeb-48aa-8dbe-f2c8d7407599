<template>
  <div class="search-place">
    <el-input
      placeholder="请输入地址"
      v-model="addressInput"
      class="input-with-select"
      clearable
      @keyup.enter.native="poiSearch()"
    >
      <el-button
        class="search-btn"
        slot="append"
        icon="el-icon-search"
        @click="poiSearch()"
      ></el-button>
    </el-input>
    <el-card
      v-if="poiResultList.length"
      class="search-result"
      :class="{ 'closed-panel': isHidePanel }"
      shadow="always"
    >
      <ul class="tips-list vertical-scroll">
        <li
          class="tips"
          :class="{ active: poiActiveIndex === index }"
          v-for="(item, index) in poiResultList"
          :key="index"
          @click="clickPoiList(item, index)"
        >
          <div class="poi-name">{{ index + 1 }}.&nbsp;{{ item.NAME }}</div>
          <div class="address">{{ item.ADDRESS }}</div>
        </li>
      </ul>
      <div v-if="isSearch" class="fold-btn" @click="isHidePanel = !isHidePanel">
        <em class="el-icon-caret-left"></em>
      </div>
    </el-card>
    <el-empty v-else-if="isSearch && !poiResultList.length" description="暂无搜索结果"></el-empty>
  </div>
</template>
<script>
import Axios from 'axios';
export default {
  name: 'search-place',
  props: {
    isShow: {
      type: Boolean,
      default: false,
    },
    city: {
      type: Array,
      default: () => [],
    },
    innerCity: {
      type: Array,
      default: () => [],
    },
    gisCenterMove: {
      type: Function,
      default: () => {},
    },
    getMayType: {
      type: Function,
      default: () => {},
    },
  },
  data() {
    return {
      isSearch: false,
      poiResultList: [],
      pointData: {},
      poiActiveIndex: '',
      oldMeshIndex: '',
      addressInput: '',
      currentPage: 1,
      pageSize: 20,
      isHidePanel: false,
    };
  },
  methods: {
    // 搜索
    clickPoiList(item, index) {
      this.pointData = item;
      this.poiActiveIndex = index;
      this.oldMeshIndex = index;
      this.gisCenterMove(this.pointData);
    },
    //POI查询
    poiSearch() {
      const city = (this.city || []).flat();
      if (!city.length && !this.innerCity.length) {
        this.$exmessage('请先选择地市');
        return;
      }
      if (!this.pointData) {
        this.$message.warning('请先输入关键词');
        return;
      }
      // this.$popupLoading({
      //   show: true,
      //   message: '加载中...',
      // });
      const AxiosUrl =
        this.getMayType() === 'default' ? `${location.origin}/wzlocMapSearch` : `${location.origin}/mtex`;
      Axios({
        url: `${AxiosUrl}/SearchWebProject/PoiSearch`,
        method: 'get',
        headers: {
          key: 'ad6609c5afe3741b',
          Version: '1.0.0',
          ReqNo: '1111',
          'content-Type': 'application/xml',
        },
        responseType: 'blob',
        params: {
          data_type: 'POI',
          query_type: 'TQUERY',
          protocol: 'json',
          city: city[1] || this.innerCity[1],
          keywords: this.addressInput || '',
          page_num: this.pageSize,
          page: this.currentPage,
          qii: true,
          key: 'ad6609c5afe3741b',
        },
      })
        .then((res) => {
          let reader = new FileReader();
          reader.readAsText(res.data, 'GBK');
          const that = this;
          reader.onload = function () {
            that.handleResData(reader.result);
          };
          // this.$popupLoading({ show: false });
        })
        .catch((err) => {
          this.poiResultList = [];
          // this.$popupLoading({ show: false });
          let options = {
            title: '消息提示',
            content: '消息内容！',
            detail: `详细内容：${err}`,
          };
          this.$popupMessageWindow(options);
        });
    },
    // 处理查询返回数据
    handleResData(data) {
      this.isSearch = true;
      const resData = JSON.parse(data);
      this.poiResultList = resData.poi || [];
    },
  },
};
</script>
<style lang="less" scoped>
.search-place {
  margin-bottom: 0;
  font-size: 12px;
  line-height: 18px;
  /deep/ .el-input-group__append {
    background-color: #3871B3;
    color: #fff;
    border-color: #387183;
  }
  /deep/ .el-input__inner {
    border: none;
    background: #1F3459;
    border-radius: 6px 0 0 6px;
    border: 1px solid rgba(182,213,255,0.2);
  }
  .search-btn {
    padding: 11px;
    /deep/ i {
      color: #fff;
      font-weight: 600;
      font-size: 20px;
    }
  }
  .fold-btn {
    position: absolute;
    display: flex;
    justify-content: center;
    align-items: center;
    bottom: -30px;
    left: 50%;
    transform: translateX(-50%) rotate(90deg);
    height: 35px;
    width: 20px;
    background-color: #409eff;
    cursor: pointer;

    em {
      color: #ffff;
      font-size: 20px;
      transition: transform 0.5s;
    }
  }
  .search-result {
    position: absolute;
    top: 42px;
    left: 0;
    background: #fff;
    z-index: 10;
    font-size: 12px;
    line-height: 18px;
    width: 260px;
    height: 400px;
    overflow: inherit;
    ::v-deep .el-card__body {
      position: relative;
      padding: 0px;
      height: 100%;
    }
    .tips-list {
      height: 100%;
      overflow-y: auto;
    }
    .tips {
      border-top: 1px solid #eaeaea;
      padding: 10px;
      cursor: pointer;
      display: flex;
      flex-direction: column;
      &:hover {
        background: #e6f4ff;
      }
      &.active {
        background: #d3e9fb;
      }
      .poi-name {
        float: left;
        max-width: 200px;
        height: 22px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        margin-top: 1px;
        font-size: 14px;
        font-weight: 600;
      }
    }
    &.closed-panel {
      height: 0;
      transition: height 0.2s linear;
      .fold-btn {
        em {
          transform: rotate(180deg);
        }
      }
    }
  }
  .el-empty {
    margin-top: 2px;
    background-color: #fff;
  }
}
</style>
