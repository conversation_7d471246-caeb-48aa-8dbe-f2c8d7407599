const apiPrefix = process.env.VUE_APP_API; //公网api前缀
const testPrefix = process.env.VUE_APP_APIPREFIX || 'earthquake';
const getFullPath = (path) => {
    if (apiPrefix) {
        return window.origin + '/' + apiPrefix + testPrefix + path;
    }
    return window.origin + '/' + testPrefix + path;
};
const getSpacePath = (path, suffix = '/spaceResource') => {
    return window.origin + suffix + path;
};
export default {
    getTenant: getFullPath('locservice/usermanage/getTenantInfo'),
    getPage: getFullPath('/loc/disaster/salvage/earthquake/records/pages'),
    getOverview: getFullPath('/loc/disaster/salvage/earthquake/platform/overview'),
    taskCreate: getFullPath('/loc/disaster/salvage/task/create'),
    getTaskList: getFullPath('/loc/disaster/salvage/earthquake/task/pages'),
    getTaskResult: getFullPath('/loc/disaster/salvage/earthquake/task/result'),
    getBaseDistribution: getFullPath('/loc/disaster/salvage/earthquake/task/laccellDistribute'), //基站分布查询
    verify: getFullPath('/loc/user/login/verify'), // 鉴权
    getTopN: getFullPath('/loc/intelligence/crowd/pointRegion/topN'), // 人群topN查询
    s2HeatList: getFullPath('/loc/intelligence/heat/list'), // S2网格热力
    getSummary: getFullPath('/loc/intelligence/summary'), // 分析综述
    getS2Result: getFullPath('/loc/intelligence/earthquake/task/result/s2'), //栅格趋势分析
    s2Analysis: getFullPath('/loc/intelligence/s2/analysis'), //单个栅格分析
    flowAnalysis: getFullPath('/loc/intelligence/flow/analysis'), //流入流出人数分析
    downloadExcel:getFullPath('/loc/disaster/salvage/earthquake/downloadExcel'), //Excel数据报告下载
    checkBaseStation:getFullPath('/loc/disaster/salvage/task/checkBaseStation'), //基站校验
    addEarthquake: getFullPath('/loc/disaster/salvage/record/create'), //新增地震
    getBaseStationAlarm: getFullPath('/loc/baseStation/out/alert'), //基站退服告警
    getBaseStationOverview: getFullPath('/loc/baseStation/out/overview'), //退服基站概览
    getBaseStationPage: getFullPath('/loc/baseStation/out/page'), //退服基站分页查询
    getBaseStationRegion: getFullPath('/loc/baseStation/out/region'), //行政区查询
    // 空资
    getCenter: getSpacePath('/spaceCreate/getCenter'),
    downloadRegionTemplate: getSpacePath('/spaceCreate/downloadExcelTemplate'),
    downloadShp: getSpacePath('/fileCreate/downloadShapeZipTemplate'),
    // uploadRegionShapeExcel: getSpacePath('/regionMng/addRegionShapeByExcel'),
    searchRegionPrivate: getSpacePath('/regionMng/searchRegionPrivate'),
    // collectResource: getSpacePath('/likeManagement/collect'),
    subscribe: getSpacePath('/publicResource/addSubscribe'),
    getLacCiNumber: getSpacePath('/regionMng/airport/getLacCiNumber'),
    downloadCSV: getSpacePath('/spaceCreate/downloadCsvTemplate'),
    downloadGeo: getSpacePath('/spaceCreate/downloadGeojsonTemplate'),

    // 资源图层分类查询
    getCategoryPage: getSpacePath('/spaceCreate/searchCategoryPage'),
    // 分类-专有资源查询
    getPrivateCategory: getSpacePath('/spaceCreate/searchPrivateCategoryPage'),
    // 资源清单-专有资源列表查询（单个资源、区域组）
    getPrivateResourceList: getSpacePath('/owned/resource/list'),
    // 资源清单-新资源包列表查询
    getNewPrivateResourceList: getSpacePath('/owned/resource/list/onlyOwned'),
    // 资源清单-资源包列表查询
    getPackageResourceList: getSpacePath('/resourcePackage/owned/list'),
    // 资源清单-专有资源列表查询（单个资源、区域组）
    getPublicResourceList: getSpacePath('/public/resource/list'),
    // 资源清单-新专有资源列表查询（单个资源、区域组）
    getNewPublicResourceList: getSpacePath('/public/resource/list/onlyPublic'),
    // 区域修改记录
    getUpdateLog: getSpacePath('/spaceCreate/getUpdateLog'),
    // 单个图层详情查询
    queryOneRegion: getSpacePath('/spaceCreate/searchOneRegion'),
    // 区域列表
    queryZoneGroupDetail: getSpacePath('/regionGroup/oneDetails'),
    // 区域组概况查询
    queryRegionGroupDetail: getSpacePath('/regionGroup/statDetails'),
    // 租户区域信息推送接口
    pushBaseStations: getSpacePath('/regionPush/user'),
    // 资源是否被订阅查询
    queryIsSubscribed: getSpacePath('/resourcePackage/resourcePackageIsSubscribe'),
    // 区域批量删除接口
    delRegionList: getSpacePath('/spaceCreate/deleteRegionList'),
    // 批量资源包删除
    delResPackage: getSpacePath('/resourcePackage/resource/list/delete'),
    // 单个区域(好评、差评)
    feedback: getSpacePath('/likeManagement/region/feedback'),
    // 资源包概况
    queryResPackageDetail: getSpacePath('/resourcePackage/resOverview'),
    queryResPackageDetailList: getSpacePath('/resourcePackage/resDetails'),
    // 删除区域组
    delRegionGroup: getSpacePath('/regionGroup/delete'),
    // 机场和基站对应关系查询及GIS呈现
    getAirportLac: getSpacePath('/spaceCreate/airport/getLacCiNumber'),
    // 专有资源批量管理--批量专有资源编辑
    updateRegionByBatch: getSpacePath('/spaceCreate/updateRegionByBatch'),
    // 恢复历史区域轮廓信息
    recoverHistoryRegionShape: getSpacePath('/spaceCreate/recoverHistoryRegionShape'),
    // 转化接口
    transferImport: getSpacePath('/resource/transferImport'),

    // 获取行政区划信息
    getDistrict: getSpacePath('/aoi/getDistrict'),
    // 单个区域创建
    singleCreate: getSpacePath('/spaceCreate/addRegionShape'),
    // 批量区域创建
    multiCreate: getSpacePath('/spaceCreate/addRegionShapeByExcel'),
    // 图层编辑
    editLayer: getSpacePath('/spaceCreate/updateRegion'),
    // 重叠度校验
    overlapValid: getSpacePath('/spaceCreate/regionRepetitionRank'),
    // 获取区域类型详情
    getAreas: getSpacePath('/dimTblMixture/getAreaTypeDetail'),
    // 获取图层信息
    getLayers: getSpacePath('/dimensionTable/getLayer'),
    // 查询区域周边基站列表
    getRegionExpansionCells: getSpacePath('/cellManagement/getRegionExpansionCells'),
    // 区域基站列表编辑
    getRegionCellUpdate: getSpacePath('/cellManagement/regionCellUpdate'),
    // shape文件解析
    shapeFileReader: getSpacePath('/fileCreate/shapeFileReader'),
    // shape文件导入创建
    addRegionShapeByShp: getSpacePath('/fileCreate/addRegionShapeByShp'),
    // GeoJSON文件导入创建
    addRegionShapeByGeo: getSpacePath('/spaceCreate/addRegionShapeByGeo'),
    // GeoJSON文件解析返回映射字段
    geoJsonFileReader: getSpacePath('/spaceCreate/geoJsonFileReader'),
    // Excel、CSV文件异步资源创建
    asyncBulkCreate: getSpacePath('/fileCreate/addRegionByCsvAndExcel')
};
