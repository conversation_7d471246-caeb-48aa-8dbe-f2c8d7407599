<template>
    <card 
        ref="rightPanel"
        :img="require(`../../../../img/common/overview1.png`)"
        :title="title"
    >
        <div class="content earth-overflow-y">
            <card1 class="content-analysisOverview" title="分析综述">
                <div :style="[analysisOverviewStyle]" v-if="analysisOverview" :tips="analysisOverview" v-tooltip  class="analysisOverview earth-overflow-y" v-html="analysisOverview || '暂无数据'"></div>
                <el-empty :style="[analysisOverviewStyle]" class="analysisOverview" v-else :image="noData1"></el-empty>
            </card1>
            <card1 ref="coreInformation" title="核心信息">
                <div class="coreInformation">
                    <div v-for="(item,index) in coreInformationList" :key="index" class="coreInformation-item">
                        <img :src="require(`../../../../img/analyzeDetails/${item.icon}.png`)" alt="" />
                        <div class="info">
                            <div class="num">{{item.value!=null?item.value:'-'}}</div>
                            <div class="name">{{item.name}}</div>
                        </div>
                    </div>
                </div>  
            </card1>
            <card1 ref="trendAnalysis" title="趋势分析">
                <div class="trendAnalysis ">
                    <commonCharts :data="trendAnalysisChart" name="trendAnalysis"></commonCharts>
                </div>
            </card1>
            <card1 ref="portraitAnalysis" title="画像分析">
                <div class="portraitAnalysis ">
                    <div class="portraitAnalysis-chart">
                        <commonCharts  :data="portraitAnalysisChart" :name="'portraitAnalysis'+isActive"></commonCharts>
                    </div>
                    <div class="tab">
                        <div class="tab-item" :class="{'is_active':isActive === 'Gender'}" @click="isActive='Gender'">性别</div>
                        <div class="tab-item" :class="{'is_active':isActive === 'Age'}" @click="isActive='Age'">年龄</div>
                    </div>
                </div>
            </card1>
        </div>
    </card>
</template>

<script>
import card from '_com/card/index.vue';
import card1 from '_com/card/card1.vue';
import commonCharts from '_com/echarts/commonCharts.vue';
import { tooltip } from '@/script/common/directives/tooltip.js';
import {fontSizeRem} from '@/utils/utils.js';
import Vue from 'vue';
export default {
    name:'rightPanel',
    components:{
        card,
        card1,
        commonCharts
    },
    props:{
        type:{
            type:String,
            default:'受影响人群',
        },
        coreInformation:{
            type:Array,
            default:() => ['100000','32']
        },
        title:{
            type:String,
            default:'总览'
        },
        analysisOverview:{
            type:String,
            default:'一、总览：</br> 分析区域内基站数量共计个，在地震发生时区域内估计受影响人群达xxx人</br>二、详情：</br>1.从网格维度上看，<栅格区域名TOP1>、<栅格区域名TOP2>、<栅格区域名TOP3>、<栅格区域名TOP4>、<栅格区域名TOP5>等区域受影响人群最多；</br>2.从性别分布上看，主要为<性别>人群，占比xx%;</br>3.从年龄分布上看，年龄段人群居多，共计人。</br>hahahahahahahhahahahahahaha',
        },
        trendAnalysisData:{
            type:Object,
            default:() => ({
                xAxis:['11-01','11-02','11-03','11-04','11-05','11-06'],
                data:[100,2000,3764,3974,4975,4085],
            })
        },
        portraitAnalysisData:{
            type:Object,
            default:() => ({
                xAxis:['0-10岁','10-20岁','20-30岁','30-40岁','40-50岁','50-60岁','60-70岁','70-80岁','80岁以上'],
                data:[100,2000,3764,3974,4975,4085],
                pieData:[
                    {name:'男',value:10220,itemStyle:{color:'#04D9CF'}},
                    {name:'女',value:11220,itemStyle:{color:'#C99E2F'}},
                    {name:'未知',value:12220,itemStyle:{color:'#7D7D7D'}},
                ]
            })
        }
    },
    directives: {
        tooltip,
    },
    data(){
        return{
            isActive:'Gender',
            noData1: require('../../../../img/common/noData1.svg'),
            analysisOverviewStyle:{},
        }
    },
    computed:{
        coreInformationList(){
            return[
                {
                    name:this.type +'数',
                    value:this.coreInformation[0],
                    icon:'numberOfAffectedPopulation'
                },
                {
                    name:'基站数量',
                    value:this.coreInformation[1],
                    icon:'numberOfBaseStations'
                },
            ]
        },
        trendAnalysisChart(){
            return{
                title: '',
                type: 'common',
                xAxis:this.trendAnalysisData.xAxis,
                isNull:!this.trendAnalysisData.data.length,
                yAxis:[''],
                hasLegend:false,
                isxAxisBreak:true,
                data:[
                    {
                        name: '趋势分析',
                        data: this.trendAnalysisData.data,
                        itemStyle: {
                            color: '#7DACDC',
                        },
                        type: 'line',
                        showSymbol: true,
                        areaStyle: {
                            color: {
                                type: 'linear',
                                x: 0,
                                y: 0,
                                x2: 0,
                                y2: 1,
                                colorStops: [
                                    {
                                        offset: 0,
                                        color: 'rgba(125, 172, 220, 1)', // 0% 处的颜色
                                    },
                                    {
                                        offset: 0.5,
                                        color: 'rgba(125, 172, 220, 0.15)', // 0% 处的颜色
                                    },
                                    {
                                        offset: 1,
                                        color: 'rgba(125, 172, 220, 0.03)', // 100% 处的颜色
                                    },
                                ],
                                global: false, // 缺省为 false
                            },
                        },
                    },
                ]
            }
        },
        portraitAnalysisChart(){
            if(this.isActive === 'Gender'){
                const data = this.portraitAnalysisData.pieData;
                return{
                    type:'pie',
                    isNull:!this.portraitAnalysisData.pieData.length,
                    pieData:data,
                }
            }else{
                return{
                    title: '',
                    type: 'common',
                    isNull:!this.portraitAnalysisData.data.length,
                    xAxis:this.portraitAnalysisData.xAxis,
                    yAxis:[''],
                    hasLegend:false,
                    data:[
                        {
                            name: '人数',
                            data: this.portraitAnalysisData.data,
                            itemStyle:{
                                color:{
                                    type: 'linear',
                                    x: 0,
                                    y: 1,
                                    x2: 1,
                                    y2: 0,
                                    colorStops: [{
                                        offset: 0, color: '#1E3970' // 0% 处的颜色
                                    }, {
                                        offset: 1, color: '#4984FF' // 100% 处的颜色
                                    }],
                                    global: false // 缺省为 false
                                },
                            },
                            type: 'bar',
                            barWidth:10,
                        },
                    ]
                }
            }
        }
    },
    mounted(){
        window.addEventListener('resize', this.resize);
        const time = setTimeout(() => {
            this.resize();
            clearTimeout(time)
        }, 500);
    },
    methods:{
        resize(){
            const rightPanelHeight = this.$refs.rightPanel.$el.offsetHeight - 92;
            const coreInformationHeight = this.$refs.coreInformation.$el.offsetHeight;
            const trendAnalysisHeight = this.$refs.trendAnalysis.$el.offsetHeight;
            const portraitAnalysisHeight = this.$refs.portraitAnalysis.$el.offsetHeight;
            if(rightPanelHeight > 600){
                const diff = rightPanelHeight - coreInformationHeight - trendAnalysisHeight- portraitAnalysisHeight;
                this.$set(this.analysisOverviewStyle,'height',diff-80 + 'px');
            }
        }
    }
}
</script>

<style lang="less" scoped>
.content{
    width:100%;
    height:100%;
    display:flex;
    flex-direction:column;
    .analysisOverview{
        width:100%;
        font-size: 15px;
        color: #FFFFFF;
        line-height: 1.15rem;
        padding:0px 6px;
        min-height:90px;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    .coreInformation{
        width:100%;
        height:80px;
        padding: 0px 6px;
        display:flex;
        justify-content:space-between;
        .coreInformation-item{
            width:50%;
            height:70px;
            display:flex;
            align-items:center;
            padding-left:20px;
            img{
                width:70px;
                height:70px;
            }
            .info{
                padding-left:15px;
                .num{
                    font-size: 20px;
                    color: #FFFFFF;
                    line-height: 40px;
                    letter-spacing: 1px;
                    text-shadow: 0 0 5px #fff, 0 0 10px #fff, 0 0 15px #fff, 0 0 70px #ff00de, 0 0 70px rgba(62,136,233,0.64);
                    text-align: left;
                    font-weight:600;
                }
                .name{
                    font-weight: normal;
                    font-size: 14px;
                    color: #FFFFFF;
                    line-height: 20px;
                    text-shadow: 0px 0px 4px rgba(62,136,233,0.64);
                }
            }
        }
    }
    .trendAnalysis{
        width:100%;
        height:130px;
    }
    .portraitAnalysis{
        width:100%;
        height:168px;
        display: flex;
        flex-direction: column;
        align-items: center;
        &-chart{
            width:100%;
            height:140px;
        }
    }
    .tab{
        width:120px;
        height:24px;
        margin-top:5px;
        background: #13395E;
        border-radius: 4px;
        border: 1px solid #3871B3;
        backdrop-filter: blur(8px);
        display:flex;
        align-items:center;
        &-item{
            width:50%;
            color:#fff;
            font-size:14px;
            text-align:center;
            cursor:pointer;
            &.is_active{
                background: rgba(56,113,179,0.69);
                border-radius: 4px;
                backdrop-filter: blur(0px);
                border: 2px solid rgba(56, 113, 179, 0.69);
            }
        }
    }
}
</style>