<template>
    <transition name="fade">
        <div v-show="visible" class="dialogue-wrapper">
            <!-- 左侧边栏 -->
            <div v-if="isFullScreen && hasSidebar" class="sidebar"></div>
            <div :class="['mainBody', { isFullScreen: isFullScreen }]">
                <!-- 头部标题 -->
                <titleBar v-bind="$attrs" :isFullScreen="isFullScreen"></titleBar>
                <!-- 内容体 -->
                <div class="dialogue-wrapper-conversations">
                    <conversations ref="conversations" :talkRecords="talkRecords"></conversations>
                </div>
                <!-- 输入框 -->
                <div class="dialogue-wrapper-inputBox">
                    <inputBox
                        @sendText="sendText"
                        :modelOpts="modelOpts"
                        :model.sync="model"
                    ></inputBox>
                </div>
                <!-- 免责声明 -->
                <div class="dialogue-wrapper-bottomStatement">
                    {{ bottomStatement }}
                </div>
            </div>
        </div>
    </transition>
</template>

<script>
import conversations from './conversations.vue';
import inputBox from './inputBox.vue';
import titleBar from './titleBar.vue';
import businessComponents from '../business/index.js';
import TalkRecord from '../common/talkRecord.js';

export default {
    name: 'dialogue',
    components: {
        conversations,
        inputBox,
        titleBar,
    },
    props: {
        title: {
            type: String,
            default: '云南大理地震灾害报告分析',
        },
        visible: {
            type: Boolean,
            default: false,
        },
        modelOpts: {
            type: Array,
            default: () => [],
        },
        model: {
            type: String,
            default: 'DeepSeek-V3',
        },
        recommendationList: {
            type: Array,
            default: () => [],
        },
        isFullScreen: {
            type: Boolean,
            default: false,
        },
        hasSidebar: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            talkRecords: [],
            curTalkRecord: {},
            bottomStatement: '内容由 AI 生成，仅供参考，您据此所作判断及操作均由您自行承担责任。',
        };
    },
    computed: {
        curModel() {
            const modelItem = this.modelOpts.find((item) => item.value === this.model);
            return modelItem || {};
        },
        isStream() {
            return Boolean(this.curModel.stream);
        },
    },
    watch: {},
    // 接口调用注入
    mounted() {},
    methods: {
        sendText(text) {
            const talkRecord = TalkRecord.createTalkRecord('user');
            talkRecord.setText(text);
            this.addTalkRecord(talkRecord);

            this.next('createEarthInfo', {
                text,
            });
        },

        next(name, lastData) {
            const { component, nextStep } = businessComponents[name];
            this.curTalkRecord = TalkRecord.createTalkRecord('robot');
            this.addTalkRecord(this.curTalkRecord);
            // 获取组件内容说明
            this.requestData(lastData)
                .then((data = {}) => {
                    // 文字打印结束
                    this.curTalkRecord.setText(data.text);
                    this.curTalkRecord.setTextStatus('success');
                    this.curTalkRecord.setComponent(component, data, {
                        setStatus: (status) => {
                            this.curTalkRecord.setStatus(status);
                        },
                        setData: (data) => {
                            this.curTalkRecord.setData(data);
                        },
                        submit: (res) => {
                            this.next(nextStep, res);
                        },
                    });

                })
                .catch((err) => {
                    console.error('获取组件内容失败', err);
                  /*   // 使用默认内容作为备选
                    const record = this.createBusinessComponent(name, data, 'defaultContent');
                    this.addTalkRecord(record); */
                });
        },

        // 从接口获取组件内容说明
        requestData() {
            // 这里应该调用实际的API
            return new Promise((resolve) => {
                // 模拟API调用
                setTimeout(() => {
                    // 根据组件名返回不同的内容
                    resolve({
                        text: '接口内容返回了',
                        data: '未完待续'
                    });
                }, 2000);
            });
        },

        addTalkRecord(record) {
            this.talkRecords.push(record);
            return this.talkRecords;
        },
        getConclusionStream() {},
        // 从字符串中提取content内容的工具函数 - 优化版本
        extractContentFromStream(text) {
            // 使用字符串索引方法，性能更好
            let content = '';
            let startIndex = 0;
            const searchStr = '"answer":"';
            let hasMoreContent = true;
            while (hasMoreContent) {
                // 查找下一个content标记
                const contentIndex = text.indexOf(searchStr, startIndex);
                if (contentIndex === -1) {
                    hasMoreContent = false;
                    break;
                }

                // 找到content值的开始位置
                const valueStart = contentIndex + searchStr.length;
                // 找到content值的结束位置（下一个双引号）
                const valueEnd = text.indexOf('"', valueStart);
                if (valueEnd === -1) {
                    hasMoreContent = false;
                    break;
                }

                // 提取content值并添加到结果中
                content += text.substring(valueStart, valueEnd);
                // 更新下一次搜索的起始位置
                startIndex = valueEnd + 1;
            }

            return content;
        },
    },
};
</script>

<style lang="less" scoped>
.fade-enter-active,
.fade-leave-active {
    transition: opacity 0.7s;
}
.fade-enter,
.fade-leave-to {
    opacity: 0;
}
.dialogue-wrapper {
    display: flex;
    // flex-direction: column;
    overflow: hidden;
    .sidebar {
        width: 410px;
    }
    .mainBody {
        flex: 1;
        height: 100%;
        display: flex;
        flex-direction: column;
        &.isFullScreen {
            padding: 0 100px;
        }
    }
    &-title {
        width: 100%;
        height: 57px;
        border-bottom: 1px solid var(--baseBorderColor);
        display: flex;
        align-items: center;
        padding: 15px;
        justify-content: space-between;
        .title {
            font-size: var(--baseFontSize);
            color: var(--baseTextColor);
            font-weight: bold;
        }
    }
    &-conversations {
        flex: 1;
        width: 100%;
        letter-spacing: 1px;
        overflow: hidden;
    }
    &-inputBox {
        padding: 15px 32px;
        width: 100%;
        height: 152px;
        box-sizing: border-box;
    }
    &-bottomStatement {
        width: 100%;
        height: 30px;
        text-align: center;
        line-height: 30px;
        font-size: 13px;
        color: var(--subTextColor);
    }
    &-overflow {
        overflow-y: auto;
        &::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }
        &::-webkit-scrollbar-thumb {
            border-radius: 10px;
            box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
            background: #44c7ff;
        }
        &::-webkit-scrollbar-track {
            /* 滚动条里面轨道 */
            box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
            border-radius: 10px;
            background: transparent;
        }
        &::-webkit-scrollbar-corner {
            background: rgba(0, 0, 0, 0);
        }
    }
}
</style>
