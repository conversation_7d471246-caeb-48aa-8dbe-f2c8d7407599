<template>
    <div ref="details" id="details" class="analyze-details">
        <Transition name="fade-top">
            <div v-show="show" class="analyze-details-top">
                <div class="title">{{ routeParmas.taskName }}</div>
                <div class="show-list">
                    <el-row :gutter="10" class="w-full">
                        <el-col
                            style="display: flex; align-items: center"
                            :span="item.span"
                            v-for="(item, index) in listDisplay"
                            :key="index"
                        >
                            <span class="name">{{ item.name }}</span>
                            <span
                                v-tooltip
                                :tips="item.name + timeTypeList[routeParmas[item.props]]"
                                v-if="item.props === 'timeType'"
                                class="value earth-text-ellipsis"
                                >{{ timeTypeList[routeParmas[item.props]] || '-' }}</span
                            >
                            <span
                                v-tooltip
                                :tips="item.name + analysisTime"
                                v-else-if="item.props === 'time'"
                                class="value earth-text-ellipsis"
                                >{{ analysisTime }}</span
                            >
                            <span
                                v-tooltip
                                :tips="item.name + routeParmas[item.props]"
                                v-else
                                class="value earth-text-ellipsis"
                            >
                                {{
                                    item.formatter
                                        ? item.formatter(routeParmas[item.props])
                                        : routeParmas[item.props] || '-'
                                }}
                            </span>
                        </el-col>
                    </el-row>
                </div>
            </div>
        </Transition>
        <div class="analyze-details-content">
            <gisMap
                v-loading="isLoading"
                element-loading-text="拼命加载中"
                element-loading-spinner="el-icon-loading"
                element-loading-background="rgba(0, 0, 0, 0.8)"
                @loaded="gisLoaded"
            />
            <div class="top-tab">
                <div class="tab">
                    <img
                        :src="
                            require(`../../../img/analyzeDetails/affectedPopulation${
                                isActive === 1 ? '_active' : ''
                            }.png`)
                        "
                        class="tab-item"
                        @click="isActive = 1"
                    />
                </div>
                <div class="baseStation">
                    <el-checkbox v-model="checked">基站</el-checkbox>
                </div>
            </div>
            <Transition name="fade-left">
                <div v-show="hasShowLeft" class="left-panel">
                    <leftPanel :overview="overview" />
                    <div class="left-shrink" title="收起" @click="hasShowLeft = false"></div>
                </div>
            </Transition>
            <Transition name="fade-bottom">
                <div v-show="show" class="bottom-panel">
                    <div class="text">热力图时间选择</div>
                    <timeContol
                        ref="timeContol"
                        :dataList="mapTimeLineData"
                        @timeLineChangeAction="timeLineChangeAction"
                    />
                </div>
            </Transition>
            <div class="legend">
                <mapLegend :legends="legends" />
            </div>
        </div>
    </div>
</template>

<script>
import { listDisplay } from '_const/resultProduct.js';
import { timeTypeList } from '_const/analyzeProduct.js';
import gisMap from '_com/gisMap/index.vue';
import leftPanel from './components/leftPanel.vue';
import timeContol from '_com/timeLine/timeContol.vue';
import mapLegend from '_com/gisMap/mapLegend.vue';
import initCircular from '_com/gisMap/layer/circular.js';
import initRing from '_com/gisMap/layer/circle.js';
import initGrid from '_com/gisMap/layer/Grid.js';
import initPoint from '_com/gisMap/layer/point.js';
import initLine from '_com/gisMap/layer/line.js';
import { tooltip } from '@/script/common/directives/tooltip.js';
import { toCoordinate } from '@/utils/method.js';
// import { union } from '@/utils/gisUtils.js';
// import { routeParamOfCircle, routeParamOfPolygon, overviewData } from './mock';
const dayjs = require('dayjs');
import html2canvas from 'html2canvas';
export default {
    name: 'resultProduct',
    components: {
        gisMap,
        leftPanel,
        timeContol,
        mapLegend,
    },
    data() {
        return {
            rightTitle: '总览',
            routeParmas: {},
            listDisplay,
            timeTypeList,
            isActive: 1,
            checked: false,
            show: false,
            mapTimeLineData: [],
            currentTimeLineValue: '',
            legends: [
                {
                    hexBgColor: 0xff3333,
                    background: '#FF3333',
                    mapColor: 0xff3333,
                    value: '200以上',
                },
                {
                    hexBgColor: 0xffa040,
                    background: '#FFA040',
                    mapColor: 0xffa040,
                    value: '101-200',
                },
                { hexBgColor: 0xffff02, background: '#FFFF02', mapColor: 0xffff02, value: '1-100' },
                { hexBgColor: 0x00be42, background: '#00BE42', mapColor: 0x00be42, value: '0' },
            ],
            s2List: [],
            lacellList: [],
            coreInformation: [],
            typeList: ['受影响人群', '疑似失联人群'],
            isLoading: false,
            hasShowLeft: true,
            overview: {},
        };
    },
    directives: {
        tooltip,
    },
    computed: {
        analysisTime() {
            const { analysisStartTime, analysisEndTime } = this.routeParmas;
            if (analysisStartTime) {
                return analysisStartTime + ' - ' + analysisEndTime;
            }
            return '-';
        },
        routeQuery() {
            return this.$route.query;
        },
    },
    watch: {
        checked: {
            handler(newV) {
                this.basePoint && this.basePoint.removeAll();
                this.pointCircle && this.pointCircle.removeAll();
                this.pointRange && this.pointRange.removeAll();
                if (newV) {
                    this.drawBaseStation();
                }
            },
        },
        isActive: {
            handler(newV) {
                this.initData(newV);
                this.resetOverview();
            },
        },
    },
    mounted() {
        this.$nextTick(() => {
            this.show = true;
            this.initialization();
            window.addEventListener('message', (e) => {
                if (e.origin == window.origin && e.data.type == 'screenshot') {
                    this.screenshot(e.data.sourceUrl);
                }
            });
        });
    },
    activated() {
        this.$nextTick(() => {
            this.show = true;
            this.initialization();
        });
    },
    methods: {
        screenshot(sourceUrl) {
            setTimeout(() => {
                const node = document.body;
                window.pageYoffset = 0;
                document.documentElement.scrollTop = 0;
                document.body.scrollTop = 0;
                html2canvas(node, {
                    scale: 2,
                    useCORS: true,
                    scrollX: 0,
                    scrollY: 0,
                    height: node.scrollHeight, //canvas高
                    width: node.scrollWidth, //canvas宽
                    windowHeight: node.scrollHeight,
                    windowWidth: node.scrollWidth,
                }).then((canvas) => {
                    const screenUrl = canvas.toDataURL('image/png');
                    window.parent.postMessage(
                        {
                            type: 'screenshot',
                            content: screenUrl,
                            sourceUrl: sourceUrl,
                        },
                        window.origin
                    );
                });
            }, 3000);
        },
        initialization() {
            const params = sessionStorage.getItem('routeParmas') || '{}';
            if (JSON.stringify(this.$route.params) !== '{}') {
                this.routeParmas = this.$route.params;
            } else if (JSON.stringify(params) !== '{}') {
                this.routeParmas = JSON.parse(params);
            }
            // test
            // this.routeParmas = routeParamOfPolygon;

            sessionStorage.setItem('routeParmas', JSON.stringify(this.routeParmas));
            this.initData();
        },
        initData(type = 1) {
            this.rightTitle = '总览';
            this.line && this.line.removeAll();
            this.$refs.timeContol && (this.$refs.timeContol.activeDayItem = 0);

            // this.overview = overviewData || {};
            // this.drawGraph(this.overview.accPepCnt)
            // return;

            this.getTrendAnalysisChartData(type);
        },
        gisLoaded(g) {
            this.g = g;
            this.circular = initCircular(g, {});
            this.Grid = initGrid(g);
            this.basePoint = initPoint(g, {
                img: require('../../../img/icon/baseStation_icon.png'),
            });
            this.pointCircle = initRing(g, {});
            this.pointRange = initGrid(g, {
                needFrame: false,
            });
            this.line = initLine(g);
            // this.initCircle(g);
            const layer = g.tileLayerList['高德底图'].Group;
            g.event.addClick(layer, () => {
                if (this.rightTitle !== '总览') {
                    this.handleClickOther();
                }
            });
        },
        // 点击其他处总览数据展示
        handleClickOther() {
            this.initData();
            this.resetOverview();
        },
        // 重置为总览数据
        resetOverview() {
            this.line.removeAll();
            const { centerLon, centerLat, analysisRadius } = this.routeParmas;
            const startPoint = { lat: centerLat, lng: centerLon };
            this.circular.toMoveCenter(startPoint, analysisRadius);
        },
        drawGraph(accPepCnt = 0) {
            const { shapeType, regionCoors } = this.routeParmas;
            const isCircle = Number(shapeType) === 1;
            const color = this.getColorByCnt(accPepCnt);
            if (isCircle) {
                this.initCircle(color);
            } else {
                const data = {
                    centerPoint: {},
                    points: toCoordinate(regionCoors),
                    config: {
                        color: color,
                    },
                    data: {},
                };
                this.Grid && this.Grid.createGrids([data], null, true, true);
            }
        },
        initCircle(color) {
            const { centerLon, centerLat, analysisRadius } = this.routeParmas;
            if (!centerLon || !this.g) {
                return;
            }
            const startPoint = { lat: centerLat, lng: centerLon };
            this.circular.createCircular({
                circleColor: color,
                circleOpacity: 0.1,
                circleFrame: true,
                circleFrameColor: 0x7dacdc,
                cirCleShowClose: true,
                circleShowRadius: false,
                radius: Number(analysisRadius),
                startPoint: startPoint,
            });
        },
        creatTimeLineData(slicesData) {
            const { occurTime, earthquakeLevel } = this.routeParmas;
            // const slicesData = this.sliceTime(analysisStartTime, analysisEndTime, timeType);
            this.mapTimeLineData = slicesData;
            this.currentTimeLineValue = this.mapTimeLineData[0];
            this.earthquakeData = { occurTime, earthquakeLevel };
        },
        // 时间切片函数
        sliceTime(startTime, endTime, timeType) {
            const result = [];
            let currentTime = new Date(startTime);

            while (currentTime <= new Date(endTime)) {
                result.push(dayjs(currentTime).format('YYYY-MM-DD HH:mm:ss')); // 保存当前时间
                // 根据切片间隔更新当前时间
                if (timeType === 1) {
                    currentTime.setDate(currentTime.getDate() + 1);
                } else if (timeType === 2) {
                    currentTime.setHours(currentTime.getHours() + 1);
                } else if (timeType === 3) {
                    currentTime.setMinutes(currentTime.getMinutes() + 15);
                }
            }
            //15分钟粒度，去掉第一个时间切片
            if (timeType === 3) {
                if (result.length > 0) {
                    result.shift();
                }
            } else if (timeType === 2) {
                if (result.length > 0) {
                    result.pop();
                }
            }

            return result;
        },
        //时间轴切换
        timeLineChangeAction(params) {
            this.currentTimeLineValue = params;
            this.getS2Analysis(params);
        },
        //右侧分析数据
        getS2Analysis(time, s2Id) {
            this.getPost(
                'post',
                's2Analysis',
                {
                    startTime: time,
                    endTime: time,
                    populationTargetId: this.isActive,
                    s2Id: s2Id,
                    taskId: this.routeParmas.taskId,
                },
                '获取栅格信息',
                (res) => {
                    this.overview = res || {};
                    this.drawGraph(this.overview.accPepCnt);
                    if (s2Id) {
                        this.rightTitle = '网格名称：' + (res.gridName || '');
                    }
                }
            );
        },
        // 渲染基站
        drawBaseStation() {
            this.basePoint && this.basePoint.removeAll();
            this.pointCircle && this.pointCircle.removeAll();
            const { taskId } = this.routeParmas;
            let params = {
                taskId,
            };
            this.getPost('post', 'getBaseDistribution', params, '获取基站分布', (res) => {
                this.lacellList = (res.detailList || []).map((it) => {
                    return {
                        latLng: {
                            lat: it.cgiLat,
                            lng: it.cgiLng,
                        },
                        data: it,
                    };
                });
                this.basePoint.createPoints(this.lacellList);
                //
                const { polygonList, circleList } = this.getBaseStationOutline(this.lacellList);
                this.pointCircle.createCircles(circleList);
                // 将polygonList按每10个为一组进行切分，用setInterval每隔1.5秒渲染一组
                const chunkSize = 10;
                const chunks = [];
                
                // 先将数据切分成多个数组
                for (let i = 0; i < polygonList.length; i += chunkSize) {
                    chunks.push(polygonList.slice(i, i + chunkSize));
                }
                
                // 使用setInterval逐个渲染
                let currentIndex = 0;
                const renderInterval = setInterval(() => {
                    if (currentIndex < chunks.length) {
                        this.pointRange.createGrids(chunks[currentIndex], null, false);
                        currentIndex++;
                    } else {
                        clearInterval(renderInterval);
                    }
                }, 1500); // 每1.5秒渲染一组
            });
        },
        getBaseStationOutline(points = []){
            const polygonList = [];
            const circleList = [];
            for (const [index, point] of points.entries()) {
                const { latLng, data } = point;
                const { regionCoors = '' } = data;
                if (regionCoors) {
                    // 若index为奇数，就加入到polygonList
                    if (index % 2 === 1) {
                        polygonList.push({
                            centerPoint: {},
                            points: toCoordinate(regionCoors),
                            config: {
                                color: 0xff3333,
                            },
                            data: {},
                        });
                    }
                } else {
                    circleList.push(latLng);
                }
            }
            return {
                polygonList,
                circleList,
            };
        },
        // 清空数据
        clearData() {
            this.mapTimeLineData = [];
            this.currentTimeLineValue = '';
            this.s2List = [];
            this.lacellList = [];
        },
        //获取总览趋势分析折线图数据
        getTrendAnalysisChartData(type) {
            const { analysisStartTime, analysisEndTime, taskId } = this.routeParmas;
            let params = {
                startTime: analysisStartTime,
                endTime: analysisEndTime,
                taskId,
                burialPepTaskPara: {
                    popType: type,
                    dataType: 2,
                },
                pageSize: 300,
                currentPage: 1,
            };
            this.getPost('post', 'getTaskResult', params, '获取趋势分析折线图信息', (res) => {
                const detailList = res.detailList;
                if (detailList && detailList.length) {
                    this.creatTimeLineData(detailList.map((item) => item.dataTime));
                    this.getS2Analysis(this.currentTimeLineValue);
                } else {
                    this.clearData();
                }
            });
        },
        getColorByCnt(accPepCnt) {
            const parseRange = (value) => {
                if (value.includes('以上')) {
                    const min = parseInt(value);
                    return { min, max: Infinity };
                }
                if (value.includes('-')) {
                    const [min, max] = value.split('-').map(Number);
                    return { min, max };
                }
                const exact = parseInt(value);
                return { min: exact, max: exact };
            };

            for (const legend of this.legends) {
                const { min, max } = parseRange(legend.value);
                if ((min === max && accPepCnt === min) || (accPepCnt >= min && accPepCnt <= max)) {
                    return legend.hexBgColor;
                }
            }
            return 0x00be42; // 默认颜色
        },
    },
};
</script>

<style lang="less" scoped>
.analyze-details {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;
    &-top {
        height: 100px;
        background: linear-gradient(270deg, #101620 0%, #1b2f4d 100%);
        padding: 15px 40px 10px;
        .title {
            font-size: @font-size-h1;
            font-weight: bold;
            color: #ffffff;
            line-height: 28px;
            text-shadow: 0px 0px 4px rgba(62, 136, 233, 0.64);
            position: relative;
            &::after {
                position: absolute;
                content: '';
                left: -15px;
                top: 10px;
                width: 8px;
                height: 8px;
                border-radius: 50%;
                background: #53ffff;
            }
        }
    }
    .show-list {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        &-item {
            padding: 15px 80px 0px 0;
        }
        .name {
            font-weight: 400;
            font-size: 16px;
            color: #ffffff;
            width: auto;
            white-space: nowrap;
        }
        .value {
            font-weight: 400;
            font-size: 16px;
            color: #ffffff;
        }
        .level-text {
            font-size: 18px;
            font-weight: 600;
            color: #ffa040;
            font-style: italic;
        }
        /deep/.el-col {
            padding-top: 10px;
        }
    }
    &-content {
        width: 100%;
        height: calc(100% - 100px);
        position: relative;
        .left-panel {
            position: absolute;
            left: 24px;
            top: 24px;
            width: 280px;
            .left-shrink {
                width: 20px;
                height: 92px;
                position: absolute;
                right: -1.25rem;
                top: calc(50% - 46px);
                cursor: pointer;
                background: url('../../../img/icon/left-shrink.png') no-repeat center center / 100%
                    100%;
                &:hover {
                    background: url('../../../img/icon/left-shrink-active.png') no-repeat center
                        center / 100% 100%;
                }
            }
        }
        .left-open {
            width: 20px;
            height: 92px;
            position: absolute;
            left: 24px;
            top: calc(50% - 96px);
            cursor: pointer;
            background: url('../../../img/icon/left-open.png') no-repeat center center / 100% 100%;
            &:hover {
                background: url('../../../img/icon/left-open-active.png') no-repeat center center /
                    100% 100%;
            }
        }
        .top-tab {
            width: 100%;
            height: 46px;
            position: absolute;
            top: 24px;
            display: flex;
            justify-content: center;
            .tab-item {
                width: 160px;
                height: 46px;
                cursor: pointer;
                margin-right: 15px;
            }
            .baseStation {
                width: 92px;
                height: 32px;
                background: rgba(23, 42, 65, 0.6);
                box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.65);
                border-radius: 2px;
                backdrop-filter: blur(8px);
                position: absolute;
                right: 20px;
                /deep/.el-checkbox {
                    padding: 5px 17px;
                    height: 32px;
                }
                /deep/.el-checkbox__inner {
                    background: rgba(125, 172, 220, 0.2);
                    box-shadow: 0px 0px 30px 0px rgba(0, 245, 242, 0.2);
                    border-radius: 1px;
                    border: 1px solid #7dacdc;
                }
                /deep/.el-checkbox__label {
                    color: #fff;
                }
            }
        }
        .bottom-panel {
            position: absolute;
            bottom: 0px;
            width: 100%;
            height: 100px;
            // background-image:url('../../../img/analyzeDetails/thermalPanel.png');
            // background-repeat:no-repeat;
            // background-size:100% 100%;

            background: url('../../../img/analyzeDetails/thermalPanel.png') no-repeat center center /
                100% 100%;
            display: flex;
            flex-direction: column;
            .text {
                text-align: center;
                font-size: 16px;
                color: #ffffff;
                line-height: 22px;
                text-shadow: 0 0 5px #fff, 0 0 10px #fff, 0 0 15px #fff, 0 0 70px #ff00de,
                    0 0 70px rgba(62, 136, 233, 0.64);
                padding: 10px 0px 0px;
            }
        }
        .legend {
            position: absolute;
            right: 20px;
            bottom: 160px;
        }

        .num-tips {
            position: absolute;
            left: 600px;
            top: 100px;
            z-index: 666;
        }
    }
}
.w-full {
    width: 100%;
}
/deep/.epicenter {
    position: relative;
    background: url('../../../img/analyzeDetails/epicenter.png') no-repeat center center / 100% 100%;
    width: 48px;
    height: 52px;
    transform: translate(-50%, -50%);
}
</style>
