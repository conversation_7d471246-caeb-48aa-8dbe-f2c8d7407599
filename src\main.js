import Vue from 'vue'
// import App from './script/plugin2x/main/indexPlugin.vue'
import App from './App'
import router from './script/router'
import store from './script/store'
import './style/index.less'
import ElementUI from 'element-ui';
import 'element-ui/lib/theme-chalk/index.css';
// import './utils/flexible.js';
import commonMixins from './script/mixins/commonMixins.js';
import './script/components/copilot/icons/iconfont.css';
import $ from 'jquery';
window.baseMapUrl = process.env.VUE_APP_BASE_MAP_URL || '39755BEB33D55533A333BC8447E4D7CF4CD86CE26E552510A32CA5B56BC38FAFD46230781252C29C9E5138F3D8363F1982471E5FC2458885E91BB9F7B34E969E37C6B9D673276DD88A4B19A920F3EEAF841F478BBEB3D5FFDE9B6A6FD7E6E6E5828BEAE197B2079419FE6AC8FE7BAF14578E56FE569847F36E7CE8F6C3F1FCF50D1D40FAD9FD38E37813F06FB3D5EB13';
// import './permission.js';

window.$  = $ ;
Vue.use(ElementUI);
Vue.use({
  install($vue, store) {
      $vue.mixin(commonMixins);
  },
});
Vue.prototype.$eventBus = new Vue();

Vue.config.productionTip = false

new Vue({
  router,
  store,
  render: h => h(App)
}).$mount('#app')
