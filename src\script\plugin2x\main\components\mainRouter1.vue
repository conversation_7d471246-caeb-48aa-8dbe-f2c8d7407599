<!-- 头部导航栏 -->
<template>
  <div class="main-router">
    <div class="flex-row logo-box">
      <img :src="logo" alt="" /><span class="logo-text">基于通讯信号大数据的掩埋人员规模及分布快速评估系统</span>
    </div>
    <div class="router-out">
      <el-menu
        :default-active="activeIndex"
        class="el-menu-demo"
        mode="horizontal"
        background-color="#ffffff"
        text-color="#fff"
        active-text-color="#fff"
        @select="selectMenu"
      >
        <template v-for="item in menuList">
          <el-menu-item
            v-if="!item.children && !item.groups"
            :class="{ disabled: item.disabled }"
            :key="item.value"
            :index="item.value"
          >
            {{ item.label }}
          </el-menu-item>
          <!-- 分组子菜单 -->
          <el-submenu
            v-else-if="item.groups"
            :index="item.value"
            :key="item.label"
            :disabled="item.disabled"
            popper-class="custom-group-submenu"
          >
            <template slot="title">{{ item.label }}</template>
            <el-menu-item-group
              v-for="group in item.groups"
              :title="group.label"
              :key="group.label"
            >
              <el-menu-item
                v-for="(child, index) in group.children"
                :index="child.value"
                :key="index"
                :disabled="child.disabled"
                >{{ child.label }}
              </el-menu-item>
            </el-menu-item-group>
          </el-submenu>
          <!-- 普通子菜单 -->
          <el-submenu
            v-else
            :index="item.value"
            :key="`${item.label}-subItem`"
            popper-class="custom-submenu"
            :disabled="item.disabled"
          >
            <template slot="title">{{ item.label }}</template>
            <el-menu-item
              v-for="(child, index) in item.children"
              :index="child.value"
              :key="index"
              :disabled="child.disabled"
              >{{ child.label }}
            </el-menu-item>
          </el-submenu>
        </template>
      </el-menu>
    </div>
    <div class="flex-row">
      <!-- <el-popover placement="bottom" width="230" trigger="hover" popper-class="user-popover">
        <div class="infor-box">
          <div class="user-text">账号ID: {{ userInfo.id }}</div>
          <div class="layout-click cursor" @click="layout">退出登录</div>
        </div>
        <div class="flex-row user-infor" slot="reference">
          <img class="name" :src="userIcon" alt="" />
          <div class="whole-name" :title="userInfo.describe">
            {{ userInfo.describe }}
          </div>
          <i class="el-icon-arrow-down select-down"></i>
        </div>
      </el-popover> -->
    </div>
  </div>
</template>
<script>
import logo from '../../../../img/common/logo.svg';
import userIcon from '@/img/common/userIcon.svg';
import { getMenuList } from '@/script/constant/menu.js';
export default {
  props: {},
  components: {
  },
  data() {
    return {
      userId: 12333,
      logo: logo,
      userIcon,
      activeIndex: 'home',
      menuList: getMenuList(),
    };
  },
  computed: {
    
  },
  watch: {
    $route(value) {
      this.setActive(value);
    },
  },
  created() {
    // this.getRoleList();
  },
  mounted() {
    this.setActive();
  },
  methods: {
    selectMenu(value) {
      this.$emit('jumpRouter', value);
    },
    getRoleList() {
      this.$request.roleManage.getRoleList().then((res) => {
        const role = res.roleList[0];
        this.menuList = this.formatMenu(getMenuList(role));
      });
    },
    formatMenu(menuList) {
      const res = [];
      for (const item of menuList) {
        if(!item.isHide) {
          res.push(item);
          if (item.children && item.children.length) {
            item.children = this.formatMenu(item.children);
          }
        }
      }
      return res;
    },
    layout() {},
    setActive(value) {
      if(value && value.name){
        this.activeIndex = value.name || '';
      }
    },
  },
};
</script>
<style lang="less" scoped>
.user-popover {
  .infor-box {
    width: 11.11rem;
    height: 5.56rem;
    background: #fff;
    border: .06rem solid #efefef;
    color: #333;
    text-align: center;
    padding: .56rem;

    .user-text {
      height: 2.22rem;
      line-height: 2.22rem;

      &:hover {
        background: #efefef;
      }
    }

    .layout-click {
      width: 100%;
      color: #ff3824;
      height: 2.22rem;
      line-height: 2.22rem;

      &:hover {
        background: #efefef;
      }
    }
  }
}

.flex-row {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
}

.cursor {
  cursor: pointer;
}
.router-out{
  width:50%;
}
</style>
<style lang="less" scoped>
:deep {
  .el-menu-item,
  .el-submenu__title {
    font-size: .89rem;
  }

  .el-menu--horizontal > .el-menu-item,
  .el-menu--horizontal > .el-submenu .el-submenu__title {
    height: 2.67rem;
    line-height: 2.67rem;
    background: transparent !important;

    &:hover {
      background: rgb(31, 94, 195) !important;
    }
  }
  .el-menu-item.disabled {
    color: #c0c4cc !important;
    pointer-events: none;
  }
  .el-submenu__title i {
    color: #fff;
    font-size: .89rem;
  }
}

.main-router {
  height: 2.67rem;
  line-height: 2.67rem;
  color: #fff;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  font-size: .89rem;
  position: relative;
  background: #0091ff;

  .logo-box {
    margin: 0 2.22rem 0 1.33rem;

    .logo-text {
      margin-left: .56rem;
      font-style: oblique;
      font-weight: 600;
      font-size: 1.22rem;
    }
  }

  .router-out {
    /deep/ .el-menu {
      border-bottom: none;
      background: transparent !important;
    }

    /deep/ .el-menu--horizontal > .el-menu-item {
      height: 2.5rem;
      line-height: 2.67rem;
    }

    /deep/ .el-menu--horizontal > .el-submenu .el-submenu__title {
      height: 2.5rem;
      line-height: 2.67rem;
    }
  }

  .icon-list {
    justify-content: space-between;
    width: 4.44rem;
    padding: 0 0 0 .56rem;

    i {
      font-size: 1.11rem;
    }
  }

  .user-infor {
    position: relative;

    .name {
      width: 2.22rem;
      height: 2.22rem;
      line-height: 2.22rem;
      text-align: center;
      background: #a9cbff;
      border-radius: 1.33rem;
      font-size: .78rem;
      margin-right: .67rem;
    }

    .whole-name {
      margin-right: .44rem;
      min-width: 3.33rem;
      max-width: 4.44rem;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      font-size: .78rem;

      &:hover {
        cursor: pointer;
      }
    }

    .select-down {
      font-size: .89rem;
      margin-right: 1.33rem;
    }
  }
}
</style>

<style lang="less">
.custom-group-submenu {
  .el-menu {
    display: flex;
    padding-left: .44rem;
    .el-menu-item-group {
      margin-right: 1.67rem;
      .el-menu-item-group__title {
        padding-left: 0 !important;
        margin: 0 .56rem;
        font-size: .89rem;
        font-weight: bold;
        color: #333;
        border-bottom: .06rem solid #ccc;
      }
      .el-menu-item {
        font-size: .78rem;
        color: #000 !important;
        &:hover {
          color: #0192ff !important;
          background-color: transparent !important;
        }
      }
    }
  }
}
.custom-submenu {
  .el-menu {
    padding-left: .67rem;
    .el-menu-item {
      color: #000 !important;
      &:hover {
        color: #0192ff !important;
        background-color: transparent !important;
      }
    }
  }
}
</style>
