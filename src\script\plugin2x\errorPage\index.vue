<template>
  <div class="error-page">
		<div class="error-box">
			<div class="position-img">
				<img :src="errorImg" alt="" />
			</div>
		</div>
  </div>
</template>
<script>
import errorImg from '../../../img/404.png';
export default {
	name:'errorPage',
	components: {},
	data() {
		return {
			errorImg: errorImg,
		};
	},
	mounted() {
		
	},
	methods: {
		
	},
	beforeDestroy() {
	},
};
</script>
<style lang="less" scoped>
.error-page {
	width: 100%;
	height: 100%;
	padding: 16px;
	.error-box {
		background:#040A1E;
		margin: 16px;
		width: calc(100% - 32px);
		height: calc(100% - 32px);
		position: relative;
		.position-img {
			width: auto;
			position: absolute;
			left: 50%;
			top: 50%;
			transform: translate(-50%, -50%);
			display: flex;
			flex-direction: row;
			justify-content: flex-start;
			align-items: center;
		}
	}
}
</style>
