
<template>
    <div ref="echartInstance" class="map-echarts"></div>
</template>
<script>
import * as echarts from 'echarts';
import { registerMap } from 'echarts';
import nationalMap from '../../constant/nationalMap.json';

const CITY_LEVEL = {
  country: 'country', // 全国
  province: 'province',
  city: 'city', // 市
  district: 'district' // 县一级
};

const initEcharts = (myChart,options) =>{
    const option = {
		tooltip: {
			trigger: 'item'
		},
		  geo: {
		  	map: 'china',
			roam: true, //是否开启平游或缩放
			zoom:1.2,
			scaleLimit: {
				//滚轮缩放的极限控制
				min: 1,
				max: 2,
			},
			layoutCenter: ['50%', '50%'],
			itemStyle: {
				normal: {
					areaColor: 'rgba(237, 244, 255, 1)', // 地区颜色
					borderColor: 'rgba(54, 135, 224, 1)', // 轮廓线颜色
					borderWidth:1,
				},
				emphasis: {
				}
			}
		  },
		  series: [
			{
				name: '震级',
				type: 'effectScatter',
				coordinateSystem: 'geo',
				data:options,
				symbolSize: function (val) {
					return val[2] * 3;
				},
				encode: {
					value: 2
				},
				showEffectOn: 'render',
				rippleEffect: {
					brushType: 'stroke'
				},
				label: {
					formatter: '{b}',
					position: 'right',
					show: false
				},
				itemStyle: {
					shadowBlur: 10,
					shadowColor: '#333',
					color:'#F62157'
				},
				emphasis: {
					scale: true
				},
				zlevel: 1
			}
		]
	};
    myChart.clear();
    myChart.setOption(option,true);
};

export default {
  name: 'chinaMap',
  props:{
	 	data: {
            type: Array,
            default: () => [
				{
					name:'汶川',
					value:[102.51, 30.45,4.2]
				},
				{
					name:'唐山',
					value:[118.02, 39.63,5.3]
				},
				{
					name:'绵阳',
					value:[104.73, 31.48,3.2]
				},
			],
        },
  },
  data() {
	return {
		curLevel: {
			level: CITY_LEVEL.country, // 默认国家
			adcode: '100000'
		},
		levelStack: [], // 下钻的栈
	};
  },
  	watch: {
		data: {
			handler(newV) {
				this.init();
			},
			deep: true,
		},
	},
	mounted(){
		registerMap('china', nationalMap);
		this.init();
	},
  	methods: {
		init(){
			this.$nextTick(() => {
				if(!this.myChart){
					this.myChart = echarts.init(this.$refs.echartInstance);
				}
				initEcharts(
					this.myChart,
					this.data,
				);
			});
		},
		resize() {
			if (this.myChart) {
				this.myChart.resize();
			}
		},	
	},
};
</script>

<style lang="less" scoped>
.map-echarts{
	width: 100%;
	height: 100%;
	flex: 1;
}
</style>
