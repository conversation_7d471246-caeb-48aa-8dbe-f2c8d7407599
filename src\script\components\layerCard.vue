<template>
    <div class="layer-card">
        <div class="img-wrap">
            <img
                v-if="isPublic"
                class="collect"
                :src="getIcon(resource.isCollect)"
                @click="toggleStatus(resource)"
            />
            <div class="tool">
                <el-button type="primary" size="small" @click="jumpPage">进入</el-button>
                <slot name="btn"></slot>
            </div>
            <!-- 背景图 -->
            <img class="w-full" :src="resource.img" alt="" />
            <!-- 背景阴影 -->
            <div class="bg-shadow"></div>
            <!-- 标题 -->
            <span class="title">{{ resource.layerName }}</span>
            <!-- 数量 -->
            <div class="count">
                <img class="layerIcon" :src="layerIcon" alt="" />
                {{ resource.count || 0 }}
            </div>
        </div>
        <div class="updateTime">
            <span>更新时间：{{ resource.updateTime }}</span>
        </div>
    </div>
</template>
<script>
import layerIcon from '@/img/space/icons/layer.png';
import collectIcon from '@/img/space/icons/collect.png';
import collectedIcon from '@/img/space/icons/collected.png';
export default {
    name: 'layer-card',
    props: {
        resource: {
            type: Object,
            default: () => ({}),
        },
        isPublic: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            layerIcon,
        };
    },
    created() {},
    methods: {
        getIcon(isCollect) {
            return isCollect ? collectedIcon : collectIcon;
        },
        toggleStatus(resource) {
            this.$emit('collect', resource);
        },
        jumpPage() {
            this.$emit('jump', this.resource);
        },
    },
};
</script>
<style lang="less" scoped>
.layer-card {
    display: flex;
    flex-direction: column;
    padding: 15px;
    &:hover {
        transition: all 0.8s ease;
        background: rgba(31, 52, 77, 0.6);
        box-shadow: 0px 8px 24px 0px rgba(0, 0, 0, 0.16);
        border-radius: 12px;
        backdrop-filter: blur(20px);
        cursor: pointer;
    }
    .img-wrap {
        flex: 1;
        position: relative;
        .collect {
            display: none;
            position: absolute;
            top: 14px;
            left: 14px;
            z-index: 5;
        }
        .tool {
            display: none;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 4;
            .el-button {
                background-color: #3871b3;
                border: none;
            }
        }
        .bg-shadow {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(31, 52, 77, 0.5);
            border-radius: 12px;
            backdrop-filter: blur(20px);
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.5s ease-in-out;
            z-index: 3;
        }
        .title {
            position: absolute;
            top: 50%;
            left: 28px;
            font-size: 28px;
            transform: translateY(-50%);
        }
        .count {
            display: flex;
            align-items: center;
            position: absolute;
            top: 14px;
            right: 14px;
            padding: 6px 15px;
            height: 26px;
            border-radius: 13px;
            background-color: #1f3459;
            color: #fff;
            z-index: 5;
            .layerIcon {
                margin-right: 4px;
            }
        }
        &:hover .collect,
        &:hover .tool {
            display: block;
            cursor: pointer;
        }
        &:hover .tool {
            display: flex;
        }
        &:hover .bg-shadow {
            opacity: 1;
            visibility: visible;
            cursor: pointer;
        }
    }
    .updateTime {
        margin-top: 4px;
        font-weight: 400;
        font-size: 15px;
        color: #9aafc1;
        font-family: Source Han Sans CN, Source Han Sans CN;
    }
}
.w-full {
    width: 100%;
}
.bold {
    font-weight: bold;
}
</style>
