const executeStatusList = {
    0: '待执行',
    1: '执行中',
    2: '已完成',
    3: '失败',
    4: '持续分析中',
};
const formCols = () => {
    return [
        {
            element: 'el-input',
            prop: 'taskId',
            label: '任务ID',
            attrs: {
                clearable: true,
                placeholder: '请输入任务ID',
            },
            span: 3,
        },
        {
            element: 'el-input',
            prop: 'taskName',
            label: '任务名称',
            attrs: {
                clearable: true,
                placeholder: '请输入任务名称',
            },
            span: 3,
        },
        {
            element: 'el-input',
            prop: 'earthquakeName',
            label: '参考位置',
            attrs: {
                clearable: true,
                placeholder: '请输入参考位置',
            },
            span: 3,
        },
        {
            prop: 'taskStatus',
            label: '执行状态',
            attrs: {
                clearable: true,
                placeholder: '请选择执行状态',
                multiple: true,
                'collapse-tags': true,
            },
            element: 'el-select',
            slot: {
                element: 'el-option',
                enums: [
                    { label: '待执行', value: 0 },
                    { label: '执行中', value: 1 },
                    { label: '已完成', value: 2 },
                    { label: '失败', value: 3 },
                    { label: '持续分析中', value: 4 },
                ],
            },
            span: 3,
        },
        {
            prop: 'taskCreateTime',
            label: '创建时间',
            element: 'el-date-picker',
            attrs: {
                clearable: true,
                type: 'date',
                placeholder: '请选择创建时间',
                format: 'yyyy-MM-dd',
                'popper-class': 'earth-picker',
                'value-format': 'yyyy-MM-dd 00:00:00',
            },
            span: 5,
        },
        { span: 4 },
    ];
};
const tableColumns = [
    {
        prop: 'taskId',
        label: '任务ID',
        width: '80',
    },
    {
        prop: 'taskName',
        label: '任务名称',
        width: 290,
        showOverflowTooltip: false,
    },
    {
        prop: 'earthquakeName',
        label: '参考位置',
        showOverflowTooltip: false,
    },
    {
        prop: 'taskCreateTime',
        label: '创建时间',
    },
    {
        prop: 'createUserName',
        label: '创建人',
    },
    {
        prop: 'taskStatus',
        label: '执行状态',
    },
    {
        prop: 'executeStartTime',
        label: '执行开始时间',
    },
    {
        prop: 'executeEndTime',
        label: '执行结束时间',
    },
    {
        prop: 'taskNowSustainTime',
        label: '最新持续分析时间',
    },
    {
        prop: 'operation',
        label: '操作',
        width: 210,
    },
];
const taskNameDetail = [
    {
        name: '时间粒度：',
        props: 'timeType',
        formatter: (val) => {
            return (
                {
                    1: '天',
                    2: '小时',
                    3: '30分钟',
                    4: '15分钟',
                }[val] || '-'
            );
        },
    },
    {
        name: '时间选择：',
        props: 'timeSelect',
        formatter: (val, data) => {
            const { analysisStartTime, analysisEndTime } = data;
            return [analysisStartTime, analysisEndTime].filter(Boolean).join('~') || '-';
        },
    },
    {
        name: '分析半径：',
        props: 'analysisRadius',
        formatter: (val) => {
            return val ? `${val}m` : '-';
        },
    },
    {
        name: '对象选择：',
        props: 'populationIds',
        formatter: (val) => {
            const populationTypes = (val || '').split(',');
            const populationMap = {
                1: '受影响人群',
                2: '疑似失联人群',
                3: '掩埋人群',
            };
            return (
                populationTypes
                    .map((type) => populationMap[type])
                    .filter(Boolean)
                    .join('、') || '-'
            );
        },
    },
];
const detailData = [
    {
        name: '经度（°）：',
        props: 'centerLon',
    },
    {
        name: '纬度（°）：',
        props: 'centerLat',
    },
    {
        name: '震级（M）：',
        props: 'earthquakeLevel',
    },
    {
        name: '深度（千米）：',
        props: 'earthquakeDepth',
    },
    {
        name: '发震时刻（UTC+8）：',
        props: 'occurTime',
    },
];

export { formCols, tableColumns, executeStatusList, detailData, taskNameDetail };
