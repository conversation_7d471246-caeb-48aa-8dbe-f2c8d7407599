import Vue from 'vue';
// 在vue上挂载一个指量
const tooltip = Vue.directive('tooltip', {
  bind: function(el, binding) {
        // 鼠标移入时，将浮沉元素插入到body中
        el.onmouseenter = function(e) {
          // 创建浮层元素并设置样式
          // if(!el.getAttribute('tips')){
          //   return;
          // }
          // el.innerHTML = binding.value || '';
          Vue.nextTick(() => {
              if(el.scrollWidth > el.clientWidth || el.scrollHeight > el.clientHeight){
                const vcTooltipDom = document.createElement('div');
                      vcTooltipDom.style.cssText = `
                      overflow: auto;
                      position:absolute;
                      background: #101D33;
                      color:#fff;
                      box-shadow: rgba(168, 168, 168, 0.295) 1px 2px 10px;
                      border-radius:5px;
                      padding:10px;
                      display:inline-block;
                      font-size:15px;
                      z-index:3020;
                      max-width:400px;
                    `;
                      // 设置id方便寻找
                      vcTooltipDom.setAttribute('id', 'vc-tooltip');
                      // 将浮层插入到body中
                      document.body.appendChild(vcTooltipDom);
                      // 浮层中的文字 通过属性值传递动态的显示文案
                      document.getElementById('vc-tooltip').innerHTML = el.getAttribute('tips');
                    }
          })
        };
        // 鼠标移动时，动态修改浮沉的位置属性
        el.onmousemove = function(e) {
          const vcTooltipDom = document.getElementById('vc-tooltip');
          if(!vcTooltipDom){
            return;
          }
          const {clientWidth,clientHeight} = document.documentElement;
          const heightDiff = e.clientY + vcTooltipDom.offsetHeight + 40 - clientHeight;
          if(heightDiff >= 0){
            vcTooltipDom.style.top = e.clientY - heightDiff - 20 + 'px';
          }else{
            vcTooltipDom.style.top = e.clientY + 15 + 'px';
          }
          const widthDiff = e.clientX + vcTooltipDom.offsetWidth + 60 - clientWidth;
          if(widthDiff >= 0){
            vcTooltipDom.style.left = e.clientX - widthDiff - 20 + 'px';
          }else{
            vcTooltipDom.style.left = e.clientX + 15 + 'px';
          }
          
        };
        // 鼠标移出时将浮层元素销毁
        el.onmouseleave = function() {
          // 找到浮层元素并移出
          const vcTooltipDom = document.getElementById('vc-tooltip');
          vcTooltipDom && document.body.removeChild(vcTooltipDom);
        };
      }
});
export { tooltip };
