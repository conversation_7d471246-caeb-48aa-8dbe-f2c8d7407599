<template>
  <div class="input-info">
    <!-- 录入 -->
    <el-radio-group class="input-info__tab" v-model="curType" size="small">
      <el-radio-button label="singleEntry">单个录入</el-radio-button>
      <el-radio-button label="bulkEntry">批量录入</el-radio-button>
    </el-radio-group>
    <!-- 基础信息 -->
    <div class="input-info__main earth-overflow-y">
      <Keep-alive>
        <component
          :is="curType"
          :ref="`${curType}Ref`"
          v-bind="$attrs"
          v-on="$listeners"
          @setTab="(newTab) => (curTab = newTab)"
        />
      </Keep-alive>
    </div>
    <div class="input-info__footer">
      <el-button
        v-if="false&& isSingleEntry && isPolygonOrCircle"
        size="small"
        @click="overlapVerify"
        >重叠度校验</el-button
      >
      <el-button
        type="primary"
        size="small"
        class="create-btn"
        :disabled="isDisable"
        @click="createResource"
        >创建</el-button
      >
    </div>
  </div>
</template>

<script>
export default {
  name: 'input-info',
  components: {
    singleEntry: () => import('@/script/components/singleEntry.vue'),
    bulkEntry: () => import('./components/bulkEntry/index.vue'),
  },
  model: {
    prop: 'value',
    event: 'change',
  },
  props: {
    value: {
      type: String,
      default: 'singleEntry',
    },
  },
  data() {
    return {
      curTab: null,
    };
  },
  computed: {
    curType: {
      get() {
        return this.value;
      },
      set(newVal) {
        this.$emit('change', newVal);
      },
    },
    isDisable() {
      if (!this.curTab) return false;
      const { type, additionInfo } = this.curTab;
      return ['GeoJson', 'Shp'].includes(type) && !additionInfo.mapRelationship.length;
    },
    isSingleEntry() {
      return this.curType === 'singleEntry';
    },
    form() {
      return this.$attrs.form;
    },
    isPolygonOrCircle() {
      return [1, 2].includes(this.form.shapeType);
    },
  },
  methods: {
    getCurTab(curTab) {
      this.curTab = curTab;
    },
    overlapVerify() {
      this.$emit('overlapVerify');
    },
    async createResource() {
      if (this.curType === 'singleEntry') {
        const validRes = await this.$refs.singleEntryRef.validate();
        if (!validRes) {
          this.$message.warning('存在必填项未填写');
          return;
        }
        this.$emit('create', 'singleEntry');
      } else {
        const fileTab = this.$refs.bulkEntryRef.curTab;
        const file = fileTab.uploadInfo.file;
        if (!file) {
          this.$message.warning('请先上传文件');
          return;
        }
        this.$emit('create', 'bulkEntry', fileTab.create);
      }
    },
  },
};
</script>

<style lang="less" scoped>
.input-info {
  display: flex;
  flex-direction: column;
  padding: 0 12px;
  padding-bottom: 16px;
  height: 100%;

  &__tab {
    display: flex;
    .el-radio-button {
      flex: 1;
      margin: 0;
      &.is-active {
        font-display: bold;
        background: url(../../../../../img/space/resCreate/activeBg.png);
        background-size: 100% 100%;  // 新增这行，使背景图片完全填充容器
      }
      /deep/ &__inner {
        width: 100%;
        font-size: 14px;
        background-color: transparent;
        border-color: unset;
        box-shadow: unset;
        border: none;
        background: url(../../../../../img/space/resCreate/deactivatedBg.png);
        background-size: 100% 100%;  // 新增这行，使背景图片完全填充容器
      }
    }
  }

  &__main {
    flex: 1;
    height: 0;
    // background-color: #fff;
    .wrap {
      margin-top: 16px;

      .content {
        padding: 0 4px;

        .el-form-item {
          display: flex;
          flex-direction: column;
          margin-bottom: 8px;

          /deep/ .el-form-item__label {
            margin-bottom: 0;
            padding-right: 0px;
            width: 100% !important;
            text-align: left;
          }
        }
      }
    }

    .title {
      position: relative;
      padding-left: 6px;
      line-height: 24px;
      font-weight: bold;
      font-size: 14px;
      font-weight: bold;
      color: #333333;

      &::before {
        content: '';
        position: absolute;
        width: 4px;
        top: 5px;
        left: -3px;
        height: 14px;
        background: #0091ff;
        border-radius: 1px;
      }
    }
  }
  &__footer {
    margin-top: 16px;
    padding-right: 4px;
    display: flex;
    justify-content: center;
    .create-btn {
      width: 70%;
      background: #3871B3;
      border-radius: 4px;
    }
  }
}

.w-full {
  width: 100%;
}
.w-25 {
  width: 25%;
}
</style>
