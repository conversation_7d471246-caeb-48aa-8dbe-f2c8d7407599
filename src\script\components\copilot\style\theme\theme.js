import { themes } from './model.js';

// 修改页面中的样式变量值
const changeStyle = (obj) => {
    const fontSizeSetting = JSON.parse(localStorage.getItem('FontSize'));
    for (let key in obj) {
        if (key.includes('FontSize')) {
            document
                .getElementsByTagName('body')[0]
                .style.setProperty(`--${key}`, fontSizeSetting[key]);
        } else {
            document.getElementsByTagName('body')[0].style.setProperty(`--${key}`, obj[key]);
        }
    }
};

export const setTheme = (themeName) => {
    localStorage.setItem('theme', themeName); //保存到本地，下次进入使用该主题
    const themeConfig = themes[themeName] ? themes[themeName] : themes['default'];
    const fontSizeSetting = localStorage.getItem('FontSize');
    if (!fontSizeSetting) {
        localStorage.setItem(
            'FontSize',
            JSON.stringify({
                baseFontSize: themeConfig.baseFontSize,
                subFontSize: themeConfig.subFontSize,
                footFontSize: themeConfig.footFontSize
            })
        );
    }
    changeStyle(themeConfig);
};
