import layerImgs from '@/script/constant/layerResImgs.js';
import { shapeTypeOpts } from './common';
//  资源清单-排序方式
const sortOpts = [
    { label: '资源数', value: 1 },
    { label: '图层名称', value: 2 },
    { label: '更新时间', value: 3 },
];

// 资源清单-tab列表
const tabList = [
    {
        name: 'public',
        label: '公共',
        resources: [],
    },
    {
        name: 'private',
        label: '专有',
        resources: [],
    },
];
// 
const mapLayerNames = {
    1: 'park',
    2: 'businessDistrict',
    99: 'custom',
    3: 'school',
    4: 'hospital',
    5: 'venue',
    6: 'transportationCommunity',
    7: 'grid',
    8: 'industrialPark',
    9: 'residentialBuilding',
    10: 'officeBuilding',
    11: 'transportationHub',
    12: 'airport',
    13: 'railwayStation',
    14: 'intercityBusHub',
    15: 'street',
    16: 'industrialPark',
    17: 'customRegion',
    18: 'building',
    19: 'prohibitedArea',
    20: 'administrative',
    21: 'highSpeed',
    22: 'subway',
    23: 'subwayLine',
    专有资源: 'ownedResources',
    资源包: 'resourcePack',
};

const commonFormCols = (mapOpts = {}) => {
    return [
        {
            prop: 'shapeType',
            label: '轮廓类型',
            element: 'el-select',
            slot: {
                element: 'el-option',
                enums: [
                    { label: '全部', value: null },
                    ...shapeTypeOpts,
                ],
            },
            span: 4,
        },
        {
            prop: 'citys',
            label: '地市',
            element: () => import('@/script/components/selectDistrict.vue'),
            attrs: {
                options: mapOpts.citys,
                props: {
                    multiple: true,
                    label: 'label',
                    value: 'value',
                    children: 'children',
                    emitPath: false,
                }
            },
            span: 4,
        },
        {
            prop: 'resourceId',
            label: '资源ID',
            element: 'el-input',
            attrs: {
                placeHolder: '请输入',
            },
            span: 4,
        },
        {
            prop: 'resourceName',
            label: '资源名称',
            element: 'el-input',
            attrs: {
                placeHolder: '请输入',
            },
            span: 4,
        },
    ]
};


const privateResFormCols = (isExpand, resProp, mapOpts = {}) => {
    const isZoneGroup = resProp === 2;
    return [
        ...commonFormCols(mapOpts),
        {
            prop: 'regionTypeIds',
            label: '地域类型',
            element: () => import('@/script/components/selectDistrict.vue'),
            attrs: {
                isLastMultiSelect: false,
                disabled: isZoneGroup,
                options: mapOpts.regionTypeIds,
                props: {
                    multiple: true,
                    label: 'label',
                    value: 'value',
                    children: 'children',
                    emitPath: false,
                },
            },
            span: 4,
            isShow: isExpand,
        },
        {
            prop: 'layerIds',
            label: '图层类型',
            element: 'el-select',
            slot: {
                element: 'el-option',
                enums: mapOpts.layerIds,
            },
            attrs: {
                multiple: true,
                'collapse-tags': true,
            },
            span: 4,
        },
        {
            span: 4,
            isShow: !isExpand,
        },
        // 第二行
        {
            prop: 'type',
            label: '资源属性',
            element: 'el-select',
            slot: {
                element: 'el-option',
                enums: [
                    { label: '单个资源', value: 1 },
                    { label: '区域组', value: 2 },
                ]
            },
            span: 4,
            isShow: isExpand,
        },
        {
            prop: 'createTime',
            label: '创建日期',
            element: 'el-date-picker',
            attrs: {
                type: 'daterange',
                'range-separator': '-',
                'start-placeholder': '开始日期',
                'end-placeholder': '结束日期',
                'value-format': 'yyyy-MM-dd',
                style: 'width: 100%',
                'unlink-panels': true,
            },
            span: 8,
            isShow: isExpand,
        },
        {
            prop: 'describe',
            label: '资源描述',
            element: 'el-input',
            attrs: {
                placeHolder: '请输入关键词'
            },
            span: 4,
            isShow: isExpand,
        },
        {
            prop: 'isImport',
            label: '区域状态',
            element: 'el-select',
            attrs: {
                placeHolder: '请输入关键词'
            },
            slot: {
                element: 'el-option',
                enums: [
                    { label: '重点区域', value: 1 },
                    { label: '普通区域', value: 0 },
                ]
            },
            span: 4,
            isShow: isExpand,
        },
        {
            span: 4,
            isShow: isExpand,
        }
    ];
};
const publicResFormCols = (isExpand, mapOpts = {}) => {
    return [
        ...commonFormCols(mapOpts),
        {
            prop: 'regionTypeIds',
            label: '地域类型',
            element: () => import('@/script/components/selectDistrict.vue'),
            attrs: {
                isLastMultiSelect: false,
                options: mapOpts.regionTypeIds,
                props: {
                    multiple: true,
                    label: 'label',
                    value: 'value',
                    children: 'children',
                    emitPath: false,
                }
            },
            span: 4,
        },
        {
            prop: 'describe',
            label: '资源描述',
            element: 'el-input',
            attrs: {
                placeHolder: '请输入关键词'
            },
            span: 4,
            isShow: isExpand,
        },
        {
            span: 4,
            isShow: !isExpand,
        },
        // 第二行
        {
            prop: 'createTime',
            label: '创建日期',
            element: 'el-date-picker',
            attrs: {
                type: 'daterange',
                'range-separator': '-',
                'start-placeholder': '开始日期',
                'end-placeholder': '结束日期',
                'value-format': 'yyyy-MM-dd',
                style: 'width: 100%',
                'unlink-panels': true,
            },
            span: 8,
            isShow: isExpand,
        },
        {
            prop: 'isImport',
            label: '区域状态',
            element: 'el-select',
            attrs: {
                placeHolder: '请输入关键词'
            },
            slot: {
                element: 'el-option',
                enums: [
                    { label: '重点区域', value: 1 },
                    { label: '普通区域', value: 2 },
                ]
            },
            span: 4,
            isShow: isExpand,
        },
        {
            span: 6,
            isShow: isExpand,
        }
    ]
};
const resPackageFormCols = (isExpand) => {
    return [
        {
            prop: 'resourcePackageId',
            label: '资源包ID',
            element: 'el-input',
            listeners: {
                blur(e) {
                    // 提取输入值中的数字
                    const value = e.target.value;
                    const numericValue = value.replace(/\D/g, '');
                    e.target.value = numericValue;
                    // 触发 input 事件以更新 v-model 绑定的值
                    e.target.dispatchEvent(new Event('input'));
                }
            },
            span: 4,
        },
        {
            prop: 'resourcePackageName',
            label: '资源包名称',
            labelWidth: '90px',
            element: 'el-input',
            span: 5,
        },
        {
            prop: 'dataSource',
            label: '资源包来源',
            labelWidth: '90px',
            element: 'el-select',
            slot: {
                element: 'el-option',
                enums: [
                    { label: '全部来源', value: 1 },
                    { label: '租户创建', value: 2 },
                    { label: '订阅', value: 3 },
                ],
            },
            span: 4,
        },
        {
            prop: 'createTime',
            label: '创建日期',
            element: 'el-date-picker',
            attrs: {
                type: 'daterange',
                'range-separator': '-',
                'start-placeholder': '开始日期',
                'end-placeholder': '结束日期',
                'value-format': 'yyyy-MM-dd',
                style: 'width: 100%',
                'unlink-panels': true,
            },
            span: 7,
        },
        {
            span: 4,
            isShow: !isExpand,
        },
        // 第二行
        {
            prop: 'describe',
            label: '资源包描述',
            labelWidth: '92px',
            element: 'el-input',
            attrs: {
                placeHolder: '请输入关键词'
            },
            span: 4,
            isShow: isExpand,
        },
        {
            span: 6,
            isShow: isExpand,
        },
    ]
};

const layerResFormOpts = {
    citys: [],
    layerIds: [],
    shapeType: shapeTypeOpts,
    regionTypeIds: [],
    type: [
        { label: '单个资源', value: 1 },
        { label: '区域组', value: 2 },
    ],
    dataSource: [
        { label: '全部来源', value: 1 },
        { label: '租户创建', value: 2 },
        { label: '订阅', value: 3 },
    ]
};
// type: 资源属性
const detailFields = (shapeType, type, isPublic) => {
    const isPolygon = shapeType === 2;
    const isCircle = shapeType === 1;
    const isLine = shapeType === 4;
    const isLocatePoint = shapeType === 5;
    const isSingleResource = type === 1;
    const isZoneGroup = type === 2;
    const isResPackage = type === 'resPackage';
    return [
        {
            title: '基础信息',
            name: 'baseInfo',
            isShow: true,
            fields: [
                {
                    label: '资源ID',
                    prop: 'regionId',
                    isShow: true,
                },
                {
                    label: '资源属性',
                    prop: 'type',
                    isShow: !isResPackage,
                },
                {
                    label: '区域个数',
                    prop: 'regionCount',
                    isShow: isZoneGroup,
                },
                {
                    label: '图层类型',
                    prop: 'layerIds',
                    isShow: true,
                },
                {
                    label: '周长',
                    prop: 'perimeter',
                    isShow: isCircle,
                },
                {
                    label: '面积',
                    prop: 'regionArea',
                    isShow: !isResPackage && !isLocatePoint && !isLine,
                },
                {
                    label: '轮廓类型',
                    prop: 'shapeType',
                    isShow: isPolygon && isSingleResource || isResPackage,
                },
                {
                    label: '地域类型',
                    prop: 'regionTypeIds',
                    isShow: isSingleResource && !isLine,
                },
                {
                    label: '地市',
                    prop: 'city',
                    isShow: isSingleResource,
                },
                {
                    label: '区域外扩算法',
                    prop: 'shapeMethod',
                    isShow: isSingleResource && !isLocatePoint,
                },
                {
                    label: '基站类型',
                    prop: 'isIndoor',
                    isShow: isSingleResource && !isLocatePoint,
                },
                {
                    label: '区域是否起效',
                    prop: 'isValid',
                    isShow: isSingleResource,
                },
                {
                    label: '区域基站是否匹配完成',
                    prop: 'status',
                    isShow: isSingleResource && !isLocatePoint,
                },
                {
                    label: '开放状态',
                    prop: 'layerStatus',
                    isShow: !isPublic && !isResPackage,
                },
                {
                    label: '订阅次数',
                    prop: 'subscribeCount',
                    isShow: false, // 暂无
                },
                {
                    label: '最后更新时间',
                    prop: 'lastUpdateTime',
                    isShow: !isResPackage, // 暂无
                },
                {
                    label: `${isResPackage ? '资源包' : '资源'}描述`,
                    prop: 'describe',
                    isShow: true,
                },
            ]
        },
        {
            title: '其他信息',
            name: 'otherInfo',
            isShow: isResPackage,
            fields: [
                {
                    label: 'resourcePackageId',
                    prop: 'creator',
                    isShow: false,
                },
                {
                    label: '资源包名称',
                    prop: 'resourcePackageName',
                    isShow: false,
                },
                {
                    label: '数据来源',
                    prop: 'resourceSource',
                    isShow: isResPackage, // 暂无
                },
                {
                    label: '订阅截止日期',
                    prop: 'creator',
                    isShow: false,
                },
            ]
        },
    ];
}

const operateFields = [
    {
        prop: 'createName',
        label: '创建人',
    },
    {
        prop: 'createTime',
        label: '创建时间',
    },
    {
        prop: 'examineStatus',
        label: '审核状态',
    },
    {
        prop: 'examineOpinion',
        label: '审核意见',
    },
    {
        prop: 'lastUpdateName',
        label: '最近修改人',
    },
    {
        prop: 'lastUpdateTime',
        label: '最近修改时间',
    },
]
// todo: 
const mapRequest = {
    1: {
        regionKey: 'regionIdList',
        regionVal: 'resourceId',
        method: 'delRegionList',
    },
    2: {
        regionKey: 'regionGroupIds',
        regionVal: 'resourceId',
        method: 'delRegionGroup',
    },
    'resPackage': {
        regionKey: 'resourceList',
        regionVal: 'resourcePackageId',
        method: 'delResPackage',
    }
};
const mapExamineStatus = {
    1: '未开始',
    2: '进行中',
    3: '已完成',
}
const baseStationTableCols = (renderHeader, isShowOperate = true) => {
    const columns = [
        {
            prop: 'LACCELL',
            label: 'LACCELL',
        },
        {
            prop: 'gen',
            label: '网络格式',
            width: 80,
        },
        // todo
        // {
        //     prop: 'latLng',
        //     label: '经纬度',
        //     width: 80,
        // },
        {
            prop: 'isArea',
            label: '是否区域内',
            width: 114,
            className: 'custom-cell',
            renderHeader: (h, scope) => renderHeader(
                h,
                {
                    ...scope,
                    menu: [
                        {
                            label: '是否区域内',
                            value: null,
                        },
                        {
                            label: '是',
                            value: '是',
                        },
                        {
                            label: '否',
                            value: '否',
                        }
                    ]
                }
            ),
        },
    ];
    if (isShowOperate) {
        columns.push({
            prop: 'operate',
            label: '操作',
            className: 'custom-cell',
            width: 60,
        });
    }
    return columns;
}
const excelWidths = new Array(17).fill({ wch: 40 });
const overlapsTableCols = [
    {
        prop: 'regionId',
        label: '资源ID',
        width: 150,
    },
    {
        prop: 'regionName',
        label: '资源名称',
        width: 150,
    },
    {
        prop: 'repetition',
        label: '重叠度',
        width: 70,
    },
];
const bulkOpts = [
    { label: '批量管理', value: '' },
    // { label: '批量查看', value: '批量查看' },
    { label: '批量编辑', value: '批量编辑' },
    { label: '批量导出', value: '批量导出' },
    { label: '批量删除', value: '批量删除' },
]

export {
    sortOpts,
    tabList,
    mapLayerNames,
    layerResFormOpts,
    detailFields,
    operateFields,
    layerImgs,
    baseStationTableCols,
    excelWidths,
    mapRequest,
    overlapsTableCols,
    mapExamineStatus,
    privateResFormCols,
    publicResFormCols,
    resPackageFormCols,
    bulkOpts,
};