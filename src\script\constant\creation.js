const timeRangeList = {
    1: {
        type: 'daterange',
        'start-placeholder': '开始日期',
        'end-placeholder': '结束日期',
        format: 'yyyy-MM-dd',
        'value-format': 'yyyy-MM-dd 00:00:00',
    },
    2: {
        type: 'datetimerange',
        'start-placeholder': '开始时间',
        'end-placeholder': '结束时间',
        format: 'yyyy-MM-dd HH',
        'value-format': 'yyyy-MM-dd HH:00:00',
    },
    3: {
        type: 'datetimerange',
        'start-placeholder': '开始时间',
        'end-placeholder': '结束时间',
        format: 'yyyy-MM-dd HH:mm',
        'value-format': 'yyyy-MM-dd HH:mm:00',
    },
    4: {
        type: 'datetimerange',
        'start-placeholder': '开始时间',
        'end-placeholder': '结束时间',
        format: 'yyyy-MM-dd HH:mm',
        'value-format': 'yyyy-MM-dd HH:mm:00',
    },
};
const tableColumn = [
    {
        prop: 'earthquakeName',
        label: '参考位置',
        width: 200,
    },
    {
        prop: 'centerLon',
        label: '经度（°）',
    },
    {
        prop: 'centerLat',
        label: '纬度（°）',
    },
    {
        prop: 'earthquakeLevel',
        label: '震级（M）',
    },
    {
        prop: 'earthquakeDepth',
        label: '深度（千米）',
    },
    {
        prop: 'occurTime',
        label: '发震时刻（UTC+8）',
        width: 180,
    },
];
const creationFormCols = (vm, earthquake, timeGranularity) => {
    const list = [
        {
            element: 'el-input',
            prop: 'taskName',
            label: '任务名称：',
            labelWidth: '93px',
            attrs: {
                clearable: true,
                placeholder: '请输入任务名称',
            },
            span: 24,
            rules: [{ required: true, message: '请输入任务名称！', trigger: 'blur' }],
        },
        {
            element: 'el-input',
            prop: 'earthquakeSelection',
            label: '地震选择：',
            labelWidth: '93px',
            attrs: {
                readonly: true,
                placeholder: '请选择',
            },
            slot: {
                element: 'span',
                slot: 'append',
                text: '选择',
                style: {
                    fontSize: '12px',
                    color: '#fff',
                    cursor: 'pointer',
                },
                listeners: {
                    click: () => { // 改为点击事件触发弹框
                        vm.showDailog = true;
                        vm.search();
                    }
                },
            },
            span: 24,
            rules: [{ required: true, message: '请进行地震选择！', trigger: ['blur', 'change'] }],
        },
        {
            prop: 'timeType',
            label: '时间粒度：',
            labelWidth: '93px',
            attrs: {
                clearable: true,
                placeholder: '请选择时间粒度',
            },
            element: 'el-select',
            slot: {
                element: 'el-option',
                enums: [
                    { label: '天', value: 1 },
                    { label: '小时', value: 2 },
                    { label: '30分钟', value: 3 },
                    { label: '15分钟', value: 4 },
                ],
            },
            listeners: {},
            span: 24,
            rules: [{ required: true, message: '请选择时间粒度！', trigger: ['blur', 'change'] }],
        },
        {
            prop: 'executionTime',
            label: '时间选择：',
            labelWidth: '93px',
            element: 'el-date-picker',
            attrs: Object.assign(
                {
                    clearable: true,
                    'range-separator': '-',
                    type: 'datetimerange',
                    'start-placeholder': '开始时间',
                    'end-placeholder': '结束时间',
                    'popper-class':"earth-picker",
                    format: 'yyyy-MM-dd HH:mm',
                    'picker-options': {
                        selectableRange: ['00:00', '23:45'],
                        step: '00:15',
                    },
                    'value-format': 'yyyy-MM-dd HH:mm:00',
                },
                timeGranularity ? timeRangeList[timeGranularity] || {} : {}
            ),
            listeners: { change: vm.handleDateChange },
            span: 24,
            rules: [{ required: true, message: '请选择时间！', trigger: ['blur', 'change'] }],
        },
        {
            element: 'el-input-number',
            prop: 'analysisRadius',
            label: '分析半径：',
            labelWidth: '93px',
            attrs: {
                clearable: true,
                placeholder: '请输入',
                'controls-position': 'right',
            },
            rightSlot: 'KM',
            span: 22,
            rules: [{ required: true, message: '请输入分析半径！', trigger: 'blur' }],
        },
        {
            prop: 'continuousAnalysis',
            label: '是否持续分析：',
            labelWidth: '123px',
            attrs: {},
            element: 'el-select',
            slot: {
                element: 'el-option',
                enums: [
                    { label: '是', value: 1 },
                    { label: '否', value: 0 },
                ],
            },
            span: 24,
            rules: [{ required: true, message: '请选择是否持续分析！', trigger: ['blur', 'change'] }],
        },
        {
            prop: 'populationTargetId',
            label: '对象选择：',
            labelWidth: '93px',
            attrs: {},
            element: 'el-checkbox-group',
            slot: {
                element: 'el-checkbox',
                enums: [
                    { text: '受影响人群', label: '1' },
                    { text: '疑似失联人群', label: '2' },
                    { text: '掩埋人群', label: '3'},
                ],
            },
            span: 24,
            rules: [{ required: true, message: '请选择对象！', trigger: ['blur', 'change'] }],
        },
        { span: 24 },
    ];
    if (earthquake) {
        // list.splice(2, 0, { span: 24, prop: 'showEarth',itemClassName:'not-border' });
        list.splice(2, 0, ...detailData);
    }
    return list;
};
const detailData = [
    {
        span: 12,
        element: 'el-input',
        prop: 'earthquakeLevel',
        label: '震级：',
        labelWidth: '93px',
        attrs: {
            readonly:true,
        },
    },
    {
        span: 12,
        element: 'el-input',
        prop: 'earthquakeDepth',
        label: '深度：',
        labelWidth: '93px',
        attrs: {
            readonly:true,
        },
    },
    {
        name: '发震时刻：',
        props: 'occurTime',
        span: 24,
        element: 'el-input',
        prop: 'occurTime',
        label: '发震时刻：',
        labelWidth: '93px',
        attrs: {
            readonly:true,
        },
    },
];

const initForm = {
    taskName: '',
    earthquakeSelection: '',
    timeType: '',
    executionTime: [],
    analysisRadius: 5000,
    continuousAnalysis:0,
    populationTargetId: ['1','2'],
    earthquakeLevel:'',
    earthquakeDepth:'',
    occurTime:''
};
const formCols = () => {
    const list = [
        {
            element: 'el-input',
            prop: 'earthquakeName',
            label: '参考位置',
            attrs: {
                clearable: true,
                placeholder: '请输入参考位置',
            },
            span: 9,
        },
        {
            prop: 'earthquakeDepthLevel',
            label: '震源深度',
            attrs: {
                clearable: true,
                placeholder: '请选择',
            },
            element: 'el-select',
            slot: {
                element: 'el-option',
                enums: [
                    { label: '0-10千米', value: 1 },
                    { label: '10-20千米', value: 2 },
                    { label: '20-30千米', value: 3 },
                    { label: '30千米以上', value: 4 },
                ],
            },
            span: 8,
        },
        {
            prop: 'earthquakeScaleLevel',
            label: '地震震级',
            attrs: {
                clearable: true,
                placeholder: '请选择',
            },
            element: 'el-select',
            slot: {
                element: 'el-option',
                enums: [
                    { label: '0-3级', value: 1 },
                    { label: '3-6级', value: 2 },
                    { label: '6级以上', value: 3 },
                ],
            },
            span: 7,
        },
        {
            prop: 'time',
            label: '发震时刻',
            element: 'el-date-picker',
            attrs: {
                clearable: true,
                type: 'daterange',
                'range-separator': '-',
                'start-placeholder': '开始日期',
                'popper-class':"earth-picker",
                'end-placeholder': '结束日期',
                format: 'yyyy-MM-dd',
                'value-format': 'yyyy-MM-dd 00:00:00',
            },
            span: 12,
        },
        { span: 12 },
    ];
    return list;
};
const addEarthquakeFields = (handleDepth, handleLevel) => [
    {
        element: 'el-input',
        prop: 'cataId',
        label: '地震事件id',
        labelWidth: '94px',
        attrs: {
            clearable: true,
            placeholder: '请输入',
        },
        span: 12,
    },
    {
        element: 'el-input',
        prop: 'location',
        label: '参考位置',
        attrs: {
            clearable: true,
            placeholder: '请输入',
        },
        span: 12,
    },
    {
        element: 'el-input',
        prop: 'epiLon',
        label: '经度',
        attrs: {
            clearable: true,
            placeholder: '请输入',
        },
        span: 12,
    },
    {
        element: 'el-input',
        prop: 'epiLat',
        label: '纬度',
        attrs: {
            clearable: true,
            placeholder: '请输入',
        },
        span: 12,
    },
    {
        element: 'el-input',
        prop: 'epiDepth',
        label: '震源深度',
        attrs: {
            clearable: true,
            placeholder: '请输入',
        },
        listeners: {
            input: handleDepth,
        },
        span: 12,
    },
    {
        element: 'el-input',
        prop: 'level',
        label: '地震震级',
        attrs: {
            clearable: true,
            placeholder: '请输入',
        },
        listeners: {
            input: handleLevel,
        },
        span: 12,
    },
    {
        prop: 'time',
        label: '发震时刻',
        element: 'el-date-picker',
        attrs: {
            clearable: true,
            type: 'datetime',
            placeholder: '请选择日期',
            'popper-class': 'earth-picker',
            'value-format': 'yyyy-MM-dd HH:mm:ss',
        },
        span: 12,
    },
]
export { creationFormCols, detailData, initForm,tableColumn ,formCols, addEarthquakeFields};
