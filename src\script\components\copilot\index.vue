<template>
    <div
        class="draggable"
        :style="{
            top: formatValue(top),
            left: formatValue(left),
            right: formatValue(right),
            bottom: formatValue(bottom)
        }"
    >
        <div
            class="draggable-sign"
            @mousedown="startDrag"
            @mouseup="stopDrag"
            @mousemove="dragging"
            @click="handleRobot"
        >
            <img src="./img/logo.gif" alt="" />
            <div class="draggable-sign-title">AI助手</div>
        </div>
        <dialogue
            ref="dialogue"
            :class="['dialogue', { isFullScreen: isFullScreen }]"
            v-bind="$attrs"
            v-show="isVisible"
            :visible="isVisible"
            :currentTheme="currentTheme"
            :isFullScreen="isFullScreen"
            :voiceOrder="voiceOrder"
            :style="[dialogueLoc]"
            @setCopilotVisible="setCopilotVisible"
        ></dialogue>
        <div v-show="voiceOrder" class="shadow"></div>
    </div>
</template>

<script>
import { setTheme } from './style/theme/theme.js';
import dialogue from './components/dialogue.vue';
import vioceIdentify from './mixins/vioceIdentify.js';
import { findComponentDownward } from './utils/assist.js';
const ROBOT_WIDTH = 41;
const ROBOT_HEIGHT = 122;
const DIALOGUE_WIDTH = 569;
export default {
    name: 'copilot',
    components: {
        dialogue
    },
    props: {
        location: {
            type: Object,
            default: () => ({
                top: 100,
                left: '77%',
                right: 'auto',
                bottom: 'auto'
            })
        }
    },
    data() {
        return {
            isDragging: false,
            isMove: false,
            top: 100,
            left: '77%',
            right: 'auto',
            bottom: 'auto',
            isVisible: false,
            dialogueLoc: {
                top: '100px',
                left: '-36.45rem',
                'min-width': '35.45rem',
                height: '795px'
            },
            dialogueLocHeight: '70%',
            isFullScreen: false, //是否全屏
            currentTheme: ''
        };
    },
    mixins: [vioceIdentify],
    provide() {
        return {
            copilotIndex: this
        };
    },
    watch: {
        location: {
            handler(newV) {
                Object.keys(newV).forEach((item) => {
                    this[item] = newV[item];
                });
                this.popUpPositionDeter();
            }
        },
        voiceOrder: {
            handler(newV) {
                if (newV) {
                    this.isVisible = true;
                }
            }
        }
    },
    mounted() {
        this.allWidth = document.body.offsetWidth;
        this.allHeight = document.body.offsetHeight || document.documentElement.clientHeight;
        const themeType = localStorage.getItem('theme');
        this.themeType(themeType || 'blue');
        this.popUpPositionDeter();
        // 录音控件有点问题，暂时只能先开启关闭再开启，后续再优化
        /* const time1 = setTimeout(() => {
            this.startRecord();
            clearTimeout(time1);
        }, 500);
        const time2 = setTimeout(() => {
            this.stopRecord();
            clearTimeout(time2);
        }, 600);
        const time3 = setTimeout(() => {
            this.startRecord();
            clearTimeout(time3);
        }, 700); */
    },
    methods: {
        // 设置弹窗加载或关闭
        setCopilotVisible(isVisible) {
            this.isVisible = isVisible;
        },
        themeType(type) {
            this.currentTheme = type;
            setTheme(type);
        },
        formatValue(value) {
            return Number.isFinite(value) ? value + 'px' : value;
        },
        // 开始拖拽事件
        startDrag(event) {
            event.preventDefault();
            this.isMove = false;
            this.isDragging = true;
            const el = document.querySelector('.draggable');
            this.offsetX = event.clientX - el.offsetLeft;
            this.offsetY = event.clientY - el.offsetTop;
            document.addEventListener('mousemove', this.dragging);
            document.addEventListener('mouseup', this.stopDrag);
        },
        // 结束拖拽事件
        stopDrag(event) {
            event.preventDefault();
            this.isDragging = false;
            this.isMove = false;
            this.popUpPositionDeter();
            document.removeEventListener('mousemove', this.dragging);
            document.removeEventListener('mouseup', this.stopDrag);
        },
        // 弹窗位置判断
        popUpPositionDeter() {
            const { left, top, right } = this;
            let dialogueLeft, dialogueTop;
            // 在机器人左侧
            if (left + ROBOT_WIDTH + DIALOGUE_WIDTH + 10 > this.allWidth || right !== 'auto') {
                dialogueLeft = -DIALOGUE_WIDTH - 10;
            } else {
                // 在机器人右侧
                dialogueLeft = ROBOT_WIDTH + 10;
            }
            const dialogueHeight =
                this.allHeight * Number(this.dialogueLocHeight.split('%')[0] / 100);
            if (top + dialogueHeight < this.allHeight) {
                // 与机器人齐平
                dialogueTop = 0;
            } else if (top > dialogueHeight) {
                // 在机器人上侧
                dialogueTop = -dialogueHeight + ROBOT_HEIGHT;
            } else {
                // 在顶部
                dialogueTop = -top;
            }
            Object.assign(this.dialogueLoc, {
                left: dialogueLeft + 'px',
                top: dialogueTop + 'px',
                height: dialogueHeight + 'px',
            });
        },
        dragging(event) {
            event.preventDefault();
            if (this.isDragging) {
                const el = this.$el;
                const width = el.offsetWidth;
                const height = el.offsetHeight;
                let left = event.clientX - this.offsetX;
                let top = event.clientY - this.offsetY;
                // 如果鼠标移动了超过一定距离，则认为是拖拽
                if (!this.isMove && (Math.abs(left) > 0 || Math.abs(top) > 0)) {
                    this.isMove = true;
                    this.isVisible = false;
                }
                // 边界检查
                left = Math.min(Math.max(0, left), this.allWidth - width);
                top = Math.max(0, Math.min(top, this.allHeight - height));
                this.left = left;
                this.top = top;
                this.right = 'auto';
                this.bottom = 'auto';
            }
        },
        handleRobot() {
            if (this.isMove) {
                return;
            }
            this.isVisible = !this.isVisible;
        },
        renderContent() {
            const com = findComponentDownward(this, 'conversations');
            console.warn('111', com);
        },
        // 窗口吸附
        windowAdsorption() {
            let { left, right } = this;
            let { height } = this.dialogueLoc;
            // 判断是否已吸附
            if (height === this.allHeight + 'px') {
                this.popUpPositionDeter();
            } else {
                let leftOffset;
                if (left > this.allWidth * 0.5 || right !== 'auto') {
                    leftOffset = this.allWidth - DIALOGUE_WIDTH - left;
                } else {
                    leftOffset = 0 - left;
                }
                Object.assign(this.dialogueLoc, {
                    top: -this.top + 'px',
                    left: leftOffset + 'px',
                    height: this.allHeight + 'px'
                });
            }
        },
        // 全屏
        windowFullScreen() {
            let { width } = this.dialogueLoc;
            if (width === this.allWidth + 'px') {
                this.isFullScreen = false;
                this.popUpPositionDeter();
            } else {
                this.isFullScreen = true;
                Object.assign(this.dialogueLoc, {
                    top: -this.top + 'px',
                    left: -this.left + 'px',
                    height: this.allHeight + 'px',
                    width: this.allWidth + 'px'
                });
            }
        }
    }
};
</script>

<style lang="less" scoped>
.draggable {
    position: fixed;
    z-index: 48;
    cursor: pointer;
    width: 41px;
    height: 122px;
    max-width: 100%;
    max-height: 100%;
    &-sign {
        position: absolute;
        left: 0;
        top: 0;
        width: 41px;
        height: 122px;
        border-radius: 20px;
        padding: 5px;
        background: var(--baseBgColor);
        border: 1px solid var(--baseBorderColor);
        box-shadow: var(--baseBorderShadow);
        cursor: pointer;
        box-sizing: border-box;
        img {
            width: 31px;
            height: 31px;
        }
        &-title {
            font-size: var(--baseFontSize);
            color: var(--baseTextColor);
            font-weight: bold;
            text-align: center;
        }
        &:hover {
            box-shadow: inset 0 0 5px var(--baseBorderColor);
        }
    }
    .dialogue {
        position: absolute;
        z-index: 49;
        border-radius: 11px;
        background: var(--baseBgColor);
        border: 1px solid var(--baseBorderColor);
        box-shadow: var(--baseBorderShadow);
        &.isFullScreen {
            border-radius: none;
            border: none;
        }
    }
}
.shadow {
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    // box-shadow: 0px 0px 16px 5px red inset;
    animation: border-animation 1.5s infinite;
    position: fixed;
    z-index: 10000;
    /* 关键裁剪路径 */
    clip-path: polygon(
        0 0,
        /* 左上角 */ 100% 0,
        /* 右上角 */ 100% 100%,
        /* 右下角 */ 0 100%,
        /* 左下角 */ 0 0,
        /* 闭合路径 */ /* 内部裁剪区域 */ 1rem 1rem,
        /* 内左上 */ 1rem calc(100% - 1rem),
        /* 内左下 */ calc(100% - 1rem) calc(100% - 1rem),
        /* 内右下 */ calc(100% - 1rem) 1rem,
        /* 内右上 */ 1rem 1rem /* 闭合内路径 */
    );
    @keyframes border-animation {
        0% {
            box-shadow: 0px 0px 0px 0px #63a8aa inset;
        }
        50% {
            box-shadow: 0px 0px 1rem 0.4rem #63a8aa inset;
        }
        100% {
            box-shadow: 0px 0px 0px 0px #63a8aa inset;
        }
    }
}
</style>
