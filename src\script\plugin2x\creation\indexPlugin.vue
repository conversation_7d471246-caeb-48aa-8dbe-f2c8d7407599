<template>
    <div class="creation">
        <headTitle class="creation__title" titleName="新增资源"> </headTitle>
        <!-- 主体 -->
        <div class="creation__main">
            <div class="operate">
                <inputInfo
                    v-model="curEntryType"
                    :form="form"
                    :shapeType="form.shapeType"
                    :handleExpand="handleExpand"
                    @create="createResource"
                    @overlapVerify="handleOverlapVerify"
                />
            </div>
            <div class="map">
                <!-- 地图 -->
                <gisMap
                    ref="gisCom"
                    :curType="curEntryType"
                    :city="form.city"
                    :layerList="layerList"
                    :isShowInnerCity="isBulkEntry"
                    :isJustShowLayer="isBulkEntry"
                    isShowTool
                    isShowHideManage
                    isShowBaseStationManage
                    :isCreated="false"
                    :defExpansion="3"
                    :shapeType="form.shapeType"
                    :roadLength.sync="form.roadLength"
                    :expand="form.expand"
                />
            </div>
        </div>
    </div>
</template>

<script>
import headTitle from '@/script/components/headTitle.vue';
import inputInfo from './components/inputInfo/index.vue';
import gisMap from '@/script/components/gis/gisComLast.vue';
import overlapsArea from '@/script/components/gis/layerResource/components/overlapsArea.vue';
import sharpInfo from '@/script/components/gis/layerResource/components/sharpInfo.vue';
import imgInfo from '@/script/constant/popoverImgs.js';
export default {
    name: 'creation',
    components: {
        headTitle,
        inputInfo,
        gisMap,
    },
    provide() {
        return {
            root: this,
        };
    },
    data() {
        return {
            curEntryType: 'singleEntry',
            form: {
                regionName: '',
                city: [],
                layerIDs: [],
                shapeType: 2,
                regionTypeIDs: [],
                isIndoor: 3, // 基站类型-全部
                shapeMethod: 1,
                describe: '',
                layerStatus: 1,
                // 线路特有参数
                roadLength: 0,
                roadType: 0,
                expand: 1,
            },
            regionId: '', // 当前图层id
            overlapAreas: [],
            curSingleParams: {},
        };
    },
    computed: {
        userInfo() {
            return {
                id: 1,
                name: 'admin',
                describe: '系统管理员',
            };
        },
        layerList() {
            return [
                {
                    label: '重叠区域',
                    prop: overlapsArea,
                    icons: [imgInfo.layer, imgInfo.layerActive],
                    attrs: {
                        isResCreate: true,
                    },
                    isShow: [1, 2].includes(this.form.shapeType),
                },
                {
                    label: '轮廓信息',
                    prop: sharpInfo,
                    icons: [imgInfo.sharp, imgInfo.sharpActive],
                    isShow: this.curEntryType === 'singleEntry',
                },
            ];
        },
        overlapsLayer() {
            return this.layerList.find((item) => item.label === '重叠区域');
        },
        isBulkEntry() {
            return this.curEntryType === 'bulkEntry';
        },
        isComplexShapeType() {
            return [1, 2].includes(this.form.shapeType);
        },
        isLine() {
            return this.form.shapeType === 4;
        },
    },
    created() {
       
    },
    methods: {
        async createResource(type, bulkCreate) {
            if (type === 'singleEntry') {
                const { id, name } = this.userInfo;
                const {
                    regionName,
                    city,
                    layerIDs,
                    shapeType,
                    layerStatus,
                    shapeMethod,
                    isIndoor,
                    regionTypeIDs,
                    describe,
                    roadLength,
                    roadType,
                    expand,
                } = this.form;
                const areas = this.formatCity(city);

                const regionParams = this.$refs.gisCom.getRegionParams();
                if (!regionParams) {
                    this.$message.error('请先绘制图形');
                    return;
                }

                const params = {
                    userId: id,
                    userName: name,
                    regionName,
                    shapeType,
                    layerStatus,
                    ...areas,
                    shapeMethod: this.isComplexShapeType ? shapeMethod : 0,
                    isIndoor,
                    attributes: {
                        layerIDs,
                        regionTypeIDs: regionTypeIDs,
                    },
                    coorsType: 'WGS84',
                    describe,
                    roadLength,
                    roadType,
                    expand,
                    ...regionParams,
                };

                const res = await this.$post('singleCreate', params);
                if (res.serviceFlag === 'FALSE') {
                    return this.$message.error(res.returnMsg);
                }
                if (this.isComplexShapeType || this.isLine) {
                    this.updateBaseStations(res.regionId, params);
                }
                this.$message({
                    message: '创建成功',
                    type: 'success',
                    duration: 1500,
                    onClose: () => {
                        this.jumpResLayer();
                    },
                });
            } else {
                bulkCreate();
            }
        },
        async updateBaseStations(regionId, params) {
            const addedBasePoints = this.$refs.gisCom.getAddedBasePoints();
            const circleChoices = this.$refs.gisCom.circleChoices;
            if (addedBasePoints.length) {
                const res = await this.$post('editLayer', {
                    regionId,
                    cgiCode: addedBasePoints,
                    auxiliaryCircleList: Object.keys(circleChoices).map((item) => {
                        return {
                            radius: circleChoices[item].radius,
                            centerLatitude: circleChoices[item].centerLatitude,
                            centerLongitude: circleChoices[item].centerLongitude,
                        };
                    }),
                    ...params,
                });
                console.log('区域基站列表编辑', res);
            }
        },
        formatCity(areas) {
            return {
                province: areas[0],
                city: areas[1],
            };
        },
        handleExpand() {
            if (this.$refs.gisCom) {
                this.$refs.gisCom.setExpand();
            }
        },
        async handleOverlapVerify() {
            const tip = this.validate(this.form);
            if (tip) {
                this.$message.warning(tip);
                return;
            }

            this.$eventBus.$emit('openLayer', this.overlapsLayer);
            this.$nextTick(() => {
                this.$eventBus.$emit('getOverlapAreas', this.form);
            });
        },
        validate(form) {
            const planes = this.$refs.gisCom.planeObjs;
            const { regionName, city, layerIDs, shapeType } = form;
            const planeLen = planes.filter(Boolean).length;
            if (!planeLen) {
                return '清先绘制图形！';
            } else if (!regionName) {
                return '请输入区域名称！';
            } else if (!city || !city.length) {
                return '请选择城市！';
            } else if (!layerIDs || !layerIDs.length) {
                return '请选择图层类型！';
            } else if (!shapeType) {
                return '请选择轮廓类型！';
            }
        },
        jumpResLayer() {
            this.$router.push({
                path: 'resManifest',
                query: {
                    layerName: '专有资源',
                },
            });
        },
        checkResource() {
            this.$router.push({
                name: 'manifest',
                query: {},
            });
        },
    },
};
</script>

<style lang="less" scoped>
.creation {
    display: flex;
    flex-direction: column;
    height: 100%;

    &__main {
        flex: 1;
        display: flex;
        padding: 16px;
        height: 0;
        z-index: 2;

        .operate {
            flex-basis: 300px;
            max-width: 300px;
            padding: 0 4px;
            background: rgba(23, 42, 65, 0.6);
            box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.65);
            border-radius: 12px;
            backdrop-filter: blur(8px);
        }

        .map {
            position: relative;
            padding: 0 12px 0 0;
            width: calc(100% - 300px);
            &::before {
                content: '';
                display: block;
                position: absolute;
                left: 0;
                top: 0;
                margin: 12px 0;
                width: 9px;
                height: calc(100% - 24px);
                background: linear-gradient(90deg, rgba(0, 0, 0, 0.16) 0%, rgba(0, 0, 0, 0) 100%);
                opacity: 0.74;
                z-index: 1;
            }
        }
    }
}
</style>
