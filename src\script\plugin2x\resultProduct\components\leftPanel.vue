<template>
    <card class="custom-card" :img="require(`../../../../img/common/gridTitleBg.png`)" title="核心信息">
        <div class="overview">
            <div v-for="(item, inx) in list" class="item" :key="inx">
                <img class="img" :src="item.icon" alt="" />
                <span class="content">
                    <span class="number">{{ overview[item.prop] || '-' }}</span>
                    <span class="name">{{ item.label }}</span>
                </span>
            </div>
        </div>
    </card>
</template>

<script>
import card from '_com/card/index.vue';
export default {
    name: 'leftPanel',
    components: {
        card,
    },
    props: {
        overview: {
            type: Object,
            default: () => ({}),
        },
    },
    data() {
        return {
            list: [
                {
                    icon: require('../../../../img/analyzeDetails/numberOfAffectedPopulation.png'),
                    label: '受影响人群',
                    prop: 'accPepCnt',
                },
                {
                    icon: require('../../../../img/analyzeDetails/numberOfBaseStations.png'),
                    label: '基站数量',
                    prop: 'laccellCnt',
                },
            ]
        };
    },
    computed: {},
    methods: {},
};
</script>

<style lang="less" scoped>
.custom-card {
    height: auto;
}
.overview {
    .item {
        display: flex;
        padding: 6px 0;
        .img {
            margin-right: 14px;
        }
        .content {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: end;
            .number {
                font-weight: bold;
                font-size: 22px;
                color: #ffffff;
                line-height: 27px;
                letter-spacing: 2px;
                text-shadow: 0px 0px 4px rgba(62, 136, 233, 0.64);
            }
            .name {
                margin-top: 4px;
                font-size: 14px;
                color: #ffffff;
                line-height: 18px;
                text-shadow: 0px 0px 4px rgba(62, 136, 233, 0.64);
                text-align: left;
            }
        }
    }
}
</style>
