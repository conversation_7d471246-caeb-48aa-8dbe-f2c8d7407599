<!-- 区域列表 -->
<template>
  <div class="region-list">
    <div class="region-list__head">
      <span class="title">
        <img :src="regionData.icon" alt="" />
        <span>{{ regionData.resourceName }}</span>
      </span>
    </div>
    <div class="region-list__body">
      <el-table
        :data="regionData.regionList"
        style="width: 100%"
        :max-height="regionData.regionList.length > 10 ? 437 : null"
        :header-cell-style="headerStyle"
        :row-class-name="activeRowClassName"
        size="small"
        @row-click="handleRowClk"
      >
        <el-table-column prop="regionId" label="资源ID" />
        <el-table-column prop="regionName" label="资源名称" class-name="custom-cell">
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>
<script>
export default {
  name: 'regionList',
  props: {
    outsideRow: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      regionData: {
        icon: '',
        resourceName: '',
        regionList: [],
      },
      curRow: {},
      headerStyle: {
        background: '#F4F7FC',
        color: '#000',
        fontWeight: 'bold',
      },
    };
  },
  computed: {
    userInfo() {
      return {};
    },
  },
  created() {
    this.setAreaList(this.outsideRow);
  },
  methods: {
    handleRowClk(row) {
      this.curRow = row;
      this.$emit('clickRow', row.regionCoors);
    },
    async setAreaList({ resourceId, resourcePackageId, resourcePackageName , icon }) {
      const { id, name } = this.userInfo;
      if (resourceId) {
        const res = await this.$post('queryZoneGroupDetail', {
          regionGroupid: resourceId,
          userId: id,
          userName: name,
        });
        const regionData = res.regionGroupList[0] || {};
        this.regionData = {
          icon,
          resourceName: regionData.regionGroupName,
          ...regionData,
        };
      } else if (resourcePackageId) {
        const res = await this.$post('queryResPackageDetailList', {
          userId: id,
          userName: name,
          resourcePackageId,
          pageNum: 1,
          pageSize: null,
        });
        const regionList = res.regionList;
        this.regionData = {
          icon,
          resourceName: resourcePackageName,
          regionList,
        }
      }
    },
    activeRowClassName({ row }) {
      if (row === this.curRow) {
        return 'success-row';
      }
      return '';
    },
  },
};
</script>
<style lang="less" scoped>
.region-list {
  &__head {
    margin: 0 -12px;
    padding: 6px 16px 12px 16px;
    line-height: 22px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.08);
    .title {
      width: 208px;
      height: 22px;
      font-size: 16px;
      font-weight: bold;
      color: rgba(0, 0, 0, 0.85);
    }
  }
  .el-table {
    /deep/ .el-table__row:hover > td {
      background-color: transparent;
      cursor: pointer;
    }
    /deep/ .success-row {
      background-color: #bfe3ff;
    }
    /deep/ .custom-cell {
      padding: 4px 0;
      .cell {
        padding-left: 4px;
        padding-right: 4px;
        line-height: 20px;
      }
    }
    /deep/ .el-table__header-wrapper .gutter {
      background-color: #f6f7fa;
    }
  }
}
</style>
