<template>
    <div>
        <el-tabs v-model="activeName" class="bulk-entry">
            <el-tab-pane v-for="tab in tabs" :key="tab.type" :name="tab.type" :label="tab.type">
                <keep-alive>
                    <upload-file v-if="activeName === tab.type" v-bind="tab" />
                </keep-alive>
            </el-tab-pane>
        </el-tabs>
        <!-- 文件批量导入成功弹框 -->
        <successDialog v-if="successVisible" :visible.sync="successVisible" />
    </div>
</template>

<script>
import uploadFile from './components/uploadFile.vue';
export default {
    name: 'bulk-entry',
    components: {
        uploadFile,
        successDialog: () => import('./components/successDialog.vue'),
    },
    data() {
        return {
            activeName: 'Excel',
            tabs: [
                {
                    type: 'Excel',
                    uploadInfo: {
                        file: {},
                        download: () =>
                            this.download((params) =>
                                this.$downloadFile('downloadRegionTemplate', params)
                            ),
                        downloadText: '下载Excel示例文件',
                        accept: '.xlsx, .xls',
                        explain: '可上传的附件类型为excel，文件编码为UTF-8的文件',
                        validator(filename) {
                            if (!/\.(xls|xlsx)$/.test(filename.toLowerCase())) {
                                return '文件上传格式不正确，请上传xls、xlsx格式';
                            }
                        },
                    },
                    additionInfo: {
                        title: '选择数据规模',
                        status: false,
                        tip: '若单次录入数据量较大（超过2000条）需开启大数据模式，注意：大数据模式上传文件需要排队，有等待时间，需要跳转至资源录入页面进行查询',
                    },
                    create: () => this.createSimpleResource(),
                },
                {
                    type: 'CSV',
                    uploadInfo: {
                        file: {},
                        download: () =>
                            this.download(
                                (params) => this.$downloadFile('downloadCSV', params),
                                '新增自定义区域配置-区域范围-模板.csv'
                            ),
                        downloadText: '下载CSV示例文件',
                        accept: '.csv',
                        explain: '可上传的附件类型为csv，文件编码为GB2312格式的文件',
                        validator(filename) {
                            if (!/\.csv$/.test(filename.toLowerCase())) {
                                return '文件上传格式不正确，请上传csv格式';
                            }
                        },
                    },
                    additionInfo: {
                        title: '选择数据规模',
                        status: false,
                        tip: '若单次录入数据量较大（超过2000条）需开启大数据模式，注意：大数据模式上传文件需要排队，有等待时间，需要跳转至资源录入页面进行查询',
                    },
                    create: () => this.createSimpleResource(),
                },
                {
                    type: 'GeoJson',
                    uploadInfo: {
                        file: {},
                        download: () =>
                            this.download(
                                (params) => this.$downloadFile('downloadGeo', params),
                                '新增自定义区域配置-区域范围-模板.geojson'
                            ),
                        downloadText: '下载GeoJson示例文件',
                        accept: '.geojson',
                        explain: '可上传的附件类型为geoJson，文件编码为UTF-8的文件',
                        validator(filename) {
                            if (!/\.geojson$/.test(filename.toLowerCase())) {
                                return '文件上传格式不正确，请上传geoJson格式';
                            }
                        },
                    },
                    additionInfo: {
                        title: '字段映射',
                        mapRelationship: [],
                        readFile: (params) => this.$postFile('geoJsonFileReader', params),
                        tip: '若单次录入数据量较大（超过2000条）需开启大数据模式，注意：大数据模式上传文件需要排队，有等待时间，需要跳转至资源录入页面进行查询',
                    },
                    create: () => {
                        const { uploadInfo, additionInfo } = this.curTab;
                        const { fileId, fileName } = uploadInfo.file;
                        const { id, name } = this.userInfo;
                        this.$post(
                            'addRegionShapeByGeo',
                            {
                                fileId,
                                fileName,
                                fileType: 2,
                                userId: id,
                                userName: name,
                                layerMapRelations: additionInfo.mapRelationship.map((it) => ({
                                    resourceField: it.resourceField,
                                    mapMethod: it.mapMethod,
                                    mapField: it.mapField,
                                })),
                            },
                            true
                        ).then((res) => {
                            if (res.serviceFlag === 'TRUE') {
                                this.$message({
                                    message: '创建成功',
                                    type: 'success',
                                    duration: 1500,
                                    onClose: () => {
                                        this.jumpResLayer();
                                    },
                                });
                            }
                        });
                    },
                },
                {
                    type: 'Shp',
                    uploadInfo: {
                        file: {},
                        download: () =>
                            this.download(
                                (params) => this.$downloadFile('downloadShp', params),
                                '新增自定义区域配置-区域范围-shp模板.zip',
                                'application/zip'
                            ),
                        downloadText: '下载Shp示例文件',
                        accept: '.zip',
                        explain: '可上传的附件类型为shp，文件编码为UTF-8的文件',
                        validator(filename) {
                            if (!/\.zip$/.test(filename.toLowerCase())) {
                                return '文件上传格式不正确，请上传zip格式';
                            }
                        },
                    },
                    additionInfo: {
                        title: '字段映射',
                        mapRelationship: [],
                        readFile: (params) => this.$postFile('shapeFileReader', params),
                        tip: '若单次录入数据量较大（超过2000条）需开启大数据模式，注意：大数据模式上传文件需要排队，有等待时间，需要跳转至资源录入页面进行查询',
                    },
                    create: async () => {
                        const { uploadInfo, additionInfo } = this.curTab;
                        const { fileId, fileName } = uploadInfo.file;
                        const { id, name } = this.userInfo;
                        this.$post(
                            'addRegionShapeByShp',
                            {
                                fileId,
                                fileName,
                                fileType: 1,
                                userId: id,
                                userName: name,
                                layerMapRelations: additionInfo.mapRelationship.map((it) => ({
                                    resourceField: it.resourceField,
                                    mapMethod: it.mapMethod,
                                    mapField: it.mapField,
                                })),
                            },
                            true
                        ).then((res) => {
                            if (res.serviceFlag === 'TRUE') {
                                this.successVisible = true;
                            }
                        });
                    },
                },
            ],
            successVisible: false,
            importResVisible: false,
            importResult: [],
        };
    },
    computed: {
        curTab() {
            return this.tabs.find((item) => item.type === this.activeName) || {};
        },
        isComplexFile() {
            return ['GeoJson', 'Shp'].includes(this.curTab.type);
        },
        userInfo() {
            return {
                id: 1,
                name: 'admin',
                describe: '系统管理员',
            };
        },
    },
    watch: {
        curTab: {
            handler(newTab) {
                this.$emit('setTab', newTab);
            },
            immediate: true,
        },
    },
    methods: {
        createSimpleResource() {
            const { uploadInfo } = this.curTab;
            const params = new FormData();
            params.append('file', uploadInfo.file.raw);
            this.$postFile('asyncBulkCreate', params).then((res) => {
                if (res.fileId) {
                    this.$message({
                        message: '创建成功',
                        type: 'success',
                        duration: 1500,
                        onClose: () => {
                            this.jumpResLayer();
                        },
                    });
                }
            });
        },
        jumpResLayer() {
            this.$router.push({
                path: 'resManifest',
                query: {
                    layerName: '专有资源',
                },
            });
        },
        download(
            request,
            fileName = '新增自定义区域配置-区域范围-模板.xlsx',
            type = 'application/force-download'
        ) {
            request({}).then((res) => {
                const blob = new Blob([res], { type });
                const linkNode = document.createElement('a');
                linkNode.download = fileName; //a标签的download属性规定下载文件的名称
                linkNode.style.display = 'none';
                linkNode.href = URL.createObjectURL(blob); //生成一个Blob URL
                document.body.appendChild(linkNode);
                linkNode.click(); //模拟在按钮上的一次鼠标单击
                URL.revokeObjectURL(linkNode.href); // 释放URL 对象
                document.body.removeChild(linkNode);
            });
        },
    },
};
</script>

<style lang="less" scoped>
.bulk-entry {
    display: flex;
    flex-direction: column;
    height: 100%;
    /deep/ .el-tabs__content {
        flex: 1;
        overflow: auto;
    }
    /deep/ .el-tabs__item {
        padding: 0 14px;
        font-weight: 500;
        font-size: 16px;
        color: #fff;
        font-family: AlibabaPuHuiTi, AlibabaPuHuiTi;
    }
}
</style>
