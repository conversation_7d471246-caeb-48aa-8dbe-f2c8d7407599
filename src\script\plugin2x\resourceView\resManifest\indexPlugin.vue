<template>
    <div class="resource-layer">
        <!-- 头部 -->
        <headTitle class="resource-layer__head" :paths="paths" :back="() => back()"> </headTitle>
        <!-- 主体 -->
        <div class="resource-layer__body">
            <component
                :key="key"
                :is="curComp"
                v-bind="resEditProps"
                @setCurComp="setCurComp"
                @back="back"
            />
        </div>
    </div>
</template>

<script>
import resource from './components/resource.vue';
import headTitle from '@/script/components/headTitle.vue';
export default {
    name: 'resManifest',
    components: {
        headTitle,
        resource,
        resourceEdit: () => import('@/script/components/resourceEdit.vue'),
    },
    data() {
        return {
            curComp: 'resource',
            paths: ['资源清单'],
            resEditProps: null,
            key: false,
        };
    },
    computed: {
        queryData() {
            return this.$route.query;
        },
        isPrivate() {
            return this.queryData.layerName === '专有资源';
        },
    },
    created() {
        this.paths.push(this.queryData.layerName);
    },
    watch: {
        queryData: {
            handler(newVal) {
                const layerId = Number(newVal.layerId);
                if (layerId === 18 || layerId === 19) {
                    this.curComp = 'resource';
                    this.paths = ['资源清单', newVal.layerName];
                    this.resEditProps = null;
                    this.key = !this.key;
                }
            },
            deep: true,
        },
    },
    methods: {
        back() {
            if (this.curComp === 'resourceEdit') {
                this.curComp = 'resource';
                this.paths.pop();
            } else {
                this.$router.push({
                    name: 'manifest',
                    query: {
                        resType: this.isPrivate ? 'private' : 'public',
                    },
                });
            }
        },
        setCurComp(compName, resEditProps) {
            this.curComp = compName;
            if (compName === 'resourceEdit') {
                this.resEditProps = resEditProps;
                this.paths.push('资源编辑');
            }
        },
    },
};
</script>

<style lang="less" scoped>
.resource-layer {
    display: flex;
    flex-direction: column;
    height: 100%;
    &__body {
        position: relative;
        flex: 1;
        height: 0;
        background: rgba(23, 42, 65, 0.6);
        box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.65);
        border-radius: 12px;
        backdrop-filter: blur(8px);
    }
}
.bold {
    font-weight: bold;
}
</style>
