<template>
  <div class="resource-edit">
    <!-- 主体 -->
    <div class="resource-edit__main">
      <div class="operate earth-overflow-y">
        <singleEntry
          ref="singleEntryRef"
          class="custom-entry"
          :form="form"
          :shapeType="form.shapeType"
          :handleExpand="handleExpand"
        >
          <template #resName>
            <span>基础信息</span>
          </template>
        </singleEntry>
        <el-button class="save" type="primary" size="mini" @click="createResource">保存</el-button>
      </div>
      <div class="map">
        <!-- 地图 -->
        <gisMap
          ref="gisCom"
          curType="singleEntry"
          :city="form.city"
          :regionCoors="regionCoors"
          :layerList="layerList"
          :baseInfo="baseInfo"
          :defExpansion="3"
          :shapeType="form.shapeType"
          :roadLength.sync="form.roadLength"
          :expand="form.expand"
          isShowTool
          isShowHideManage
          isShowBaseStationManage
        />
      </div>
    </div>
  </div>
</template>

<script>
import singleEntry from '@/script/components/singleEntry.vue';
import gisMap from '@/script/components/gis/gisComLast.vue';
import overlapsArea from '@/script/components/gis/layerResource/components/overlapsArea.vue';
import sharpInfo from '@/script/components/gis/layerResource/components/sharpInfo.vue';
import operateInfo from '@/script/components/gis/layerResource/components/operateInfo.vue';
import detailInfo from '@/script/components/gis/layerResource/components/detailInfo.vue';
import posImg from '@/img/space/icons/pos.png';
import baseStation from '@/script/components/gis/layerResource/components/baseStation/index.vue';
import imgInfo from '@/script/constant/popoverImgs.js';
import { getRegions } from '@/utils/method.js';
export default {
  name: 'resource-edit',
  components: {
    singleEntry,
    gisMap,
  },
  props: {
    baseInfo: {
      type: Object,
    },
    curRow: {
      type: Object,
    },
    isShowBaseStation: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      form: {
        regionName: '',
        city: [],
        layerIDs: [],
        shapeType: undefined,
        regionTypeIDs: [],
        isIndoor: 3, // 基站类型-全部
        shapeMethod: 1,
        describe: '',
        layerStatus: 1,
        // 线路特有参数
        roadLength: 0,
        roadType: 0,
        expand: 1,
      },
      regionId: '', // 当前图层id
    };
  },
  computed: {
    userInfo() {
      return {};
    },
    isPolygonOrCircle() {
      return [1, 2].includes(this.form.shapeType);
    },
    layerList() {
      return [
        {
          label: '详细信息',
          prop: detailInfo,
          icons: [imgInfo.details, imgInfo.detailsActive],
          attrs: {
            baseInfo: this.baseInfo,
          },
          isShow: true,
        },
        {
          label: '重叠区域',
          prop: overlapsArea,
          icons: [imgInfo.layer, imgInfo.layerActive],
          isShow: this.isPolygonOrCircle,
          attrs: {
            row: {
              ...this.baseInfo,
              layerIDs: this.baseInfo.layerIDs.join('、'),
            },
          },
        },
        {
          label: '操作信息',
          prop: operateInfo,
          icons: [imgInfo.operate, imgInfo.operateActive],
          attrs: {
            baseInfo: this.baseInfo,
            row: this.curRow,
          },
          isShow: true,
        },
        {
          label: '轮廓信息',
          prop: sharpInfo,
          icons: [imgInfo.sharp, imgInfo.sharpActive],
          isShow: true,
        },
        {
          label: '基站信息',
          prop: baseStation,
          icons: [imgInfo.baseStation, imgInfo.baseStationActive],
          attrs: {
            baseInfo: this.baseInfo,
            outsideRow: { ...this.curRow, icon: posImg },
          },
          isShow: this.isShowBaseStation,
        },
      ];
    },
  },
  created() {
    this.initData();
  },
  methods: {
    initData() {
      if (Object.keys(this.baseInfo || {}).length) {
        Object.keys(this.form).forEach((key) => {
          if (key === 'city') {
            const { province, city } = this.baseInfo;
            this.form.city.push(province, city);
          } else {
            this.form[key] = this.baseInfo[key];
          }
        });
        this.regionCoors = getRegions(this.baseInfo, this.curRow.shapeType);
      }
    },
    async createResource() {
      // 表单校验
      const valid = await this.$refs.singleEntryRef.validate();
      if (!valid) {
        this.$message.warning('存在必填项未填写');
        return;
      }

      const { id, name } = this.userInfo;
      const { regionId } = this.baseInfo;
      const { city, layerIDs, regionTypeIDs } = this.form;
      const areas = this.formatCity(city);

      const regionParams = this.$refs.gisCom.getRegionParams();
      if (!regionParams) {
        this.$message.error('请先绘制图形');
        return;
      }

      // 编辑
      const cellEditParams = this.getCellEditParams();
      this.$post('editLayer', {
          userId: id,
          userName: name,
          regionId,
          ...this.form,
          ...areas,
          attributes: {
            layerIDs,
            regionTypeIDs,
          },
          layerIDs: undefined,
          regionTypeIDs: undefined,
          ...regionParams,
          ...cellEditParams,
        })
        .then(() => {
          this.$message.success('编辑成功');
          this.$emit('back');
        });
    },
    getCellEditParams() {
      const addedBasePoints = this.$refs.gisCom.getAddedBasePoints();
      const circleChoices = this.$refs.gisCom.circleChoices;
      if (addedBasePoints.length) {
        return {
          cgiCode: addedBasePoints,
          auxiliaryCircleList: Object.keys(circleChoices).map((item) => {
            return {
              radius: circleChoices[item].radius,
              centerLatitude: circleChoices[item].centerLatitude,
              centerLongitude: circleChoices[item].centerLongitude,
            };
          }),
        };
      }
      return {};
    },
    formatCity(areas) {
      return {
        province: areas[0],
        city: areas[1],
      };
    },
    jumpResLayer() {
      let timer = setTimeout(() => {
        this.$router.push({
          path: 'resManifest',
          query: {
            layerName: '专有资源',
          },
        });
        clearTimeout(timer);
        timer = null;
      }, 1500);
    },
    handleExpand() {
      if (this.$refs.gisCom) {
        this.$refs.gisCom.setExpand();
      }
    },
  },
};
</script>

<style lang="less" scoped>
.resource-edit {
  padding: 12px;
  display: flex;
  flex-direction: column;
  height: 100%;

  &__main {
    flex: 1;
    display: flex;
    height: 0;

    .operate {
      flex-basis: 310px;
      padding: 8px;
      background: rgba(23, 42, 65, 0.6);
            box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.65);
            border-radius: 12px;
            backdrop-filter: blur(8px);
      .custom-entry {
        /deep/ .wrap {
          margin-top: 10px;
        }
      }
      .save {
        margin: 12px 4px 0 0;
        float: right;
        width: 40%;
      }
    }

    .map {
      position: relative;
      padding: 0 12px 0 0;
      width: calc(100% - 310px);
      &::before {
        content: '';
        display: block;
        position: absolute;
        left: 0;
        top: 0;
        margin: 12px 0;
        width: 9px;
        height: calc(100% - 24px);
        background: linear-gradient(90deg, rgba(0, 0, 0, 0.16) 0%, rgba(0, 0, 0, 0) 100%);
        opacity: 0.74;
        z-index: 1;
      }
    }
  }
}
</style>
