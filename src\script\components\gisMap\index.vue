<template>
    <div class="gis-map">
        <div id="frame-gis" class="earth-h-full" ref="mapRef"></div>
        <div class="tip">已获得高德地图的地图服务使用授权</div>
    </div>
</template>

<script>
const { GIS } = window.MTGIS;
import { gisOptions, cityOption } from '_const/gis.js';
import {Decrypt} from '@/utils/encrypted.js';
export default {
    name:'gisMap',
    mounted() {
        this.initGis();
        window.addEventListener('resize',  this.resizeGis);
    },
    activated(){
        this.resizeGis();
    },
    beforeDestroy(){
        window.removeEventListener('resize',  this.resizeGis);
    },
    methods:{
        initGis(){
            const g = new GIS({
                dom: document.querySelector('#frame-gis'),
                initialZoom: 17,
                city: cityOption,
                options: gisOptions,
            });
            g.gis.color = 0x081422;
            const serverPath = Decrypt(window.baseMapUrl).replace(/\{/g, '${');
            g.baseMapConfig.setBaseMapConfig({
                name: "底图图层",
                type: "gcj02",
                serverPath: serverPath,
                option: {
                    colorControl: true
                }
            });
            g.tileLayerList['高德底图'].colorChangeDate.light.setHex(0x081422);
            g.tileLayerList['高德底图'].colorChangeDate.dark.setHex(0xa5a6f9);
            g.layerList.圈选.unitMeter = true;
            this.g = g;
            this.$emit('loaded', g);
        },
        resizeGis(){
            this.g && this.g.gis.reSize();
        }
    }
}
</script>

<style lang="less" scoped>
.gis-map{
    width:100%;
    height:100%;
}
.tip{
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
  color: #aaa;
  font-size: 12px;
  z-index: 999;
}
/deep/.pcGis .showInfo .checkedBox,/deep/.pcGis .showInfo .showBox{
    visibility:hidden;
}
</style>