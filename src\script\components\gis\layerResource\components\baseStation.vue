<!-- 基站列表 -->
<template>
  <div class="base-station">
    <div class="base-station__head">
      <span class="title">
        <img :src="baseData.icon" alt="" />
        <span>{{ baseData.resourceName }}&nbsp;&nbsp;</span>
        <span style="font-size:12px;">(基站数量：{{tableData.length}}个)</span>
      </span>
    </div>
    <div class="base-station__body">
      <dataTable
        class="data-table"
        :columns="columns"
        :tableData="tableData"
        :paginationData="paginationData"
        :row-class-name="activeRowClassName"
        :max-height="480"
        layout="total, prev, pager, next, sizes"
        border
        @rowClick="handleRowClk"
      >
        <template #LACCELL="{ row }"> {{ row.lac }}-{{ row.cell }} </template>
        <template #gen="{ row }">
          {{ String(row.gen).toUpperCase() }}
        </template>
        <template #cellBoundary="{ row }">
          {{ getBoundary(row.cellBoundary) }}
        </template>
      </dataTable>
    </div>
  </div>
</template>
<script>
import { baseStationTableCols } from '@/script/constant/resourceManifest.js';
import dataTable from '@/script/components/dataTable.vue';
export default {
  name: 'baseStationShow',
  components: {
    dataTable,
  },
  props: {
    outsideRow: {
      type: Object,
      default: () => ({}),
    },
    baseInfo: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      columns: baseStationTableCols(this.renderHeader, false),
      baseData: {
        icon: '',
        resourceName: '',
        regionList: [],
      },
      curRow: {},
      headerStyle: {
        background: '#F4F7FC',
        color: '#000',
        fontWeight: 'bold',
      },
      paginationData: {
        curPage: 1,
        pageSize: 10,
        totalCount: 0,
      },
      isArea: null,
      cellBoundary: null,
      tableData: [],
    };
  },
  computed: {
    userInfo() {
      return {};
    },
  },
  watch: {
    isArea() {
      this.setTableData(this.baseData.regionList);
    },
    cellBoundary() {
      this.setTableData(this.baseData.regionList);
    },
  },
  created() {
    Object.assign(this.baseData, {
      icon: this.outsideRow.icon,
      resourceName: this.outsideRow.resourceName,
    });
    this.setAreaList(this.outsideRow);
  },
  methods: {
    handleRowClk(row) {
      this.curRow = row;
      this.$emit('clickRow', row.regionCoors);
      this.$emit('setLocation', row);
    },
    async setAreaList({ resourceId, regionId }) {
      const isMultiRegion = Boolean(
        this.baseInfo.multiPolygonList && this.baseInfo.multiPolygonList.length
      );
      const params = {
        expansion: 0,
        type: 0, //
        shapeType: this.outsideRow.shapeType, // 
        regionId: resourceId || regionId,
        isMultiRegion: Number(isMultiRegion),
      };
      const { regionInnerList, regionOutList } =
        await this.$post('getRegionExpansionCells', params);
      let filterData = regionInnerList.filter((item) => item.cellType === 2);
      if(!filterData.length){
        filterData = regionInnerList;
      }
      const innerList = filterData.map((item) => ({
        ...item,
        status: 'added',
        isArea: '是',
      }));
      const outList = regionOutList.map((item) => ({
        ...item,
        status: 'noAdd',
        isArea: '否',
      }));
      this.baseData.regionList = innerList.concat(outList);
      this.tableData = this.baseData.regionList;
    },
    activeRowClassName({ row }) {
      if (row === this.curRow) {
        return 'success-row';
      }
      return '';
    },
    renderHeader(h, { column, menu }) {
      const prop = column.property;
      return h(
        'el-select',
        {
          class: 'custom-select', // 添加的class类名
          props: {
            value: this[prop], // 这里是你的变量 this[prop]
            size: 'mini', // 设置el-select的size属性为mini
          },
          on: {
            change: (command) => {
              this[prop] = command; // 更新你的变量
            },
          },
        },
        menu.map((it) =>
          h('el-option', { props: { value: it.value, label: it.label }, key: it.value }, it.label)
        )
      );
    },
    getBoundary(cellBoundary) {
      if (Number.isFinite(cellBoundary)) {
        return (cellBoundary / 1000).toFixed(2);
      }
      return '-';
    },
    setTableData(allTableData) {
      allTableData = allTableData.filter((item) => {
        const isArea = this.isArea ? item.isArea === this.isArea : true;
        const cellBoundary = this.cellBoundary ? Number(this.getBoundary(item.cellBoundary)) : true;
        let isCellBoundary;
        if (!this.cellBoundary) {
          isCellBoundary = true;
        } else if (this.cellBoundary === 10) {
          isCellBoundary = cellBoundary > this.cellBoundary;
        } else {
          isCellBoundary = cellBoundary < this.cellBoundary;
        }
        return isArea && isCellBoundary;
      });
      this.tableData = allTableData;
    },
  },
};
</script>
<style lang="less" scoped>
.base-station {
  &__head {
    margin: 0 -12px;
    padding: 6px 16px 12px 16px;
    line-height: 22px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.08);
    .title {
      width: 208px;
      height: 22px;
      font-size: 16px;
      font-weight: bold;
      color: rgba(0, 0, 0, 0.85);
    }
  }
  &__body {
    .data-table {
      margin-top: 10px;
    }
  }
  /deep/.custom-select {
    .el-input {
      font-size: 14px;
      & > .el-input__inner {
        padding-left: 0px;
        border: none;
        background-color: transparent;
      }
    }
  }
}
</style>
