import Vue from 'vue'
import Vuex from 'vuex'

Vue.use(Vuex)

export default new Vuex.Store({
	state: {
		userName:'',
		ssoToken:'',
		spaceResource: {
            districts: [],
            areas: [],
            originAreas: [],
            layers: [],
            curSelectResource: {},
            originDistricts: [],
        }
	},
	getters:{
		getUserName:(state) => {
			return state.userName;
		},
		getToken:(state) => {
			return state.ssoToken;
		},
		spaceDistricts: (state) => {
            return state.spaceResource.districts;
        },
        spaceOriginDistricts: (state) => {
            return state.spaceResource.originDistricts;
        },
        spaceAreas: (state) => {
            return state.spaceResource.areas;
        },
        SpaceOriginAreas: (state) => {
            return state.spaceResource.originAreas;
        },
        spaceLayers: (state) => {
            return state.spaceResource.layers;
        },
        spaceCurSelectResource: (state) => {
            return state.spaceResource.curSelectResource;
        },
	},
	mutations: {
		setUserName(state, payload) {
			state.userName = payload;
		},
		setToken(state, payload) {
			state.ssoToken = payload;
		},
		setSpaceDistricts(state, payload) {
            state.spaceResource.districts = payload;
        },
        setSpaceOriginDistricts(state, payload) {
            state.spaceResource.originDistricts = payload;
        },
        setSpaceAreas(state, payload) {
            state.spaceResource.areas = payload;
        },
        setSpaceOriginAreas(state, payload) {
            state.spaceResource.originAreas = payload;
        },
        setSpaceLayers(state, payload) {
            state.spaceResource.layers = payload;
        },
        setSpaceCurSelectResource(state, payload) {
            state.spaceResource.curSelectResource = payload;
        }
	},
	actions: {
	},
	modules: {
	}
})
