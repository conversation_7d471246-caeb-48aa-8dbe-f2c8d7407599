<template>
  <div class="operate-region">
    <div class="operate-region__search"></div>
    <div class="operate-region__body">
      <customTip>
        <div class="title">
          <span class="count">
            共 {{ paginationData.totalCount }} 条， 选择（<span class="blue">{{
              selectList.length
            }}</span
            >）
          </span>
        </div>
      </customTip>
      <!-- 表格 -->
      <dataTable
        ref="dataTable"
        class="data-table"
        :columns="columns"
        :tableData="tableData"
        :rowClassName="activeRowClassName"
        :paginationData="paginationData"
        layout="total, prev, pager, next, sizes"
        isSelectable
        @updateTable="search"
        @selectChange="handleSelect"
        @selectAll="selectAll"
        @rowClick="handleRowClick"
      >
        <template #LACCELL="{ row }"> {{ row.lac }}-{{ row.cell }} </template>
        <template #gen="{ row }">
          {{ String(row.gen).toUpperCase() }}
        </template>
        <template #cellBoundary="{ row }">
          {{ getBoundary(row.cellBoundary) }}
        </template>
        <template #operate="{ row }">
          <el-button type="text" @click="del(row)">移除</el-button>
        </template>
      </dataTable>
    </div>
  </div>
</template>
<script>
import dataTable from '@/script/components/dataTable.vue';
import customTip from '@/script/components/customTip.vue';
import { baseStationTableCols } from '@/script/constant/resourceManifest.js';
import baseMixin from '../mixin';
export default {
  name: 'added',
  components: {
    dataTable,
    customTip,
  },
  mixins: [baseMixin],
  props: {
    baseStations: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      columns: baseStationTableCols(this.renderHeader),
      selectList: [],
    };
  },
  computed: {
    userInfo() {
      return {};
    },
  },
  methods: {
    del(row) {
      row.status = 'noAdd';
      this.$emit('delAddedBase', row);
    },
    handleSelect(selection) {
      if (this.isSelectAll) {
        this.selectList = this.baseStations;
      } else {
        this.selectList = selection;
      }
      this.$emit('setSelectList', this.selectList, 'added');
    },
    selectAll(selection) {
      if (!selection.length) {
        this.selectList = [];
      } else {
        this.selectList = this.baseStations;
      }
      this.$emit('setSelectList', this.selectList, 'added');
    },
  },
};
</script>
<style lang="less" scoped>
.operate-region {
  flex: 1;
  height: 0;
  display: flex;
  flex-direction: column;
  &__search {
    .el-button {
      width: 100%;
    }
  }
  &__body {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 10px 10px 0 10px;
    height: 0;
    // background: #ffffff;
    border-radius: 6px;
    border: 1px solid rgba(255, 255, 255, .3);
    box-shadow: inset 0px 0px 0px 0px rgba(0, 0, 0, 0.5);
    .title {
      color: rgba(255, 255, 255, .7);
    }
    .data-table {
      flex: 1;
      height: 0;
      margin: 10px 0;
    }
  }
}
.blue {
  color: #0091ff;
}
/deep/.custom-select {
  .el-input {
    font-size: 14px;
    & > .el-input__inner {
      padding-left: 0px;
      border: none;
      background-color: transparent;
    }
  }
}
</style>
