<template>
  <el-dialog
    class="check-layer"
    :visible="visible"
    :close-on-click-modal="false"
    top="80px"
    destroy-on-close
    append-to-body
    @close="handleClose"
  >
    <div class="check-layer__wrap">
      <div class="check-layer__history">
        <div class="title">
          <slot>
            <span class="date">{{ layerInfo.updateTime }}</span>
            <span class="name">历史图层</span>
            <el-button type="primary" size="mini" @click="compare">对比</el-button>
            <el-button v-if="isPrivate" type="warning" size="mini" @click="restoringLayer">还原历史图层</el-button>
          </slot>
        </div>
        <gis-com
          ref="gisComHistory"
          key="history"
          gisId="refs_history_gis_api"
          tag="history-tag"
          class="content"
          v-bind="$attrs"
          v-on="$listeners"
          :regionCoors="historyRegionCoors"
          :baseInfo="layerInfo"
          :isShowLayers="false"
          :defExpansion="3"
          :shapeType="layerInfo.shapeType"
          isShowInnerCity
          isJustShowLayer
        />
      </div>
      <div v-if="isShow" class="check-layer__current">
        <div class="title">
          <slot name="compareTitle">
            <span class="date">{{ layerInfo.curDate }}</span>
            <span class="name">目前图层</span>
            <el-button size="mini" @click="passCompare">取消对比</el-button>
          </slot>
        </div>
        <gis-com
          ref="gisComCurrent"
          tag="current-tag"
          gisId="refs_current_gis_api"
          class="content"
          v-bind="$attrs"
          v-on="$listeners"
          :regionCoors="curRegionCoors"
          :baseInfo="baseInfo"
          :isShowLayers="false"
          :defExpansion="3"
          :shapeType="layerInfo.shapeType"
          isShowInnerCity
          isJustShowLayer
          isShowHideManage
        />
      </div>
      <span class="close">
        <i class="el-icon-close" @click="handleClose"></i>
      </span>
    </div>
  </el-dialog>
</template>
<script>
import { getRegions } from '@/utils/method.js';
export default {
  name: 'check-layer',
  components: {
    gisCom: () => import('../../gisComLast.vue'),
  },
  inject: ['layerRoot'],
  props: {
    baseInfo: {
      type: Object,
      default: () => ({}),
    },
    visible: {
      type: Boolean,
      default: false,
    },
    layerInfo: {
      type: Object,
      default: () => ({}),
    },
    isRestoreRecord: {
      type: Boolean,
      default: false,
    },
    // 专有资源
    isPrivate: {
      type: Boolean,
      default: false,
    },
    customRestore: {
      type: Function,
    }
  },
  data() {
    return {
      isShow: false,
      historyRegionCoors: '',
      curRegionCoors: '',
    };
  },
  created() {
    this.layerRoot.isAllowClkOut = false;
    const { curRegionCoors = [], isMultiRegion, shapeType } = this.layerInfo;
    this.historyRegionCoors = getRegions(this.layerInfo, shapeType, Number(isMultiRegion));
    this.curRegionCoors = curRegionCoors;
  },
  beforeDestroy() {
    this.layerRoot.isAllowClkOut = true;
  },
  methods: {
    compare() {
      this.isShow = true;
    },
    restoringLayer() {
      if (this.customRestore) {
        this.customRestore();
        return;
      }
      this.$post('recoverHistoryRegionShape', {
          regionId: this.baseInfo.regionId,
          ...this.layerInfo.srcItem,
        }, true)
        .then((res) => {
          if (res.serviceFlag === 'TRUE') {
            this.$message.success(res.returnMsg);
            this.$emit('update:isRestoreRecord', true);
            this.handleClose();
            this.$emit('updateRecords');
          }
        });
    },
    passCompare() {
      this.isShow = false;
    },
    handleClose() {
      this.isShow = false;
      this.$emit('update:visible', false);
    },
  },
};
</script>
<style lang="less" scoped>
.check-layer {
  &__wrap {
    display: flex;
    position: relative;
    height: 100%;
  }
  &__history,
  &__current {
    margin-left: 12px;
    flex: 1;
    display: flex;
    width: 50%;
    flex-direction: column;
    .title {
      height: 40px;
      line-height: 40px;
      color: #0091ff;
      .name {
        margin: 0 6px;
      }
    }
    .content {
      flex: 1;
    }
  }
  .close {
    position: absolute;
    right: 0;
    top: 10px;
  }
  /deep/ .el-dialog {
    width: 90%;
    height: 80%;
    background: unset;
  }
  /deep/ .el-dialog__header {
    display: none;
  }
  /deep/ .el-dialog__body {
    padding: 0 12px 12px 12px;
    height: 100%;
    background: #040A1E;
    box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.65);
    border-radius: 12px;
    backdrop-filter: blur(8px);
  }
}
.bold {
  font-weight: bold;
  color: #454545;
}
</style>
