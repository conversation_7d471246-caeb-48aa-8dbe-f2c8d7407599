import { getCoordinateExtremum, getNearestPoint } from '@/utils/utils.js'; 
const MERGE_LEVEL = { 18: 1, 17: 1, 16: 15, 15: 30, 14: 30, 13: 30, 12: 30 }; //点位聚合比例

export default (g, option = {}) => {
  let graphs = [];
  let { img, activeImg, click, cancelClick, globalSize = 50 } = option;
  let curClickPoint = null;
  let curDivMesh = null;
  // 创建图层
  let layer = new g.layer();
  layer.visible = true;
  g.gis.scene.add(layer);

  class Point {
    constructor(latLng, data) {
      this.latLng = latLng;
      this.data = data;
      this.mesh = null;
    }
    static getGraphs() {
      return graphs;
    }
    static setCurClickPoint(curPoint) {
      curClickPoint = curPoint;
    }
    static handleClick(data, event) {
      const curPoint = data.object.curPoint;
      if (curPoint === curClickPoint) return;
      if (event.button === 0) {
        // 还原之前的激活点
        Point.revertPoint();
        // 激活新的点
        curPoint.remove();
        curPoint.draw(curPoint.latLng, activeImg, 75);
        curClickPoint = curPoint;
        curDivMesh = click && click(curPoint);
      }
    }
    static revertPoint() {
      if (curClickPoint) {
        curClickPoint.remove();
        curClickPoint.draw(curClickPoint.latLng, img);
        curClickPoint = null;
      }
      if (curDivMesh) {
        curDivMesh.remove();
        curDivMesh = null;
      }
    }
    static removeAll() {
      if (curClickPoint) {
        curClickPoint.remove();
        curClickPoint = null;
      }
      if (curDivMesh) {
        curDivMesh.remove();
        curDivMesh = null;
      }
      layer.removeAll();
    }
    static createPoints(points, isRemoveAll = true , isRefresh = true) {
      if (!points || !points.length) {
        Point.removeAll();
        graphs = [];
        return;
      }
      if (isRemoveAll) {
        Point.removeAll();
        graphs = [];
      }
      let mergePoints = [];
      const mergePixel = MERGE_LEVEL[g.cameraControl.mapInfo.zoom] || 30;
      if(mergePixel !== 1 && points.length > 500 && points[0].lng){
        mergePoints = g.math.mergeData(points, g.math.getLengthByPixi(mergePixel));//数据聚合
        console.log('当前层级:',g.cameraControl.mapInfo.zoom,'聚合比例:',mergePixel,'聚合前:',points.length ,'聚合后:',mergePoints.length);
      }else {
        mergePoints = points;
        console.log('当前层级:',g.cameraControl.mapInfo.zoom,'层级过小无需聚合或页面数据未配置聚合(points[0].lat,points[0].lng)');
      } 
      for (const point of mergePoints) {
        const { latLng, data } = point.mid === undefined?point:point.mid;
        const curPoint = new Point(latLng, data);
        curPoint.draw();
        graphs.push(curPoint);
      }
      if(!isRefresh){
        Point.toMoveCenter();
      }
    }
    static addTip(tip, latLng, style = '') {
      return g.layerList.divLayer.addDiv({
        dom: ` 
          <div style="
            position: absolute;
            bottom: 40px;
            padding: 4px 6px;
            width:  fit-content;
            text-align: center;
            background-color: rgba(255, 255, 255, 0.95); /* 半透明背景 */
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2); /* 可选，增加阴影效果 */
            pointer-events: none;
            transform: translateX(-50%);
            white-space: nowrap;
            border-radius: 4px;
            color: rgba(0,0,0,0.65);
            font-size: 12px;
            ${style}
          ">
            ${tip}
          </div>
        `,
        point: latLng,
      });
    }
    static toMoveCenter() {
      let latLng = null;
      const points = graphs.map(item => item.latLng);
      if (points.length < 4) {
        latLng = points[points.length - 1];
      } else {
        //计算数据中心点
        let vertexes = getCoordinateExtremum(points);
        vertexes = vertexes.map(item => [item.lng, item.lat])
        let pointList = g.math.lsRegular(vertexes);
        let pontMaxMin =  g.math.getPointsMaxMin(pointList);
        const centerLatlng = g.math.three2world({
            x: (pontMaxMin.maxX + pontMaxMin.minX)/2,
            y: 0,
            z: (pontMaxMin.maxY + pontMaxMin.minY)/2,
        });
        latLng = {
          lat:centerLatlng.lat,
          lng: centerLatlng.lng,
        };
        const nearestPoint = getNearestPoint(points, centerLatlng, (point) => {
          return g.math.worldDistance(point, centerLatlng);
        })
        if (nearestPoint) {
          latLng = nearestPoint;
        }
      }
      g.cameraControl.move({
        lat:latLng.lat,
        lng: latLng.lng,
        ht: 1,
        showArrow: false,
      });
    }
    static toMovePoint(latLng) {
      g.cameraControl.move(latLng);
      let timer = setTimeout(() => {
        g.cameraControl.zoom = 17;
        clearTimeout(timer);
        timer = null;
      }, 300);
    }
    static destroy() {
      g = null;
      graphs = null;
      curClickPoint = null;
      curDivMesh = null;
      option = null;
      layer = null;
    }
    draw(latLng = this.latLng, image, size = globalSize) {
      const points = [{ ...latLng, ht: 20, size, dir: 0 }];
      const material = g.meshList.img.getMaterial({ url: image || img, opacity: 1 });
      points.autoScale = true;
      const mesh = g.meshList.img.create(points, material);
      mesh.curPoint = this;
      layer.add(mesh);
      this.mesh = mesh;
    }
    remove() {
      layer.remove(this.mesh);
    }
  }
  // 绑定事件
  g.event.addClick(layer, Point.handleClick,() => {
    Point.revertPoint();
    cancelClick && cancelClick(curClickPoint);
  }
);

  return Point;
};
