const path = require('path');
const JavaScriptObfuscator = require('webpack-obfuscator');
const CompressionWebpackPlugin = require('compression-webpack-plugin');
const UglifyJsPlugin = require('uglifyjs-webpack-plugin');
const isProduction = process.env.NODE_ENV === 'production';
module.exports = {
    lintOnSave: false,
    outputDir:process.env.VUE_APP_OUTPUTDIR || 'earthquakeDist',
    publicPath: './',
	productionSourceMap: false,
    devServer: {  
		hot: true, // 开启热更新  
		port: 8888, // 设置端口号，根据需要可调整  
		proxy: {
			'/earthquake/': {
				target: 'http://*************:19601/', //API服务器的地址
				ws: true, //代理websockets
				changeOrigin: true, // 是否跨域，虚拟的站点需要更管origin
				pathRewrite: {
					'^/earthquake': '',
				},
			},
			'/spaceResource/': {
				target: 'http://*************:19380/', //API服务器的地址
				ws: true, //代理websockets
				changeOrigin: true, // 是否跨域，虚拟的站点需要更管origin
				pathRewrite: {
					'^/spaceResource': '',
				},
			},
			'/locservice/': {
				target: 'http://************:10800/locservice/', //API服务器的地址
				ws: true, //代理websockets
				changeOrigin: true, // 是否跨域，虚拟的站点需要更管origin
				pathRewrite: {
					'^/locservice': '',
				},
			},
		},
    }, 
    configureWebpack: config => {
		config.resolve.alias = {
			'@': path.resolve(__dirname, 'src'),
			'_com': path.resolve(__dirname, 'src/script/components'),
			'_const':path.resolve(__dirname, 'src/script/constant'),
		};
		config.optimization = {
			moduleIds: 'hashed',
			splitChunks: { //代码切割模式
				chunks: "async", //可填 async, initial, all. 顾名思义，async针对异步加载的chunk做切割，initial针对初始chunk，all针对所有chunk。
				minSize: 30000, // 最小尺寸，默认 30kb  
				maxSize: 0, // 最大尺寸，默认无限制  
				minChunks: 1, // 最小被引用次数，默认 1  
				maxAsyncRequests: 30, // 异步并行请求的最大数目，默认 30  
				maxInitialRequests: 30, // 一个入口点初始并行请求的最大数目，默认 30  
				automaticNameDelimiter: '~', // 默认使用 '~' 连接块名  
				name: false, // 拆分出来的 chunk 名称，true 代表自动生成，也可以是一个函数  
				cacheGroups: {
				vendors: {
					test: /[\\/]node_modules[\\/]/, // 提取 node_modules 下的所有模块  
					priority: -10, // 优先级，数值越大优先级越高  
					reuseExistingChunk: true, // 如果已存在相同的 chunk，则复用而不再生成新的 
				},
				default: {
					minChunks: 2, // 默认是 2，表示引用 2 次以上的模块才进行分割  
					priority: -20,
					reuseExistingChunk: true,
				},
				},
			},
		};
      if (isProduction) {
			config.plugins.push(new UglifyJsPlugin({ // 代码压缩
				uglifyOptions: {
					//生产环境自动删除console
					compress: {
						// warnings: false, // 若打包错误，则注释这行
						drop_debugger: true,
						drop_console: true,
						pure_funcs: ['console.log']
					}
				},
				sourceMap: false,
				parallel: true
			}));
			config.plugins.push(new CompressionWebpackPlugin({
				filename: '[path][base].gz[query]',
				algorithm: 'gzip',
				test: /\.js$|\.css$/,
				threshold: 10240,
				minRatio: 1,
				deleteOriginalAssets: false
			}));
			config.plugins.push(new JavaScriptObfuscator({
				compact: true,
				rotateStringArray: true,
				renameGlobals: true,
				// ignoreImports: true,
				// selfDefending: true,
				// stringArray: true,
				stringArrayEncoding: ['base64'],
				stringArrayThreshold: 1,
				// disableConsoleOutput: true,
			}, []))
      }
    },
    css: {
		loaderOptions: {
			postcss: {
				plugins: [
					require('postcss-pxtorem')({
						rootValue: 16,
						propList: ['*'],
									minPixelValue:2,
						remUnit:192
					})
				]
			}
		}
  	},
    chainWebpack:config=>{
		// less 自定义
		const oneOfsMap = config.module.rule('less').oneOfs.store;

		oneOfsMap.forEach(item => {
			item
			.use('style-resources-loader')
			.loader('style-resources-loader')
			.options({
			patterns:path.resolve(__dirname,'src/style/setting.less')
			})
			.end()
		});
		config.module
		.rule('images')
		.test(/\.(png|jpe?g|gif|webp)$/i)
		.use('url-loader')
		.loader('url-loader')
		.options({
			limit: 100000,
			name: 'img/[name].[hash:8].[ext]'
		})
		.end()
    },
}
