<template>
	<div id="app">
		<earthquake/>
	</div>
</template>

<script>
import earthquake from '../src/script/plugin2x/main/indexPlugin.vue';
import {getUrlKey} from '../src/utils/utils.js';
import { request } from '@/utils/request.js';

export default {
	name: 'App',
	components: {
		earthquake,
	},
	data(){
		return{
			isShowView:null
		};
	},
	created(){
		this.setPageToken();
		window.permission = process.env.VUE_APP_PERMISSION;
		const token = getUrlKey('token');
		token && (localStorage.setItem('token',token));
		if(window.permission === 'true'){
			this.singleSignOnVerify();
		}
	},
	methods:{
		setPageToken() {
			const pageToken = this.$route.query.pageToken;
			if (pageToken) {
				localStorage.setItem('pageToken', pageToken);
			}
		},
		singleSignOnVerify(){
			const isVerify =  process.env.VUE_APP_HASVERIFY;
			const token = localStorage.getItem('token');
			if(!token){
				this.$router.push({
					path:'/errorPage'
				})
				return;
			}
			if(!isVerify){
				return;
			}
			request('get', 'verify', {token:token}).then((rcvData) => {
				
			})
			.catch((err) => {
				if(err.response.status !== 401){
					this.$router.push({
						path:'/errorPage'
					})
				}
			});
		},
	}
}
</script>

<style lang="less">
#app {
  font-family: "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  color: #2c3e50;
  width: 100%;
  height: 100%;
  font-size:.78rem;
  background:#040A1E;
}
html{
  font-size:calc(100vw * 16 / 1920);
}
*{
  margin:0;
  padding:0;
  box-sizing:border-box;
}
html,body{
  width:100%;
  height:100%;
  overflow-x:auto!important;
}
body, div, li, a, span, label, i, h1, h2, h3, h4, h5, input, pre, code{
  font-family: "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif;
}
*:before, *:after{
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}
</style>
