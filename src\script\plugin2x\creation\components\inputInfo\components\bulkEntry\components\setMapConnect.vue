<template>
    <el-dialog
        class="set-mapConnect"
        title="建立图层映射关系"
        top="80px"
        :visible="visible"
        append-to-body
        destroy-on-close
        :close-on-click-modal="false"
        @close="handleClose"
    >
        <!-- 主体 -->
        <div class="set-mapConnect__main">
            <div v-if="!readonly" class="tip">
                <i class="el-icon-warning"></i>
                <span>定义文件导入字段和目的配置字段之间的映射</span>
            </div>
            <dataTable
                class="data-table"
                :columns="columns"
                :data="tableData"
                :cell-style="handleCellStyle"
                row-class-name="no-hover"
                border
            >
                <template #label="{ row }">
                    <span :class="{ required: row.required }">{{ row.label }}</span>
                </template>
                <template #mapMethod="{ row }">
                    <el-select
                        v-if="!readonly && row.selectMethod"
                        class="custom-select"
                        v-model="row.mapMethod"
                        size="mini"
                        @change="handleMapMethod(row)"
                    >
                        <el-option v-for="item in mapMethodOpts" :key="item.value" v-bind="item" />
                    </el-select>
                    <span v-else>{{ mapMethodVal[row.mapMethod] }}</span>
                </template>
                <template #mapField="{ row }">
                    <template v-if="!readonly">
                        <selectDistrict
                           class="custom-cascader"
                            v-if="row.isCascade && row.mapMethod === 1"
                            v-model="row.mapField"
                            :options="getMapFieldOpts(row)"
                            :props="{
                                multiple: true,
                                label: 'label',
                                value: 'value',
                                children: 'children',
                                emitPath: false,
                            }"
                        />
                        <el-select
                            v-else-if="row.selectField || row.multiField"
                            v-model="row.mapField"
                            class="field-select"
                            :multiple="row.multiField"
                            :collapse-tags="row.multiField"
                            size="mini"
                        >
                            <el-option
                                v-for="item in getMapFieldOpts(row)"
                                :key="item.value"
                                v-bind="item"
                            />
                        </el-select>
                    </template>
                </template>
            </dataTable>
        </div>
        <!-- 底部 -->
        <div slot="footer" v-if="!readonly" class="dialog-footer">
            <el-tooltip content="若取消则中断此次批量录入" placement="left">
                <el-button class="w-118" size="small" @click="handleClose">取消</el-button>
            </el-tooltip>
            <el-button class="w-118" type="primary" size="small" @click="sure">确定</el-button>
        </div>
    </el-dialog>
</template>
<script>
import dataTable from '@/script/components/dataTableLast.vue';
import {
    singleFormOpts,
    mapRelationshipCols,
    mapRelationship,
    mapMethodOpts,
} from '@/script/constant/resourceCreate.js';
import { getRegionLabels } from '@/utils/method.js';
import { mapFields as mapFieldValues } from '@/script/constant/common.js';
import { mapGetters } from 'vuex';
export default {
    name: 'set-mapConnect',
    components: {
        dataTable,
        selectDistrict: () => import('@/script/components/selectDistrict.vue'),
    },
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        readonly: {
            type: Boolean,
            default: false,
        },
        mapFields: {
            type: Array,
            default: () => [],
        },
    },
    data() {
        return {
            columns: mapRelationshipCols,
            mapMethodOpts,
            mapRelationship,
            mapMethodVal: {
                1: '下拉选择',
                2: '文件匹配',
                3: '自动获取',
            },
        };
    },
    computed: {
        ...mapGetters(['spaceAreas', 'spaceLayers']),
        tableData() {
            if (this.readonly) {
                return this.formatValues(this.mapRelationship);
            }
            return this.mapRelationship;
        },
        mapFieldFileOpts() {
            return this.mapFields.map((val) => ({ label: val, value: val }));
        },
        selectOption() {
            const { isIndoor, shapeMethod } = singleFormOpts;
            return {
                layerIDs: this.spaceLayers,
                regionTypeIDs: this.spaceAreas,
                isIndoor: isIndoor,
                shapeMethod: shapeMethod,
            };
        },
    },
    methods: {
        getMapFieldOpts(row) {
            const { mapMethod, resourceField } = row;
            if (mapMethod === 1) {
                return this.selectOption[resourceField];
            } else if (mapMethod === 2) {
                return this.mapFieldFileOpts;
            }
        },
        handleMapMethod(row) {
            const { mapMethod, resourceField } = row;
            if (mapMethod === 1 && ['layerIDs', 'regionTypeIDs'].includes(resourceField)) {
                row.mapField = [];
            } else {
                row.mapField = '';
            }
        },
        handleClose() {
            this.$emit('update:visible', false);
        },
        sure() {
            // 校验
            const validItems = this.tableData.filter((it) => it.mapMethod !== 3 && it.required);
            const emptyItems = validItems.filter((it) => !it.mapField);
            if (emptyItems.length) {
                const messages = emptyItems.map((item) => item.resourceField).join('、');
                this.$message.warning(`${messages}未填写`);
                return;
            }
            // 赋值
            this.$emit('updateMapFields', this.tableData);
            this.handleClose();
        },
        formatValues(tableData) {
            return tableData.map((item) => {
                let { resourceField, mapMethod, mapField } = item;
                const isDropdown = mapMethod === 1;
                if (!isDropdown) return item;
                switch (resourceField) {
                    case 'layerIDs':
                        mapField = (mapField || [])
                            .map((val) => {
                                const curItem = this.spaceLayers.find((it) => it.value == val);
                                return (curItem || {}).label;
                            })
                            .filter(Boolean)
                            .join('、');
                        break;
                    case 'regionTypeIDs':
                        mapField = getRegionLabels(mapField || [], this.spaceAreas);
                        break;
                    case 'isIndoor':
                        mapField = mapFieldValues.isIndoor[mapField];
                        break;
                    case 'shapeMethod':
                        mapField = mapFieldValues.shapeMethod[mapField];
                        break;
                }
                return {
                    ...item,
                    mapField,
                };
            });
        },
        handleCellStyle({ row, column }) {
            if (row.mapMethod === 3 && column.property === 'mapField') {
                return {
                    // backgroundColor: '#F6F7FA',
                    fontSize: '14px',
                };
            }
        },
    },
};
</script>
<style lang="less" scoped>
.set-mapConnect {
    &__main {
        display: flex;
        flex-direction: column;
        height: 100%;
        .tip {
            display: flex;
            align-items: center;
            margin-bottom: 16px;
            padding-left: 12px;
            height: 32px;
            background: linear-gradient(
                180deg,
                rgba(0, 145, 255, 0.1) 0%,
                rgba(0, 145, 255, 0.25) 100%
            );
            border-radius: 4px;
            color: #0091ff;
        }
        .data-table {
            .required::before {
                content: '*';
                margin-right: 1px;
                color: red;
            }
            .custom-select {
                width: 101px;
                /deep/ .el-input .el-input__inner {
                    font-size: 14px;
                    color: #fff;
                    background: rgba(117, 163, 223, 0);
                    box-shadow: inset 0px 0px 8px 0px #4984ff;
                    border: 0;
                }
            }
            .field-select {
                width: 100%;
                /deep/ .el-input .el-input__inner {
                    font-size: 14px;
                    background: rgba(117, 163, 223, 0);
                    box-shadow: inset 0px 0px 8px 0px #4984ff;
                }
            }
            .custom-cascader {

             /deep/ .el-cascader__search-input {
                  color: #fff;
                  background: unset;
              }
            }
            /deep/ .no-hover:hover > td {
                background-color: unset;
            }
        }
    }
    /deep/ .el-dialog {
        display: flex;
        flex-direction: column;
        border-radius: 10px;
        box-shadow: 0px 6px 30px 0px rgba(0, 0, 0, 0.05), 0px 16px 24px 2px rgba(0, 0, 0, 0.04),
            0px 8px 10px -5px rgba(0, 0, 0, 0.08);
        border-radius: 10px;
        background: rgba(31, 52, 79, 0.8);
        border-radius: 8px;
        backdrop-filter: blur(8px);
    }
    /deep/ .el-dialog__header {
        position: relative;
        padding: 20px;
        border-bottom: 1px solid #ccc;
        .el-dialog__title {
            color: #fff;
            font-size: 18px;
            font-weight: bold;
        }
        .el-dialog__headerbtn {
            top: 19px;
            font-size: 18px;
        }
    }
    /deep/ .el-dialog__body {
        padding: 20px;
        flex: 1;
        height: 0;
    }
    /deep/ .el-dialog__footer {
        padding: 20px;
        border-top: 1px solid #ccc;
    }
}

.w-118 {
    width: 118px;
}
</style>
