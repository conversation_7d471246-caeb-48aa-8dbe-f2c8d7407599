const listDisplay = [
    {
        name: '任务名称：',
        props: 'taskName',
        width:283,
        span:4
    },
    {
        name: '任务ID：',
        props: 'taskId',
        width:210,
        span:3
    },
    {
        name: '创建人：',
        props: 'createUserName',
        width:214,
        span:3
    },
    {
        name: '地震名称：',
        props: 'earthquakeName',
        width:256,
        span:4
    },
    {
        name: '深度（千米）：',
        props: 'earthquakeDepth',
        width:214,
        span:4
    },
    {
        name: '震级（M）：',
        props: 'earthquakeLevel',
        width:424,
        span:6
    },
    {
        name: '发生时间：',
        props: 'occurTime',
        width:283,
        span:4
    },
    {
        name: '时间粒度：',
        props: 'timeType',
        width:210,
        span:3
    },
    {
        name: '分析半径（米）：',
        props: 'analysisRadius',
        width:214,
        span:3
    },
    {
        name: '震中经度（°）：',
        props: 'centerLon',
        width:256,
        span:4
    },
    {
        name: '震中纬度（°）：',
        props: 'centerLat',
        width:214,
        span:4
    },
    {
        name: '分析时间：',
        props: 'time',
        width:424,
        span:6
    },
];

const tabList = [
    {
        name: '1',
        label: '拍照人口',
    },
    {
        name: '2',
        label: '疑似失联人口',
    },
    {
        name: '3',
        label: '疑似掩埋人口',
        disabled: true,
    },
];
const getRandomInt = (n, min, max) => {
    const result = new Set();
    min = Math.ceil(min);
    max = Math.floor(max);
    while (result.size < n) {
        const randomNumber = Math.floor(Math.random() * (max - min + 1)) + min;
        result.add(randomNumber);
    }
    return Array.from(result);
};

const trendAnalysisChart = {
    title: '',
    type: 'common',
    xAxis: [], //横坐标
    isxAxisBreak: true,
    yAxis: [''], //纵坐标
    hasLegend: true,
    data: [
        {
            name: '人数',
            data: [],
            itemStyle: {
                color: '#3470FF',
            },
            type: 'line',
            showSymbol: false,
            areaStyle: {
                color: {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 0,
                    y2: 1,
                    colorStops: [
                        {
                            offset: 0,
                            color: 'rgba(52, 112, 255, 0.5)', // 0% 处的颜色
                        },
                        {
                            offset: 0.5,
                            color: 'rgba(52, 112, 255, 0.15)', // 0% 处的颜色
                        },
                        {
                            offset: 1,
                            color: 'rgba(52, 112, 255, 0.03)', // 100% 处的颜色
                        },
                    ],
                    global: false, // 缺省为 false
                },
            },
        },
    ],
};

const trendAnalysisTable = [
    {
        prop: 'dataTime',
        label: '时间',
    },
    {
        prop: 'popCnt',
        label: '人数',
    },
];
const populationTable = [
    {
        prop: 'taskId',
        label: '时间',
    },
    {
        prop: 'taskName1',
        label: '0-0.5km人数',
    },
    {
        prop: 'taskName2',
        label: '0.5-2km人数',
    },
    {
        prop: 'taskName3',
        label: '2km人数',
    },
];
const populationChart = {
    title: '',
    type: 'common',
    xAxis: new Array(100).fill('00:00'), //横坐标
    yAxis: [''], //纵坐标
    hasLegend: true,
    data: [
        {
            name: '0-0.5KM',
            data: getRandomInt(100, 0, 500),
            itemStyle: {
                color: 'rgba(56, 178, 136, 1)',
            },
            type: 'line',
            showSymbol: false,
            areaStyle: {
                color: {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 0,
                    y2: 1,
                    colorStops: [
                        {
                            offset: 0,
                            color: 'rgba(56, 178, 136, 0.5)', // 0% 处的颜色
                        },
                        {
                            offset: 0.5,
                            color: 'rgba(56, 178, 136, 0.15)', // 0% 处的颜色
                        },
                        {
                            offset: 1,
                            color: 'rgba(56, 178, 136, 0.03)', // 100% 处的颜色
                        },
                    ],
                    global: false, // 缺省为 false
                },
            },
        },
        {
            name: '0.5-2KM',
            data: getRandomInt(100, 500, 1000),
            itemStyle: {
                color: 'rgba(52, 112, 255, 1)',
            },
            type: 'line',
            showSymbol: false,
            areaStyle: {
                color: {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 0,
                    y2: 1,
                    colorStops: [
                        {
                            offset: 0,
                            color: 'rgba(52, 112, 255, 0.5)', // 0% 处的颜色
                        },
                        {
                            offset: 0.5,
                            color: 'rgba(52, 112, 255, 0.15)', // 0% 处的颜色
                        },
                        {
                            offset: 1,
                            color: 'rgba(52, 112, 255, 0.03)', // 100% 处的颜色
                        },
                    ],
                    global: false, // 缺省为 false
                },
            },
        },
        {
            name: '2KM以上',
            data: getRandomInt(100, 1000, 1500),
            itemStyle: {
                color: 'rgba(26, 159, 244, 1)',
            },
            type: 'line',
            showSymbol: false,
            areaStyle: {
                color: {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 0,
                    y2: 1,
                    colorStops: [
                        {
                            offset: 0,
                            color: 'rgba(26, 159, 244, 0.5)', // 0% 处的颜色
                        },
                        {
                            offset: 0.5,
                            color: 'rgba(26, 159, 244, 0.15)', // 0% 处的颜色
                        },
                        {
                            offset: 1,
                            color: 'rgba(26, 159, 244, 0.03)', // 100% 处的颜色
                        },
                    ],
                    global: false, // 缺省为 false
                },
            },
        },
    ],
};
const ageList = {
    '0-10岁':'age10',
    '10-20岁':'age20',
    '20-30岁':'age30',
    '30-40岁':'age40',
    '40-50岁':'age50',
    '50-60岁':'age60',
    '60-70岁':'age70',
    '70-80岁':'age80',
    '80岁以上':'age80_above',
}
export {
    listDisplay,
    tabList,
    trendAnalysisChart,
    trendAnalysisTable,
    populationTable,
    populationChart,
    ageList,
};
