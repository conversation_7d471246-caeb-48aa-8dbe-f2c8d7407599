const formCols = () => {
    return [
        {
            element: 'el-input',
            prop: 'taskId',
            label: '任务ID',
            attrs: {
                clearable: true,
                placeholder: '请输入任务ID',
            },
            span: 3,
        },
        {
            element: 'el-input',
            prop: 'taskName',
            label: '任务名称',
            attrs: {
                clearable: true,
                placeholder: '请输入任务名称',
            },
            span: 3,
        },
        {
            element: 'el-input',
            prop: 'earthquakeName',
            label: '参考位置',
            attrs: {
                clearable: true,
                placeholder: '请输入参考位置',
            },
            span: 3,
        },
        {
            prop: 'taskCreateTime',
            label: '创建时间',
            element: 'el-date-picker',
            attrs: {
                clearable: true,
                type: 'date',
                placeholder: '请选择创建时间',
                'popper-class':"earth-picker",
                format: 'yyyy-MM-dd',
                'value-format': 'yyyy-MM-dd 00:00:00',
            },
            span: 5,
        },
        { span: 4 },
    ];
};
const tableColumns = [
    {
        prop: 'taskId',
        label: '任务ID',
        width: '80',
    },
    {
        prop: 'taskName',
        label: '任务名称',
        width: '200',
    },
    {
        prop: 'earthquakeName',
        label: '参考位置',
    },
    {
        prop: 'createUserName',
        label: '创建人',
    },
    {
        prop: 'timeType',
        label: '时间粒度',
        width: 120,
    },
    {
        prop: 'executeStartTime',
        label: '执行开始时间',
    },
    {
        prop: 'executeEndTime',
        label: '执行结束时间',
    },
    {
        prop: 'taskCreateTime',
        label: '创建时间',
    },
    {
        prop: 'operation',
        label: '操作',
        width: 230,
    },
];
const detailData = [
    {
        name: '经度（°）：',
        props: 'centerLon',
    },
    {
        name: '纬度（°）：',
        props: 'centerLat',
    },
    {
        name: '震级（M）：',
        props: 'earthquakeLevel',
    },
    {
        name: '深度（千米）：',
        props: 'earthquakeDepth',
    },
    {
        name: '发震时刻（UTC+8）：',
        props: 'occurTime',
    },
];

const timeTypeList = {
    1: '天',
    2: '小时',
    3: '30分钟',
    4: '15分钟',
};

const  defaultSetting = {
    alignment:{
        horizontal: 'center', // 水平居中
        vertical: 'middle',   // 垂直居中
    },
    fill:{
        type: 'pattern',
        pattern:'solid',
        fgColor:{argb:'FFFFFF00'},
        bgColor:{argb:'FF0000FF'}
    },
    border:{
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
    }
};

const snapshotPopulationAnalysis = (options) => {
    const data = {
        name:'快照人口分析',
        mergeCells:['A1:D4','A5:A7','B5:B7','C5:C7','D5:D7'],
        columns:[
            { header:'时间',key:'dataTime',width:20},
            { header:'区域受影响人口',key:'allPopCnt',width:30},
            { header:'topN网格中心经纬度',key:'gridCenterInglat',width:50},
            { header:'网格受影响人口',key:'popCnt',width:50},
        ],
        setCell:{
            'A1':{
                value:options.A1Value,
                alignment:{
                    horizontal: 'center', // 水平居中
                    vertical: 'middle',   // 垂直居中,
                    wrapText: true
                }
            },
            'A5':{
                value:'时间',
                ...defaultSetting,
            },
            'B5':{
                value:'区域受影响人口',
                ...defaultSetting,
            },
            'C5':{
                value:'topN网格中心经纬度',
                ...defaultSetting,
            },
            'D5':{
                value:'网格受影响人口',
                ...defaultSetting,
            }
        },
        addRow:options.addRow,
    };
    return data;
};

const analysisOfMissingPopulation = (options) => {
    const data = {
        name:'失联人口分析',
        mergeCells:['A1:D4','A5:A7','B5:B7','C5:C7','D5:D7'],
        columns:[
            { header:'时间',key:'dataTime',width:20},
            { header:'失联人数总数',key:'allPopCnt',width:30},
            { header:'失联经纬度',key:'gridCenterInglat',width:50},
            { header:'失联人数',key:'popCnt',width:50},
        ],
        setCell:{
            'A1':{
                value:options.A1Value,
                alignment:{
                    horizontal: 'center', // 水平居中
                    vertical: 'middle',   // 垂直居中,
                    wrapText: true
                }
            },
            'A5':{
                value:'时间',
                ...defaultSetting,
            },
            'B5':{
                value:'失联人数总数',
                ...defaultSetting,
            },
            'C5':{
                value:'失联经纬度',
                ...defaultSetting,
            },
            'D5':{
                value:'失联人数',
                ...defaultSetting,
            },
        },
        addRow:options.addRow,
    }
    return data;
};

const downloadOption = (typeList) =>{ 
    const result = [];
    if(typeList.length === 2){
        result.push(snapshotPopulationAnalysis(typeList[0]),analysisOfMissingPopulation(typeList[1]))
    }else if(typeList[0].key === 1){
        result.push(snapshotPopulationAnalysis(typeList[0]));
    }else{
        result.push(analysisOfMissingPopulation(typeList[0]));
    }
    return result;
}

export { formCols, tableColumns, detailData, timeTypeList,downloadOption };
