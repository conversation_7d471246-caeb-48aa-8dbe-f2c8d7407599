<!-- 操作信息 -->
<template>
    <div class="operate-info">
        <div class="operate-info__head">
            <span class="title">
                <span class="title-dot"></span>
                <span>{{ operateInfo.resourceName }}</span>
            </span>
        </div>
        <div class="operate-info__body">
            <el-collapse v-model="activeNames">
                <el-collapse-item name="1">
                    <template slot="title">
                        <img :src="operateInfoIcon" alt="image" />
                        <span class="collapse-title">区域操作信息</span>
                    </template>
                    <ul class="ul">
                        <li v-for="(it, inx) in operateFields" class="item" :key="inx">
                            <span class="name">{{ it.label }}：</span>
                            <span class="value">{{ operateInfo[it.prop] }}</span>
                        </li>
                        <li class="modify-records item">
                            <span class="name">修改记录：</span>
                            <div class="records">
                                <el-steps direction="vertical" :active="-1">
                                    <el-step
                                        v-for="(item, i) in operateInfo.updateLogDetail"
                                        icon="empty"
                                        :key="i"
                                    >
                                        <template #icon>
                                            <span class="dot">●</span>
                                        </template>
                                        <template #title>
                                            <span class="title">{{ item.userName }}</span>
                                        </template>
                                        <template #description>
                                            <span class="time">{{ item.updateTime }}</span>
                                            <el-button
                                                class="check-layer"
                                                size="small"
                                                @click="checkLayer(item)"
                                            >
                                                查看圈画图层
                                            </el-button>
                                        </template>
                                    </el-step>
                                </el-steps>
                            </div>
                        </li>
                    </ul>
                </el-collapse-item>
            </el-collapse>
        </div>
        <!-- 查看圈画图层  -->
        <checkLayer
            v-if="visible"
            :visible.sync="visible"
            :isRestoreRecord.sync="isRestoreRecord"
            :layerInfo="layerInfo"
            :baseInfo="baseInfo"
            :isPrivate="isPrivate"
            @updateRecords="updateRecords"
        />
    </div>
</template>
<script>
import operateInfoIcon from '@/img/space/icons/otherInfo.png';
import { operateFields } from '@/script/constant/resourceManifest.js';
import { getRegions } from '@/utils/method.js';

const mapExamineStatus = {
    1: '通过',
    2: '未通过',
    3: '待审核',
    4: '已取消',
};
export default {
    name: 'operateInfo',
    components: {
        checkLayer: () => import('./checkLayer.vue'),
    },
    props: {
        baseInfo: {
            type: Object,
            default: () => ({}),
        },
        row: {
            type: Object,
            default: () => ({}),
        },
        isPrivate: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            operateInfoIcon,
            operateFields,
            activeNames: ['1'],
            title: '查看圈画信息',
            visible: false,
            layerInfo: {},
            operateInfo: {},
            isRestoreRecord: false,
        };
    },
    watch: {
        visible(val) {
            // 手动关闭弹框时
            if (!val && this.isRestoreRecord) {
                let timer = setTimeout(() => {
                    this.$emit('renderCurLayer');
                    this.isRestoreRecord = false;
                    clearTimeout(timer);
                }, 1000);
            }
        },
    },
    created() {
        this.setOperateInfo(this.row);
    },
    methods: {
        async setOperateInfo(row) {
            if (!row || (!row.resourceId && !row.regionId)) {
                this.$message.warning('regionId不存在');
                return;
            }
            const operateInfo = await this.$post('getUpdateLog', {
                regionId: row.resourceId || row.regionId,
            });
            this.operateInfo = {
                ...operateInfo,
                examineStatus: mapExamineStatus[operateInfo.examineStatus],
                resourceName: row.resourceName,
            };
        },
        checkLayer(item) {
            const curRegionCoors = getRegions(this.baseInfo, this.row.shapeType);
            this.layerInfo = {
                curDate: this.operateInfo.lastUpdateTime,
                curRegionCoors,
                shapeType: this.row.shapeType,
                srcItem: item,
                ...item,
            };
            this.visible = true;
        },
        updateRecords() {
            this.setOperateInfo(this.row);
        },
    },
};
</script>
<style lang="less" scoped>
.operate-info {
    &__head {
        margin: 0 -12px;
        padding: 10px 16px 10px 16px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.08);
        background: rgba(32, 58, 90, 0.6);
        border-radius: 12px 12px 0px 0px;
        backdrop-filter: blur(8px);
        .title {
            width: 208px;
            height: 22px;
            font-size: 16px;
            font-weight: 500;
            color: #ffffff;
            .title-dot {
                display: inline-block;
                width: 8px;
                height: 8px;
                background: #53ffff;
                border-radius: 4px;
                vertical-align: middle;
                margin-right: 8px;
            }
        }
    }
    &__body {
        padding: 0 4px;
        .el-collapse {
            border: none;
            .collapse-title {
                margin-left: 8px;
                font-size: 14px;
                font-weight: bold;
            }
            .ul {
                padding-top: 6px;
                .item {
                    line-height: 28px;
                    font-size: 14px;
                }
                .modify-records {
                    .records {
                        padding: 12px;
                        max-height: 352px;
                        overflow: auto;
                        border-radius: 6px;
                        .dot {
                            color: #53ffff;
                        }
                        .title {
                            color: #fff;
                            font-size: 14px;
                        }
                        .time {
                            color: #d1e4ff;
                        }
                        .check-layer {
                            position: absolute;
                            top: 50%;
                            transform: translateY(-50%);
                            right: 8px;
                            background: unset;
                            border: none;
                            color: #53ffff;
                        }
                    }
                    .el-step {
                        /deep/ .el-step__icon {
                            background-color: rgba(255, 255, 255, 0.3);
                        }
                        /deep/.el-step__line {
                            background-color: #fff;
                            opacity: 0.3;
                        }
                    }
                }
            }
            /deep/ .el-collapse-item__header {
                color: #ffffff;
                background: transparent;
                border-bottom-color: #5d636c;
            }
            /deep/ .el-collapse-item__header.is-active {
                border-bottom-color: #5d636c;
            }
            /deep/.el-collapse-item__wrap {
                background: transparent;
                border: none;
            }
            /deep/ .el-collapse-item__content {
                padding-bottom: 0;
                .item {
                    .name {
                        color: #d1e4ff;
                    }
                    .value {
                        color: #ffffff;
                    }
                }
            }
        }
    }
}
</style>
