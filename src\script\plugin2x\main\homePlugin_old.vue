<template>
    <div class="home-wrapper">
        <div class="home-wrapper-top earth-shadow">
            <titleBar title="地震记录" />
            <div class="content earth-flex-row">
                <div class="list">
                    <searchBar class="search-form" :fields="formCols" :form="form">
                        <template>
                            <el-button
                                class="earth-btn-common"
                                type="primary"
                                size="small"
                                @click="search()"
                                >查询</el-button
                            >
                        </template>
                    </searchBar>
                    <dataTable
                        class="data-table"
                        :columns="columns"
                        :data="tableData"
                        :pagination="paginationData"
                        :total="total"
                        :updateTable="getTableData"
                        @row-click="rowClick"
                    >
                    </dataTable>
                </div>
                <div class="list">
                    <mapEcharts ref="mapEcharts" :data="mapData" />
                </div>
            </div>
        </div>
        <div class="home-wrapper-bottom earth-flex-row">
            <div class="card left earth-shadow">
                <titleBar title="快捷入口" />
                <div class="content earth-flex-row-com">
                    <div
                        class="earth-flex-row-com width-3 earth-cursor"
                        v-for="(item, index) in quickEntrance"
                        :key="index"
                        @click="quickEntranceClick(item)"
                    >
                        <img :src="require(`../../../img/home/<USER>" alt="" />
                        <span class="quick-text earth-bold-text">{{ item.name }}</span>
                    </div>
                </div>
            </div>
            <div class="card right earth-shadow">
                <titleBar title="平台概览">
                    <span class="overview-text" slot="right">【统计总计为最近一个月】</span>
                </titleBar>
                <div class="content earth-flex-row">
                    <div class="overview">
                        <div
                            class="overview-item earth-cursor"
                            :style="{
                                background: `url(${
                                    backgroundList[index + 1]
                                }) center center / 100% 100% no-repeat`,
                            }"
                            v-for="(item, index) in platformOverview"
                            :key="index"
                        >
                            <div class="data">
                                <span class="count">{{ item.count }} {{ item.unit }}</span>
                                <span class="text">{{ item.name }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import titleBar from '_com/titleBar/index.vue';
import dataTable from '_com/table/dataTable.vue';
import mapEcharts from '_com/echarts/mapEcharts.vue';
import searchBar from '_com/searchBar/searchBar.vue';
import { tableColumn, quickEntrance, platformOverview, formCols } from '_const/home.js';
const dayjs = require('dayjs');
import { dataList } from './test.js';
export default {
    name: 'home',
    components: {
        titleBar,
        dataTable,
        mapEcharts,
        searchBar,
    },
    data() {
        return {
            formCols: formCols(),
            form: {
                earthquakeName: '',
                earthquakeDepthLevel: '',
                earthquakeScaleLevel: '',
                time: [],
            },
            columns: tableColumn,
            tableData: [],
            paginationData: {
                curPage: 1,
                pageSize: 20,
            },
            total: 0,
            quickEntrance: quickEntrance, //快捷入口
            platformOverview: JSON.parse(JSON.stringify(platformOverview)), //平台概览
            backgroundList: {
                1: require('../../../img/home/<USER>'),
                2: require('../../../img/home/<USER>'),
                3: require('../../../img/home/<USER>'),
            },
            mapData: [],
        };
    },
    mounted() {
        // 窗口大小变化
        window.onresize = () => {
            this.$refs.mapEcharts.resize();
        };
        this.getOverview();
        this.search();
    },
    methods: {
        search() {
            this.paginationData.curPage = 1;
            this.getTableData({ curPage: 1 });
        },
        async getTableData(paginationData = {}) {
            const { curPage = 1, pageSize = 20 } = paginationData;
            const { earthquakeName, earthquakeDepthLevel, earthquakeScaleLevel, time } = this.form;
            let params = {
                pageSize,
                currentPage: curPage,
                burialPepTaskPara: {
                    earthquakeName,
                    earthquakeDepthLevel,
                    earthquakeScaleLevel,
                },
            };
            if (time && time[1]) {
                params.endTime = time && time[1];
            }
            if (time && time[0]) {
                params.startTime = time && time[0];
            }
            this.getPost('post', 'getPage', params, '获取地震列表信息', (res) => {
                this.total = res.totalPageNum;
                this.tableData = res.earthquakeList;
                this.mapData = this.tableData.map((item) => {
                    return {
                        name: item.earthquakeName,
                        value: [
                            Number(item.centerLon),
                            Number(item.centerLat),
                            Number(item.earthquakeLevel),
                        ],
                    };
                });
            });
        },
        getOverview() {
            this.getPost(
                'post',
                'getOverview',
                {
                    startTime: dayjs().subtract(1, 'month').format('YYYY-MM-DD 00:00:00'),
                    endTime: dayjs().format('YYYY-MM-DD 00:00:00'),
                },
                '获取平台概览信息',
                (res) => {
                    this.platformOverview.forEach((item) => {
                        item.count = res[item.props] || '-';
                    });
                }
            );
        },
        quickEntranceClick(item) {
            this.$router.push({
                name: item.value,
            });
        },
        rowClick(row) {
            this.$router.push({
                name: 'creation',
                params: row,
            });
        },
    },
};
</script>

<style lang="less" scoped>
.home-wrapper {
    width: 100%;
    height: 100%;
    padding: @interval-size-small;
    &-top {
        width: 100%;
        height: calc(100% - 13.44rem);
        .list {
            width: 50%;
            height: 100%;
        }
        .list + .list {
            margin-left: @interval-size-small;
        }
    }
    &-bottom {
        width: 100%;
        height: 12.61rem;
        margin-top: @interval-size-small;
        .card {
            height: 100%;
        }
        .left {
            width: 50%;
        }
        .right {
            width: 50%;
        }
        .card + .card {
            margin-left: @interval-size-small;
        }
    }
    .content {
        width: 100%;
        height: calc(100% - 2.78rem);
        padding: @interval-size-mini;
        .search-form {
            width: 100%;
            height: 5rem;
        }
        .data-table {
            height: calc(100% - 5.56rem);
            margin-top: @interval-size-mini;
        }
        .width-3 {
            width: 33%;
            height: 100%;
            background: linear-gradient(180deg, #c3defd 0%, #edf4ff 100%);
            border-radius: 0.22rem;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            position: relative;
            &:hover {
                opacity: 0.8;
            }
            img {
                width: 7.5rem;
                height: 7.5rem;
            }
            .quick-text {
                position: absolute;
                bottom: 20px;
                text-align: center;
            }
        }
        .width-3 + .width-3 {
            margin-left: @interval-size-mini;
        }
        .overview {
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: space-between;
        }
        .overview-item {
            display: flex;
            height: 100%;
            width: 17.94rem;
            padding: @interval-size-mini;
            &:hover {
                opacity: 0.8;
            }
            .image {
                border: 0.06rem dashed #cccccc;
            }
            .data {
                margin-left: 0.67rem;
                flex: 1;
                width: 0;
                display: flex;
                flex-direction: column;
                justify-content: space-around;
                .count {
                    font-weight: bold;
                    font-size: 1.39rem;
                    color: @white;
                    text-align: left;
                    font-family: Helvetica, Helvetica;
                }
                .text {
                    display: inline-block;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    cursor: pointer;
                    font-weight: 400;
                    font-size: 0.94rem;
                    color: @white;
                }
            }
        }
        .overview-item + .overview-item {
            margin-left: @interval-size-mini;
        }
    }
}
.overview-text {
    color: rgba(0, 0, 0, 0.45);
    font-size: 16px;
}
</style>
