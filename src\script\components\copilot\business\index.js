/*
 * @Date: 2025-08-05 20:41:09
 * @LastEditors: liurong <EMAIL>
 * @LastEditTime: 2025-08-06 08:51:53
 * @FilePath: \mtex-static-knowledgebased:\project\earthquake\src\script\components\copilot\business\index.js
 */
export default {
  createEarthInfo: {
    // 移除静态content
    component: () => import('./queryEarthInfo.vue'),
    nextStep: 'createAnalysisTask',
  },
  createAnalysisTask: {
    // 移除静态content
    component: () => import('./createTask.vue'),
    nextStep: 'createTaskInfo',
  },
  // 可以添加更多业务组件配置
/*   createTaskInfo: {
    // 移除静态content
    component: () => import('../business/taskInfo.vue'),
    nextStep: null, // 如果是最后一步，可以设为null
  } */
}