<template>
    <el-dialog
        class="bulk-edit"
        top="250px"
        width="489px"
        append-to-body
        :visible="visible"
        :close-on-click-modal="false"
        @open="handleOpen"
        @close="handleClose"
    >
        <template #title>
            <div class="bulk-edit__title">
                <span class="title">批量编辑</span>
            </div>
        </template>
        <div class="bulk-edit__body">
            <div class="form">
                <div class="tip">
                    <i class="el-icon-warning"></i>
                    您确定需要批量编辑选中的{{ checkCount }}个资源的图层类型吗？
                </div>
                <div class="item">
                    <span class="name">请选择修改的图层类型：</span>
                    <el-select
                        class="custom-select"
                        v-model="layerIdList"
                        size="small"
                        placeholder="图层类型（多选）"
                        multiple
                    >
                        <el-option v-for="(item, inx) in spaceLayers" :key="inx" v-bind="item" />
                    </el-select>
                </div>
            </div>
        </div>
        <!-- 底部 -->
        <div slot="footer" class="dialog-footer">
            <el-button size="small" @click="handleClose">取消</el-button>
            <el-button type="primary" size="small" @click="save">保存</el-button>
        </div>
    </el-dialog>
</template>
<script>
export default {
    name: 'bulk-edit',
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        selectList: {
            type: Array,
            default: () => ({}),
        },
    },
    data() {
        return {
            isShareCode: false,
            layerIdList: [],
        };
    },
    computed: {
        spaceLayers() {
            return this.$store.getters.spaceLayers;
        },
        checkCount() {
            return this.selectList.length;
        },
    },
    mounted() {},
    methods: {
        handleOpen() {},
        handleClose() {
            this.$emit('update:visible', false);
        },
        async save() {
            const res = await this.$post(
                'updateRegionByBatch',
                {
                    regionIdList: this.selectList.map((item) => item.resourceId),
                    layerIdList: this.layerIdList,
                },
                true
            );
            if (res.serviceFlag === 'TRUE') {
                this.$message.success(res.returnMsg);
                this.handleClose();
                this.$emit('updateTable');
            }
        },
    },
};
</script>

<style lang="less" scoped>
.bulk-edit {
    &__title {
        display: flex;
        align-items: center;
        .title {
            font-size: 18px;
            font-family: AlibabaPuHuiTi, AlibabaPuHuiTi;
            font-weight: 600;
            color: #fff;
            line-height: 22px;
            margin-right: 10px;
        }

        img {
            margin-right: 5px;
        }

        .subTitle {
            font-family: AlibabaPuHuiTi, AlibabaPuHuiTi;
            font-weight: 400;
            font-size: 12px;
            color: #666666;
            line-height: 16px;
        }
    }
    &__body {
        .form {
            .item {
                display: flex;
                align-items: center;
                height: 32px;
                .name {
                    display: inline-block;
                    width: 157px;
                    font-family: AlibabaPuHuiTi, AlibabaPuHuiTi;
                    font-weight: 400;
                    font-size: 14px;
                    color: rgba(255, 255, 255, 0.85);
                }
                .custom-select {
                    flex: 1;
                    /deep/ .el-input__inner {
                        background: rgba(0, 20, 46, 0.3);
                        box-shadow: inset 0px 0px 8px 0px #4984ff;
                        border-radius: 4px;
                        border: 1px solid #a8bfe8;
                    }
                }
            }
            .tip {
                display: flex;
                align-items: center;
                margin-bottom: 20px;
                line-height: 32px;
                font-weight: 400;
                font-size: 14px;
                color: rgba(255, 255, 0255, 0.65);
                font-family: AlibabaPuHuiTi, AlibabaPuHuiTi;
                background: linear-gradient(
                    180deg,
                    rgba(0, 145, 255, 0.1) 0%,
                    rgba(0, 145, 255, 0.25) 100%
                );
                border-radius: 4px;
                i {
                    margin: 0 4px 0 10px;
                    color: #0091ff;
                }
            }
        }
    }
    /deep/ .el-dialog {
        box-shadow: 0px 6px 30px 0px rgba(0, 0, 0, 0.05), 0px 16px 24px 2px rgba(0, 0, 0, 0.04),
            0px 8px 10px -5px rgba(0, 0, 0, 0.08);
        border-radius: 10px;
        background: rgba(31, 52, 79, 0.8);
        box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.65);
        border-radius: 8px;
        backdrop-filter: blur(8px);
    }
    /deep/ .el-dialog__header {
        color: #fff;
        padding: 16px 20px 16px;
        border-bottom: 1px solid rgba(255, 255, 255, .3);
    }
    /deep/ .el-dialog__body {
        padding: 20px 20px 8px 20px;
    }
    /deep/ .el-dialog__footer {
        padding: 12px 16px 18px;
    }
}
</style>
