const timeRangeList = {
    1: {
        type: 'daterange',
        'start-placeholder': '开始日期',
        'end-placeholder': '结束日期',
        format: 'yyyy-MM-dd',
        'value-format': 'yyyy-MM-dd 00:00:00',
    },
    2: {
        type: 'datetimerange',
        'start-placeholder': '开始时间',
        'end-placeholder': '结束时间',
        format: 'yyyy-MM-dd HH',
        'value-format': 'yyyy-MM-dd HH:00:00',
    },
    3: {
        type: 'datetimerange',
        'start-placeholder': '开始时间',
        'end-placeholder': '结束时间',
        format: 'yyyy-MM-dd HH:mm',
        'value-format': 'yyyy-MM-dd HH:mm:00',
    },
    4: {
        type: 'datetimerange',
        'start-placeholder': '开始时间',
        'end-placeholder': '结束时间',
        format: 'yyyy-MM-dd HH:mm',
        'value-format': 'yyyy-MM-dd HH:mm:00',
    },
};

const creationFormCols = (timeType, handler = {}) => {
    const timeConfig = timeRangeList[timeType || 4];
    const list = [
        {
            element: 'el-input',
            prop: 'taskName',
            label: '任务名称',
            labelWidth: '93px',
            attrs: {
                clearable: true,
                placeholder: '请输入',
            },
            span: 24,
            rules: [{ required: true, message: '请输入任务名称！', trigger: 'blur' }],
        },
        {
            element: 'el-input',
            prop: 'regionName',
            label: '区域名称',
            labelWidth: '93px',
            attrs: {
                disabled: true,
                placeholder: '',
            },
            span: 24,
        },
        {
            prop: 'timeType',
            label: '时间粒度',
            labelWidth: '93px',
            attrs: {
                clearable: true,
                placeholder: '请选择',
                popperClass: 'el-select-dropdown',
            },
            element: 'el-select',
            slot: {
                element: 'el-option',
                enums: [
                    { label: '天', value: 1 },
                    { label: '小时', value: 2 },
                    { label: '30分钟', value: 3 },
                    { label: '15分钟', value: 4 },
                ],
            },
            listeners: {
                change: handler.timeType,
            },
            span: 24,
            rules: [{ required: true, message: '请选择时间粒度！', trigger: ['blur', 'change'] }],
        },
        {
            prop: 'executionTime',
            label: '时间选择',
            labelWidth: '93px',
            element: 'el-date-picker',
            attrs: Object.assign(
                {
                    clearable: true,
                    'range-separator': '-',
                    type: 'datetimerange',
                    'start-placeholder': '开始时间',
                    'end-placeholder': '结束时间',
                    'popper-class':"earth-picker",
                    format: 'yyyy-MM-dd HH:mm',
                    'picker-options': {
                        selectableRange: ['00:00', '23:45'],
                        step: '00:15',
                    },
                    'value-format': 'yyyy-MM-dd HH:mm:00',
                },
                timeConfig
            ),
            listeners: { change: handler.executionTime },
            span: 24,
            rules: [{ required: true, message: '请选择时间！', trigger: ['blur', 'change'] }],
        },
        {
            prop: 'disasterType',
            label: '灾害类型',
            labelWidth: '93px',
            element: 'el-select',
            attrs: {
                clearable: true,
                placeholder: '请选择',
            },
            slot: {
                element: 'el-option',
                enums: [
                    { label: '地震', value: 1 },
                    { label: '泥石流', value: 2 },
                    { label: '水灾', value: 3 },
                    { label: '山体滑坡', value: 4 },
                    { label: '火灾', value: 5 },
                    { label: '其他', value: 6 },
                ],
            },
            span: 24,
            rules: [{ required: true, message: '请选择灾害类型！', trigger: 'blur' }],
        },
        {
            prop: 'heatMapType',
            label: '热力类型',
            labelWidth: '93px',
            element: 'el-select',
            attrs: {
                clearable: true,
                placeholder: '请选择',
            },
            slot: {
                element: 'el-option',
                enums: [
                    { label: '网格热力', value: 1 },
                    { label: '图层热力', value: 2 },
                ],
            },
            span: 24,
            rules: [{ required: true, message: '请选择热力类型！', trigger: 'blur' }],
        },
        {
            prop: 'continuousAnalysis',
            label: '是否持续分析',
            labelWidth: '120px',
            element: 'el-select',
            attrs: {
                clearable: true,
                placeholder: '请选择',
            },
            slot: {
                element: 'el-option',
                enums: [
                    { label: '否', value: 0 },
                    { label: '是', value: 1 },
                ],
            },
            span: 24,
            rules: [{ required: true, message: '请选择！', trigger: 'blur' }],
        },
        {
            prop: 'populationTargetId',
            label: '对象选择',
            labelWidth: '93px',
            attrs: {
                placeholder: '请选择',
            },
            element: 'el-checkbox-group',
            slot: {
                element: 'el-checkbox',
                enums: [
                    { text: '受影响人群', label: '1' },
                    { text: '疑似失联人群', label: '2' },
                    { text: '掩埋人群', label: '3' },
                ],
            },
            span: 24,
            rules: [{ required: true, message: '请选择对象！', trigger: ['blur', 'change'] }],
        },
        {
            prop: 'occurTime',
            label: '事件发生时间',
            labelWidth: '114px',
            element: 'el-date-picker',
            attrs: {
                clearable: true,
                type: 'datetime',
                'popper-class': 'earth-picker',
                format: 'yyyy-MM-dd HH:mm:ss',
                'value-format': 'yyyy-MM-dd HH:mm:ss',
                placeholder: '请选择',
            },
            span: 24,
        },
        { span: 24 },
    ];
    return list.filter((item) => item.isShow !== false);
};

export { creationFormCols };
