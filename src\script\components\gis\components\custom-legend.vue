<script>
const list = [
  { color: '#57782C', text: '畅通', className: 'w-56' },
  { color: '#ADCA87', text: '基本畅通' },
  { color: '#CEC101', text: '轻度拥堵' },
  { color: '#EE9E01', text: '拥堵', className: 'w-56' },
  { color: '#FE1707', text: '严重拥堵' },
];
export default {
  functional: true,
  name: 'custom-legend',
  render(h) {
    return h('div', { class: 'map-legend' }, [
      h(
        'ul',
        { class: 'list' },
        list.map((item) => {
          return h('li', { class: `item ${item.className}` }, [
            h('span', { class: 'dot', style: { 'background-color': item.color } }),
            h('span', { class: 'text' }, item.text),
          ]);
        })
      ),
    ]);
  },
};
</script>

<style lang="less" scoped>
.map-legend {
  position: absolute;
  padding: 8px 0 8px 10px;
  right: 10px;
  bottom: 10px;
  border-radius: 6px;
  background-color: rgba(255, 255, 255, 0.3); /* 半透明背景 */
  backdrop-filter: blur(10px); /* 模糊背景 */
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); /* 可选，增加阴影效果 */
  z-index: 3;
  .list {
    display: flex;
    flex-wrap: wrap;
    margin: 0;
    padding: 0;
    width: 225px;
    .item {
      display: flex;
      align-items: center;
      width: 84px;
      list-style: none;
      .dot {
        display: inline-block;
        margin-right: 6px;
        width: 14px;
        height: 14px;
        border-radius: 7px;
      }
      .text {
        font-size: 14px;
      }
      &:nth-child(-n + 3) {
        margin-bottom: 3px;
      }
      &.w-56 {
        width: 56px;
      }
    }
  }
}
</style>
