<template functional>
    <div class="tool-box">
        <el-dropdown @command="listeners.clickTool" trigger="click" style="outline: none">
            <span class="btn">
                <img :src="props.imgList.tool" alt="" srcset="" />
                工具箱
                <i class="el-icon-arrow-down el-icon--right"></i>
            </span>
            <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="lineLayer" title="测距">
                    <img :src="props.imgList.line" alt="" srcset="" />
                </el-dropdown-item>
                <el-dropdown-item command="clean" title="清除">
                    <img :src="props.imgList.clean" alt="" srcset="" />
                </el-dropdown-item>
            </el-dropdown-menu>
        </el-dropdown>
    </div>
</template>
<script>
export default {
    name: 'tool-box',
    props: {
        imgList: {
            type: Object,
        },
    },
};
</script>
<style lang="less" scoped>
.tool-box {
    display: inline-flex;
    padding: 0 10px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    color: #fff;
    font-weight: 500;
    font-size: 16px;
    cursor: pointer;
    border-radius: 4px;
    background-color: #1f3459;
    img {
        width: 24px;
        height: 24px;
        display: inline-block;
        margin-right: 4px;
        vertical-align: middle;
    }
    .btn {
        color: #fff;
        font-weight: 500;
        font-size: 16px;
    }
    .el-dropdown {
        .el-dropdown-menu {
            background-color: #101d34 !important;
            border: 1px solid #101d34;
        }
        .el-dropdown-menu__item {
            padding: 0 5px;
            background-color: #101d34 !important;
            color: #fff;
            &:hover {
                background-color: #1f3459 !important;
                color: #fff;
            }
        }
    }
}
</style>
<style lang="less">
.el-dropdown-menu {
    background-color: #101d34 !important;
    border: 1px solid #101d34;
    .el-dropdown-menu__item {
        background-color: #101d34 !important;
        color: #fff;
        &:hover {
            background-color: #1f3459 !important;
            color: #fff;
        }
    }
    .popper__arrow {
        &::after {
          border-bottom-color: #101d34!important;
        }
    }
}
</style>
