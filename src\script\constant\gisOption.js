import boxSelect from '@/img/space/gis/boxSelect.png';
import drawCircle from '@/img/space/gis/drawCircle.png';
import locatePoint from '@/img/space/gis/locate.png';
import line from '@/img/space/gis/road.png';
import fusionPolygon from '@/img/space/gis/fusionPolygon.png';
import fusionCircle from '@/img/space/gis/fusionCircle.png';
import fusionLine from '@/img/space/gis/fusionLine.png';
import fusionLocate from '@/img/space/gis/fusionLocate.png';

const gisOptions = () => {
    return {
        antialias: true,
        /* mapLayer: {
                visible: true,
                mapType,
            },
            amapMapLayer: true, */
        areaMap: true,
        baseMapConfig: true,
        preserveDrawingBuffer: true,
        divLayer: true,
        cameraControl: {
            type: '2d',
            minZoom: 4,
            maxZoom: 17,
            wheelLevelChange: true,
        },
        //  showInfo: {
        //      visible: true,
        //      float: true,
        //  },
        tool: {
            searchVisible: false,
            baiduSearch: false,
            buttonVisible: false,
            toolBoxVisible: false, //工具箱 不显示
            layerVisible: false,
            baiduReverseGeocoding: false,
            circleChoice: false,
            boxSelectLayer: true,
        },
        lineLayer: true, //线条
        panoramaLayer: {
            visible: false,
        },
        circleChoice: {
            //圈选
            circleColor: 0x4f2cde,
            circleOpacity: 0.5,
            circleFrame: true,
            circleFrameColor: 0x734ce3,
            circleShowRadius: true, // 该属性会导致辅助圆出现半径巨大的情况
            cirCleShowClose: true,
        },
        boxSelectLayer: true,
        lineEditLayer: true,
        LacCiNumberTimer: null,
        areaEditLayer: true,
    };
};

const menuList = [
    {
        prop: 'outwardRadiate',
        label: '向外辐射',
    },
    {
        prop: 'cancelRadiation',
        label: '取消辐射',
    },
    {
        prop: 'hole',
        label: '设置空洞',
    },
    {
        prop: 'auxiliaryCircle',
        label: '画辅助圆',
    },
    {
        prop: 'delHole',
        label: '删除',
    },
    {
        prop: 'delCircle',
        label: '删除',
    },
    {
        prop: 'delPolygon',
        label: '删除区域',
    },
    {
        prop: 'delCircular',
        label: '删除区域',
    },
];

const shapeList = {
    1: {
        prop: 'circular',
        label: '圆形',
        img: drawCircle,
        fusionImg: fusionCircle,
    },
    2: {
        prop: 'areaEdit',
        label: '多边形',
        img: boxSelect,
        fusionImg: fusionPolygon,
    },
    4: {
        prop: 'line',
        label: '线段',
        img: line,
        fusionImg: fusionLine,
    },
    5: {
        prop: 'locatePoint',
        label: '位置点',
        img: locatePoint,
        fusionImg: fusionLocate,
    },
};
const mapOpts = [{ value: '普通地图' }, { value: '卫星地图' }, { value: '路况地图' }];
const mapRoadResult = {
    畅通: 0x57782c,
    基本畅通: 0xadca87,
    轻度拥堵: 0xcec101,
    拥堵: 0xee9e01,
    严重拥堵: 0xfe1707,
};
export { gisOptions, menuList, shapeList, mapOpts, mapRoadResult };
