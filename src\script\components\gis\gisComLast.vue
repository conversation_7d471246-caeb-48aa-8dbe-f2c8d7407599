<template>
    <div v-loading="gisLoading" class="gis-com">

        <div ref="spaceGis" id="space-gis" class="earth-h-full"></div>

        <div class="gis-com__tools">
            <!-- 搜索 -->
            <searchPlace
                v-if="showPanel"
                :city="city"
                :innerCity="innerCity"
                :gisCenterMove="gisCenterMove"
                :getMayType="getMayType"
            />
            <!-- 地市 -->
            <selectDistrict
                v-if="isShowInnerCity"
                v-model="innerCity"
                class="select-district"
                :options="districts"
                :props="{
                    multiple: false,
                    label: 'label',
                    value: 'value',
                    children: 'children',
                }"
                placeholder="请选择地市"
                size="medium"
            />
            <!-- 工具箱 && 轮廓类型 && 输入坐标 -->
            <div v-if="isAllowChangeLayer" class="toolbar">
                <toolbox
                    v-if="isShowTool"
                    :imgList="imgList"
                    @clickTool="clickTool"
                />
                <div v-show="isAllowChangeLayer" class="gis-tool">
                    <div class="btn-box radius-left" @click="drawAreas(curArea.prop)">
                        <img :src="curArea.img" alt="" srcset="" />{{
                            curArea.label
                        }}
                    </div>
                    <entryRegion
                        ref="entryRegion"
                        v-model="areaPopoverVisible"
                        class="radius-left"
                        :shapeType="shapeType"
                        :isSingleRegion="isStreetManage"
                        @setAreaCoors="setAreaCoors"
                    />
                </div>
            </div>
            <!-- 隐藏基站 && 基站管理 -->
            <div
                v-if="!isLocatePoint && (isShowHideManage || isShowBaseStationManage)"
                class="gis-tool shadow"
            >
                <div v-if="isShowHideManage" class="btn-box radius-left" @click="viewBaseStation()">
                    <img :src="getBaseIcon" style="margin-right: 2px" />
                    {{ getCheckBaseLabel }}
                </div>
                <div
                    v-if="isShowBaseStationManage"
                    class="btn-box radius-right"
                    @click="baseManage"
                >
                    <img
                        :src="imgList.baseStationManage"
                    />基站管理
                </div>
            </div>
        </div>
        <!-- 资源 -->
        <layerResource
            v-if="isShowLayers"
            ref="layerResource"
            :curType="curType"
            :planeObjs="planeObjs"
            :shapeType="shapeType"
            v-bind="$attrs"
            @setLocation="setLocation"
        />
        <!-- 右键菜单 -->
        <rightMenu
            :isShow.sync="isShowMenu"
            :menuList="menuList"
            @selectType="(prop) => layerObj.selectMenu(prop)"
        />
        <!-- 右键辐射菜单 -->
        <radiationRange
            ref="radiationRangeRef"
            :isShow.sync="isShowRadiation"
            :coordinate="coordinate"
            @sure="(distance) => layerObj.updateRadiation(distance)"
        />
        <!-- 基站管理 -->
        <baseManage
            v-if="isShowBaseManage"
            :drawer.sync="isShowBaseManage"
            :baseStations="allBaseStations"
            @getAddedStations="getAddedStations"
            @setLocation="setLocation"
            @handleClose="setBasePoints"
        />
        <!-- legend -->
        <customLegend v-if="isRoadConditionMap" />

        <div class="tip">已获得高德地图的地图服务使用授权</div>
    </div>
</template>
<script>
import layerResource from './layerResource/index.vue';
import imgList from './imgList';
import entryRegion from './components/entryRegion.vue';
import * as gisUtils from '@/utils/gisUtils';
import {
    getAreasToCity,
    removeDuplicateLocations,
    formatEnterAreas,
    toPointSequence,
    compareSort,
    getRegions,
    getCoordinateExtremum,
} from '@/utils/method.js';
import { gisOptions, menuList, shapeList, mapOpts } from '@/script/constant/gisOption.js';
import createPolygon from './sharps/polygonInstance.js';
import createCircular from './sharps/circleInstance.js';
import createLocatePoint from './sharps/locationPoint.js';
import createLine from './sharps/line.js';
import addedIcon from '@/img/space/gis/basePoint.png';
import noAddIcon from '@/img/space/gis/noAdd.png';
import activeIcon from '@/img/space/gis/activeBase.png';
import airportPoint from '@/img/space/gis/airportPoint.png';
const { GIS } = window.MTGIS;
import { Decrypt } from '@/utils/encrypted.js';
export default {
    name: 'gis-com',
    components: {
        layerResource,
        entryRegion,
        selectDistrict: () => import('../selectDistrict.vue'),
        rightMenu: () => import('@/script/components/rightMenu.vue'),
        radiationRange: () => import('@/script/components/radiationRange.vue'),
        baseManage: () => import('@/script/components/gis/baseManage/index.vue'),
        searchPlace: () => import('./components/searchPlace.vue'),
        toolbox: () => import('./components/toolbox.vue'),
        customLegend: () => import('./components/custom-legend.vue'),
    },
    provide() {
        return {
            root: this,
            setTabName: (tabName) => {
                this.tabName = tabName;
            },
            setAirportPoints: (airportBasePoints) => {
                this.airportBasePoints = airportBasePoints;
            },
        };
    },
    props: {
        // 展示搜索框
        showPanel: {
            type: Boolean,
            default: true,
        },
        curType: {
            type: String,
        },
        city: {
            type: Array,
            default: () => [],
        },
        showExtend: {
            type: Boolean,
            default: false,
        },
        isShowTool: {
            type: Boolean,
            default: false,
        },
        showResourcesData: {
            type: Boolean,
            default: false,
        },
        // 区块数据
        gisData: {
            type: Object,
            default: () => ({}),
        },
        // 区域数据
        regionCoors: {
            type: Array,
            default: () => [],
        },
        // 资源数据
        resourcesData: {
            type: Array,
            default: () => [],
        },
        isShowLayers: {
            type: Boolean,
            default: true,
        },
        isShowInnerCity: {
            type: Boolean,
            default: false,
        },
        isJustShowLayer: {
            type: Boolean,
            default: false,
        },
        isShowHideManage: {
            type: Boolean,
            default: false,
        },
        isShowBaseStationManage: {
            type: Boolean,
            default: false,
        },
        isResPackage: {
            type: Boolean,
            default: false,
        },
        isCreated: {
            type: Boolean,
            default: true,
        },
        isStreetManage: {
            type: Boolean,
            default: false,
        },
        baseInfo: {
            type: Object,
            default: () => ({}),
        },
        defExpansion: {
            type: Number,
            default: 0,
        },
        // 多边形或者圆形，默认多边形
        shapeType: {
            type: Number,
            default: 2,
        },
        initRegions: {
            type: Function,
            default: () => {},
        },
        roadLength: {
            // 线路类型特有属性
            type: [Number, String],
            default: 0,
        },
        expand: {
            type: [Number, String],
            default: 1,
        },
        gisId: {
            type: String,
            default: 'refs_rams_gis_api_config',
        },
    },
    data() {
        return {
            addedIcon,
            noAddIcon,
            activeIcon, // todo，待定
            isCollapse: false,
            areaPopoverVisible: false,
            imgList,
            innerCity: [],
            menuList,
            isShowMenu: false,
            isShowRadiation: false,
            isShowBaseManage: false,
            baseStations: [],
            allBaseStations: [],
            Polygon: createPolygon(),
            Circular: createCircular(),
            LocatePoint: createLocatePoint(),
            Line: createLine(),
            planeObjs: [],
            layerObj: {},
            circleChoices: {},
            curCircleChoiceObj: {},
            coordinate: {},
            isCheckBase: false,
            addStations: [],
            multiCreatedStations: [], // 已创建的复合区域的区域内基站
            circleType: '', //圆形圈选类型 'circular'圆 'circleChoice'辅助圆
            entryHoleList: [],
            isManualEntry: false,
            airportBasePoints: [],
            tabName: '',
            mapType: '普通地图',
            gisLoading: false,
        };
    },
    computed: {
        curCityPath({ city }) {
            if (city && city.length) {
                if (Array.isArray(city[0])) {
                    return city[0].slice(0, 2).join(',');
                }
                return city.filter(Boolean).join(',');
            }
            return '';
        },
        isAllowChangeLayer() {
            return ['singleEntry'].includes(this.curType);
        },
        districts() {
            return getAreasToCity(this.$store.getters.spaceDistricts);
        },
        getCheckBaseLabel() {
            return this.isCheckBase ? '隐藏基站' : '查看基站';
        },
        getBaseIcon() {
            return this.isCheckBase ? imgList.hideManage : imgList.checkBase;
        },
        // 是否为已创建的复合区域
        isMultiRegion() {
            return Boolean(
                this.baseInfo.multiPolygonList && this.baseInfo.multiPolygonList.length > 1
            );
        },
        isPolygon() {
            return this.shapeType === 2;
        },
        isLine() {
            return this.shapeType === 4;
        },
        isLocatePoint() {
            return this.shapeType === 5;
        },
        isRoadConditionMap() {
            return this.mapType === '路况地图';
        },
        curArea() {
            return shapeList[this.shapeType];
        },
    },
    watch: {
        curCityPath: {
            async handler(newPath) {
                const areas = newPath.split(',');
                const city = areas[1];
                if (!city) return;
                const code = areas[areas.length - 1];
                const mapType = ['province', 'city', 'district'];
                const result = await this.$post('getCenter', {
                    type: mapType[areas.length - 1],
                    code,
                });
                const { latitude, longitude } = result;

                if (this.g) {
                    this.g.cameraControl.gradualChangeRadius({
                        targetPoint: {
                            lat: latitude,
                            lng: longitude,
                        },
                        zoom: 10,
                    });
                }
            },
        },
        shapeType: {
            handler(newV) {
                // 切换轮廓类型，清空数据
                if (this.isResPackage) return;
                this.clearAll(true);
                this.DrawArea = this.getDrawArea(newV);
                this.DrawArea.initGisInfo(this.g, this);
            },
        },
    },
    created() {
        this.DrawArea = this.Polygon;
        this.DrawAreaSet = [this.Polygon, this.Circular, this.Line, this.LocatePoint];
        this.gisLoading = true;
    },
    activated() {
        this.resizeGis();
    },
    mounted() {
        this.initGis();
        window.addEventListener('resize', this.resizeGis);
    },
    beforeDestroy() {
        this.DrawAreaSet.forEach((DrawArea) => DrawArea.unbindEvent());
    },
    methods: {
        // 设置坐标
        setAreaCoors(regionList, holeList) {
            if (!regionList.length) {
                this.$message.warning('请输入区域坐标');
            }
            this.clearAll();
            if (!this.isLocatePoint) {
                const { holeRegionCoors, invalidHoleList } = formatEnterAreas(
                    regionList,
                    holeList,
                    this.g,
                    this.shapeType
                );
                this.entryHoleList = holeRegionCoors;
                if (invalidHoleList.length) {
                    this.$message.info(`存在以下无效的空洞坐标：${invalidHoleList.join('、')}`);
                }
            }

            this.DrawArea.initRegionCoors(regionList, false, false, true);
            this.isManualEntry = true;
        },
        async clickTool(type) {
            if (type === 'clean') {
                await this.$confirm('确认清除所有图层?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning',
                });
            }
            this.start(type);
        },
        initFinancialStreet(g) {
            const layer = new g.layer();
            layer.name = '道路';
            layer.visible = true;
            g.gis.scene.add(layer.Group);
            this.financialStreetLayer = layer;
        },
        clearFinancialStreet() {
            if (!this.financialStreetMesh) return;
            this.financialStreetLayer.remove(this.financialStreetMesh);
            this.financialStreetMesh = null;
        },
        gisCenterMove(pointData = this.pointData, isDefaultZoom = false) {
            const { X, lng, Y, lat } = pointData;
            const point = {
                lat: Y || lat,
                lng: X || lng,
            };
            this.g.cameraControl.move(point);
            let timer = setTimeout(() => {
                this.g.gis.needUpdate = true;
                if (!isDefaultZoom) {
                    this.g.cameraControl.zoom = 17;
                }
                clearTimeout(timer);
                timer = null;
            }, 300);
        },
        setLocation(latLng, baseStations = this.baseStations, isAirportPoint) {
            const { cellLongitude: lng, cellLatitude: lat } = latLng;
            const activePoints = baseStations.filter((it) => it.lat === lat && it.lng === lng);
            if (isAirportPoint) {
                this.setAirportBasePoints(activePoints);
            } else {
                this.setBasePoints(false, activePoints);
            }
            this.gisCenterMove(
                {
                    X: lng,
                    Y: lat,
                },
                true
            );
        },
        //
        getMayType() {
            let hostName = window.location.hostname;
            // // 判断是本地、测试内网还是生产地址。
            if (
                hostName.indexOf('localhost') >= 0 ||
                hostName.indexOf('192.168') >= 0 ||
                hostName.indexOf('127.0') >= 0
            ) {
                return 'tx';
            }
            return 'default';
        },
        initGis() {
            const g = new GIS({
                dom: this.$refs.spaceGis,
                initialZoom: 17,
                city: {
                    lat: 30.73722,
                    lng: 121.32674,
                    cityId: 4403,
                    cityName: '深圳',
                },
                options: gisOptions(),
            });
            g.gis.color = 0x081422;
            const serverPath = Decrypt(window.baseMapUrl).replace(/\{/g, '${');
            g.baseMapConfig.setBaseMapConfig({
                name: '底图图层',
                type: 'gcj02',
                serverPath: serverPath,
                option: {
                    colorControl: true,
                },
            });
            g.tileLayerList['高德底图'].colorChangeDate.light.setHex(0x081422);
            g.tileLayerList['高德底图'].colorChangeDate.dark.setHex(0xa5a6f9);
            this.g = g;


            this.initGisOps();
            // 加载后监听
            this.$watch(
                'regionCoors',
                (val) => {
                    if (val && val.length) {
                        this.DrawArea.initRegionCoors(val);
                    }
                },
                { immediate: true, deep: true }
            );
            this.$emit('loaded', this);
            this.gisLoading = false;
        },
        resizeGis(){
            this.g && this.g.gis.reSize();
        },
        initGisOps() {
            const g = this.g;

            this.baseLayer = new g.layer();
            this.baseLayer.visible = true;
            g.gis.scene.add(this.baseLayer);

            this.initFinancialStreet(g);
            // g.layerList.lineLayer.autoButton = false;
            g.layerList.boxSelectLayer.autoButton = false;
            g.layerList.areaEdit.autoButton = true;
            g.layerList['圈选'].autoClear = false;
            g.layerList.圈选.Group.position.y = 0.005;
            g.layerList.圈选.unitMeter = true;
            g.tool.proportional.visible = true; //游标尺展示
            gisUtils.addBoxSelectLayerListen(g, this.stop.bind(this), this);
            // gisUtils.addLineLayerListen(g, this.stop.bind(this), this);
            // 基点点击事件
            g.event.addClick(this.baseLayer, (data, event) => {
                if (this.isJustShowLayer || event.button !== 0) return;
                const pointType = data.object.name;
                const inx = g.meshList.img.getIndex(data);
                let repeatIndex = [];
                if (pointType === 'added') {
                    repeatIndex = this.addedPoints[inx].repeatIndex;
                } else if (pointType === 'noAdd') {
                    repeatIndex = this.noAddPoints[inx].repeatIndex;
                }
                this.allBaseStations.forEach((item, index) => {
                    if (!repeatIndex.includes(index)) {
                        return;
                    }
                    if (item.status === 'added') {
                        item.status = 'noAdd';
                        item.onceAdded = true;
                    } else {
                        item.status = 'added';
                        item.onceAdded = true;
                    }
                });
                this.setBasePoints();
            });
            // 圆形 && 圈选点击事件-右键
            g.event.addClick(g.layerList['圈选'], (data, event) => {
                if (this.isJustShowLayer) return;
                const obj = data.object;
                const name = String(obj.name);
                if (!name.startsWith('circular')) {
                    this.curCircleChoiceObj = data.object;
                }
                if (event.button === 2) {
                    if (name.startsWith('circleChoice')) {
                        this.menuList = menuList.filter((item) => item.prop === 'delCircle');
                        this.isShowMenu = true;
                        return;
                    }

                    const i = Number(name.replace(/[^\d]/g, ''));
                    if (!Number.isFinite(i)) return;
                    if (!this.layerObj || i !== this.layerObj.i) {
                        this.layerObj = this.planeObjs[i];
                    }
                    if (name.startsWith('circular-radiate')) {
                        const { clientX, clientY } = event;
                        this.coordinate = { x: clientX, y: clientY };
                        this.$refs.radiationRangeRef.distance = this.layerObj.distance;
                        this.isShowRadiation = true;
                    } else if (name.startsWith('circular')) {
                        const radiate = (this.layerObj || {}).radiate;
                        const radiateName =
                            radiate && radiate.plane ? 'cancelRadiation' : 'outwardRadiate';
                        this.menuList = menuList.filter((item) => {
                            return [radiateName, 'hole', 'auxiliaryCircle', 'delCircular'].includes(
                                item.prop
                            );
                        });
                        this.isShowMenu = true;
                    }
                }
            });
            //监听圈选
            g.layerList['圈选'].onCircleChoice.addEvent((data) => {
                let { startPoint, radius, name } = data;
                // 第一次画圆
                if (typeof name === 'number') {
                    if (this.circleType === 'circular') {
                        data.name = `${this.circleType}-${this.layerObj.i}`;
                    } else {
                        data.name = `${this.circleType}${name}`;
                    }
                    name = data.name;
                } else {
                    const i = Number(name.replace(/[^\d]/g, ''));
                    if (!this.layerObj || i !== this.layerObj.i) {
                        this.layerObj = this.planeObjs[i];
                    }
                }

                if (name.startsWith('circular')) {
                    this.DrawArea.circleFinish(radius, startPoint, data);
                } else {
                    this.circleChoices[name] = {
                        radius,
                        centerLatitude: startPoint.lat,
                        centerLongitude: startPoint.lng,
                        choicePoints: [],
                    };
                    const choicePoints = [];
                    for (const item of this.allBaseStations) {
                        const distance = g.math.worldDistance(item, startPoint) * 1000;
                        if (distance <= radius && item.status === 'noAdd') {
                            item.status = 'added';
                            item.onceAdded = true;
                            choicePoints.push(item);
                        }
                    }
                    if (choicePoints.length) {
                        this.circleChoices[name]['choicePoints'] = choicePoints;
                        this.setBasePoints();
                    }
                }
            });
            //
            this.DrawArea = this.getDrawArea(this.shapeType);
            this.DrawArea.initGisInfo(g, this);

            this.initRegions({
                DrawArea: this.DrawArea,
                setRegionGroup: this.setRegionGroup.bind(this),
            });
        },
        getDrawArea(shapeType = 2) {
            return {
                1: this.Circular,
                2: this.Polygon,
                4: this.Line,
                5: this.LocatePoint,
            }[shapeType];
        },
        clearAll(isClearAll = false) {
            this.g.layerList['圈选'].removeAll();
            this.g.layerList.areaEdit.removeAll();
            this.baseLayer.removeAll();
            this.DrawArea.clearAll(isClearAll);
            this.layerObj = {};
            this.planeObjs.forEach((item) => {
                if (item && item.hole) item.hole.plane = [];
            });
            this.planeObjs = [];
            this.circleChoices = {};
            this.curCircleChoiceObj = {};
            this.multiCreatedStations = [];
            this.entryHoleList = [];
            if (this.$refs.entryRegion) {
                this.$refs.entryRegion.clear();
            }
            this.$nextTick(() => {
                this.$eventBus.$emit('updateSharpInfo');
            });
        },
        // 测距等工具栏功能专用
        stop() {
            const g = this.g;
            gisUtils.runGisFun(g, 'lineLayer', 'outMode');
            gisUtils.runGisFun(g, 'boxSelectLayer', 'outMode');
        },
        drawAreas(type = 'areaEdit') {
            if (type === 'line') {
                this.g.layerList.lineEditLayer.endEdit();
                this.clearAll();
            }

            const len = this.planeObjs.length;
            this.planeObjs[len] = new this.DrawArea(len);
            this.layerObj = this.planeObjs[len];
            this.DrawArea.setCurOperateItem(this.layerObj.region);
            this.start(type);
        },
        start(type) {
            const g = this.g;
            // this.stop();
            if (type === 'clean') {
                this.clearAll();
            } else if (type === 'areaEdit') {
                this.$message(
                    '请开始绘制图形,双击结束绘制,点击图形进入编辑模式，双击边缘点退出编辑'
                );
                gisUtils.runGisFun(g, 'areaEdit', 'startEdit');
            } else if (type === 'circular') {
                this.$message(
                    '请开始绘制圆形，左击地图上任意一点，并拖动鼠标，拖动结束后即完成绘制'
                );
                g.layerList.圈选.circleColor = 0x0085f9;
                g.layerList.圈选.circleFrameColor = 0x1a7cff;
                g.layerList.圈选.startMode();
                this.circleType = 'circular';
            } else if (type === 'circleChoice') {
                this.$message(
                    '请开始绘制辅助圆，左击地图上任意一点，并拖动鼠标，拖动结束后即完成绘制'
                );
                g.layerList.圈选.circleColor = 0x4f2cde;
                g.layerList.圈选.circleFrameColor = 0x734ce3;
                g.layerList.圈选.startMode();
                this.circleType = 'circleChoice';
            } else if (type === 'locatePoint') {
                this.$message('请开始绘制位置点，左击地图上任意一点，将生成对应位置点');
                this.circleType = 'locatePoint';
                this.DrawArea.setDottingStatus(true);
            } else if (type === 'line') {
                this.$message(
                    '请开始绘制线段,双击结束绘制,点击图形进入编辑模式，双击边缘点退出编辑'
                );
                gisUtils.runGisFun(g, 'lineEditLayer', 'startEdit');
            } else {
                // 测距
                this.$message('清开始测距，在地图上左击任意点，自动连线测距，双击结束测距');
                gisUtils.runGisFun(g, type, 'startMode');
            }
        },
        // 基站打点
        setBasePoints(isEditedHole = false, activePoints) {
            // 修改空洞里的基站
            isEditedHole && this.layerObj.handlerHolePoints(this.allBaseStations);
            this.baseStations = removeDuplicateLocations(this.allBaseStations);
            if (!this.isCheckBase) return;
            const g = this.g;
            const addedPoints = [];
            const noAddPoints = [];

            for (const item of this.baseStations) {
                if (item.status === 'added') {
                    addedPoints.push(item);
                } else if (item.status === 'noAdd') {
                    noAddPoints.push(item);
                }
            }
            Object.assign(this, { addedPoints, noAddPoints });
            // 删除所有模型
            this.baseLayer.removeAll();
            //模型添加进图层
            const addedMesh = this.createPointsMesh(addedPoints, 'added');
            const noAddMesh = this.createPointsMesh(noAddPoints, 'noAdd');
            addedMesh && this.baseLayer.add(addedMesh);
            noAddMesh && this.baseLayer.add(noAddMesh);
            if (activePoints) {
                const activeMesh = this.createPointsMesh(activePoints, 'active');
                activeMesh && this.baseLayer.add(activeMesh);
            }
            //更新GIS
            g.gis.needUpdate = true;
        },
        // 机场基站打点
        setAirportBasePoints(activePoints) {
            // 删除所有模型
            this.baseLayer.removeAll();
            //模型添加进图层
            const airportMesh = this.createPointsMesh(this.airportBasePoints, 'airport');
            airportMesh && this.baseLayer.add(airportMesh);
            if (activePoints) {
                const activeMesh = this.createPointsMesh(activePoints, 'active');
                activeMesh && this.baseLayer.add(activeMesh);
            }
            //更新GIS
            this.g.gis.needUpdate = true;
        },
        // 设置所有基站数据
        setAllBaseStations() {
            this.allBaseStations = this.planeObjs.reduce((acc, cur) => {
                return cur && cur.baseStations ? acc.concat(cur.baseStations) : acc;
            }, []);
            return this;
        },
        createPointsMesh(points, status) {
            if (!points.length) return;
            const g = this.g;
            const mapIcon = {
                added: addedIcon,
                noAdd: noAddIcon,
                active: activeIcon,
                airport: airportPoint,
            };
            const icon = mapIcon[status];
            const material = g.meshList.img.getMaterial({ url: icon, opacity: 1 });
            points.autoScale = true;
            const mesh = g.meshList.img.create(points, material);
            mesh.name = status;
            return mesh;
        },
        // 查看/隐藏基站
        viewBaseStation(isManualTab) {
            if (!isManualTab) {
                this.isCheckBase = !this.isCheckBase;
            }
            // 线路控制控制外扩渲染
            if (this.isLine && Object.keys(this.layerObj || {}).length) {
                this.layerObj.setExpansion(this.isCheckBase);
            }
            if (!this.isCheckBase) {
                this.baseLayer.removeAll();
                return;
            }
            if (this.tabName === '2') {
                this.setAirportBasePoints();
            } else {
                this.setBasePoints(this.isManualEntry);
            }
            if (this.isManualEntry) {
                this.isManualEntry = false;
            }
        },
        // 单一多边形区域获取基站点/辐射
        baseManage() {
            this.isShowBaseManage = true;
        },
        // 圈选初始化
        drawCircleChoice(circleType = 'circleChoice') {
            const circleList = this.baseInfo.auxiliaryCircleList;
            circleList &&
                circleList.forEach((item, inx) => {
                    const { centerLatitude, centerLongitude, radius } = item;
                    const name = `${circleType}-${inx + 1}`;
                    this.g.layerList.圈选.create({
                        name,
                        ...gisOptions().circleChoice,
                        circleShowRadius: !this.isJustShowLayer,
                        radius,
                        startPoint: { lat: centerLatitude, lng: centerLongitude },
                    });
                    this.circleChoices[name] = {
                        ...item,
                        choicePoints: [],
                    };
                    const addedBaseStations = this.allBaseStations.filter(
                        (item) => item.status === 'added'
                    );
                    for (const baseStation of addedBaseStations) {
                        const distance =
                            this.g.math.worldDistance(baseStation, {
                                lat: centerLatitude,
                                lng: centerLongitude,
                            }) * 1000;
                        if (distance <= radius) {
                            this.circleChoices[name].choicePoints.push(baseStation);
                        }
                    }
                });
        },
        // 已创建复合区域，获取已添加基站列表
        getCreatedInnerStations(regionId) {
            return this.$post('getRegionExpansionCells',{
                    type: 0,
                    regionId,
                    expansion: 0,
                    isMultiRegion: 1,
                    shapeType: this.shapeType,
                })
                .then((res) => {
                    return res;
                })
                .catch(() => {
                    return {};
                });
        },
        /* -------------外层组件调用----------------- */
        getAddedBasePoints() {
            // 如果没有打开基站管理，过滤下全部基站数据
            if (!this.addStations.length) {
                return this.allBaseStations.filter(
                    (item) => item.onceAdded && item.status === 'added'
                );
            }
            return this.addStations;
        },
        clearDrawAreaSet() {
            this.DrawAreaSet.forEach((DrawArea) => DrawArea.unbindEvent());
        },
        getAddedStations(addStations) {
            this.addStations = addStations;
        },
        getHolePlanes() {
            if (!this.DrawArea.holeLayer) {
                return [];
            }
            const holeLayers = this.DrawArea.holeLayer.Group.children;
            const holeReginList = holeLayers.map((hole) => {
                const regionCoors = toPointSequence(hole.operateType.points);
                return {
                    areaType: 2,
                    polygon: regionCoors,
                    region: hole.operateType.i,
                    index: hole.operateType.index,
                };
            });
            holeReginList.sort(compareSort('region'));
            return holeReginList;
        },
        getRegionParams() {
            const allAreas = this.planeObjs.filter(Boolean);
            const holeList = this.getHolePlanes();
            return this.DrawArea.getParams(allAreas, holeList);
        },
        setExpand() {
            if (this.layerObj && this.layerObj.lineSegment) {
                const { lineSegment } = this.layerObj;
                this.layerObj.initExpandAndBaseStation(lineSegment.points);
            }
        },
        setRegionGroup(regionList, option = {}) {
            let mergeList = [];
            const sharpInstances = [];
            let extremum = [];
            const shapeTypeSet = [];

            for (const item of regionList) {
                const shapeType = +item.shapeType;
                const regions = getRegions(item, shapeType);

                const DrawArea = this.getDrawArea(shapeType);
                if (!shapeTypeSet.includes(shapeType)) {
                    shapeTypeSet.push(item.shapeType);
                    DrawArea.initGisInfo(this.g, this);
                }

                const { mergeRegions, sharps } = DrawArea.initRegions(regions, {
                    row: item,
                    ...option,
                });
                mergeList.push(...mergeRegions);
                sharpInstances.push(...sharps);
            }

            if (mergeList.length) {
                if (mergeList.length === 1) {
                    this.gisCenterMove(mergeList[0]);
                } else {
                    extremum = getCoordinateExtremum(mergeList);
                    this.moveCenter(extremum);
                }
            }

            return { sharps: sharpInstances, extremum };
        },
        moveCenter(extremum) {
            let timer = setTimeout(() => {
                this.g.cameraControl.zoomByPoints(extremum, 1);
                clearTimeout(timer);
                timer = null;
            }, 300);
        },
        removeAllSharps() {
            this.DrawAreaSet.forEach((DrawArea) => {
                DrawArea.clearAll();
            });
            this.planeObjs.forEach((item) => {
                if (item && item.hole) item.hole.plane = [];
            });
            this.planeObjs = [];
        },
        initGisInfo() {
            this.DrawArea.initGisInfo(this.g, this);
        },
    },
};
</script>
<style lang="less" scoped>
@import url('./gisCom.less');
.gis-com {
    
}
.tip{
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
  color: #aaa;
  font-size: 12px;
  z-index: 999;
}
</style>
