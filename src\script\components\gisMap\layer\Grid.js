import { getCoordinateExtremum } from '@/utils/utils.js'; 
export default (g, option = {}) => {
  let graphs = [];
  // 
  let { click,clickOther, frameColor, needFrame = true } = option;
  // 创建图层
  let layer = new g.layer();
  layer.name = '栅格';
  g.gis.scene.add(layer);
  layer.visible = true;
  g.meshConfig.clickColor = 0x12F2F3;


  g.meshList.plane.opacity = 0.3;

  g.event.addHover(layer,function(th,event){
    //执行栅格的hover方法获取数据index
    let index = g.meshList.plane.hover(th,event);
    const mesh = index.mesh;
    //组织信息展示模块需要的数据格式
    let showData = [{name: '栅格数据', values: mesh.hoverList[index.index] , checked: true }];
    g.show.showInfo(showData,event);
  },function(){
    g.meshList.plane.clearHover();
    g.show.clearInfo();
  });

  class Grid {
    constructor(centerPoint, points, config, data ) {
      this.centerPoint = centerPoint;
      this.points = points;
      this.data = data;
      this.config = config;
      this.mesh = null;
    }
    static getGraphs() {
      return graphs;
    }
    static getLayer(){
      return layer;
    }

    static removeHighlight(){
      layer.removeHighlights();
    }

    static highlightMesh(mesh){
      g.meshList.plane.highlight(mesh, 0, 0);
    }

    static createGrids(grids, callback, isRemoveAll = true, isMoveCenter = false) {
      if (!grids || !grids.length){
        Grid.removeAll();
        return;
      } 
      if (isRemoveAll) {
        Grid.removeAll();
      }
      for (const grid of grids) {
        const { centerPoint, points, config = {}, data } = grid;
        const curGrid = new Grid(centerPoint, points, config, data );
        graphs.push(curGrid);

        callback && callback(curGrid);
      }
      Grid.draw(grids);
      isMoveCenter && Grid.toMoveCenter();
    }
    static handleClick(data, event) {
      let index = g.meshList.plane.click(data,event);
      const curGrid = data.object.graphs;
      if (event.button === 0) {
        click && click(curGrid[index.index]);
      }
    }
    static removeAll() {
      layer.removeAll();
      graphs = [];
      // g.layerList.divLayer.removeAll();
    }
    static toMoveCenter(outsidePoints, zoom = 1) {
      const points = outsidePoints || graphs.reduce((pre, cur) => pre.concat(cur.points), []);
      const vertexes = getCoordinateExtremum(points);
      //计时2s后执行
      const timer = setTimeout(() => {
          //根据最大最小值计算GIS渲染的半径以及中心点；
          let easy = g.cameraControl.computeZoomByPoints(vertexes, zoom);
          //返回的数据中已经存在radius（半径）以及target（中心点），因为缩放需要的字段名称是targetPoint，这里赋值一下；
          easy.targetPoint = easy.target;
          //根据半径中心点进行缩放（可能是放大，可能是缩小，可用于制作钻取效果）
          g.cameraControl.gradualChangeRadius(easy);
          clearTimeout(timer);
      }, 1000);
    }
    static destroy() {
      g = null;
      graphs = null;
      option = null;
      layer = null;
    }
    static enlarge(zoom) {
      g.cameraControl.zoom = zoom;
    }
    static draw(grids) {

      //传入数据创建平面
      let data = [];
      let meshHoverData = [];
      for (let i = 0; i < grids.length; i++) {
        const { points, config = {} } = grids[i];
        const coors = points.map((point) => [point.lng, point.lat]);
        const { color, name, score, maxH = 0.01 } = config;
        data.push({
          ls: g.math.lsRegular(coors) || [],
          layers: [{ maxH, color: color }],
        });
        if (name) {
            meshHoverData.push({ [name]: score || 0 });
        }
      }
      Object.assign(data, {
          needFrame,
          frameColor: frameColor || 0xffffff
      })

      const mesh = g.meshList.plane.create(data);
      mesh.graphs = graphs;
      mesh.hoverList = meshHoverData;
      layer.add(mesh);
    }
    // 给栅格文字（如分数等）
    addText(latLng, text, style) {
      let defStyle = style || 'transform: translate(-50%, -50%)';
      return g.layerList.divLayer.addDiv({
        dom: `<div style="position: absolute;${defStyle}">${text}</div>`,
        point: latLng,
      });
    }
    toMoveGrid() {
      g.cameraControl.zoomByPoints(this.points, 1);
    }
    remove() {
      layer.remove(this.mesh);
    }
  }

  g.event.addClick(layer, Grid.handleClick,function(){
    g.meshList.plane.clearClick();
    clickOther && clickOther();
  });

  return Grid;
};
