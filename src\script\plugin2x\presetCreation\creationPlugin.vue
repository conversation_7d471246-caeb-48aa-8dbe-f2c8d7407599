<template>
    <div class="creation">
        <div class="creation-task">
            <div class="title-top">
                <div class="title">任务创建</div>
            </div>
            <div class="content">
                <div class="content-left">
                    <card :img="require(`../../../img/base_config_header.png`)">
                        <searchBar
                            ref="creationParams"
                            class="form earth-overflow-y"
                            :fields="formCols"
                            :form="form"
                        >
                            <el-button
                                style="width: 15rem; transform: translateX(5.94rem)"
                                class="earth-btn-common"
                                type="primary"
                                size="small"
                                @click="creatTask"
                                >创建任务</el-button
                            >
                        </searchBar>
                    </card>
                </div>
                <gisMap id="createGIS" @loaded="gisLoaded" />
            </div>
        </div>
        <myDialog :visible.sync="isShowTips.visible" title="提示" :btnList="isShowTips.btnList">
            <div>圈选区域范围过小，暂无基站数，重新选择范围</div>
        </myDialog>
    </div>
</template>

<script>
import { creationFormCols } from '_const/presetCreation.js';
import searchBar from '_com/searchBar/searchBar.vue';
import gisMap from '_com/gisMap/index.vue';
import initCircular from '_com/gisMap/layer/circular.js';
import initGrid from '_com/gisMap/layer/Grid.js';
import card from '_com/card/index.vue';
import myDialog from '_com/dialog/index.vue';
import { toCoordinate } from '@/utils/method.js';

export default {
    name: 'presetCreation',
    components: {
        searchBar,
        gisMap,
        card,
        myDialog,
    },
    data() {
        return {
            form: {
                taskName: '',
                regionName: '',
                timeType: '',
                executionTime: [],
                disasterType: '',
                heatMapType: 2,
                continuousAnalysis: 0,
                populationTargetId: [],
                occurTime: '',
            },
            total: 0,
            tableRow: {},
            isShowTips: {
                visible: false,
                btnList: [
                    {
                        name: '确定',
                        class: 'blue-btn',
                        listeners: {
                            click: () => {
                                this.isShowTips.visible = false;
                            },
                        },
                    },
                ],
            },
            isLoading: false,
        };
    },
    computed: {
        formCols() {
            const { timeType } = this.form;
            return creationFormCols(timeType,  {
                executionTime: this.handleDateChange,
                timeType: this.handleTimeType,
            });
        },
        curRow() {
            return this.$route.query.row ? JSON.parse(this.$route.query.row) : {};
        },
        isCircle() {
            return Number(this.curRow.shapeType) === 1;
        },
    },
    created() {
        this.form.regionName = this.curRow.regionName;
    },
    methods: {
        gisLoaded(g) {
            if (this.isCircle) {
                this.circular = initCircular(g, {});
            } else {
                this.grid = initGrid(g, {});
            }
            this.dialogGIS = g;
            this.$nextTick(() => {
                this.drawGraph();
            });
        },
        drawGraph() {
            if (this.isCircle) {
                const { centerLatitude, centerLongitude, radius } = this.curRow.circle;
                this.initCircle(centerLatitude, centerLongitude, radius);
            } else {
                const { regionName, regionCoors } = this.curRow;
                const data = {
                    centerPoint: {},
                    points: toCoordinate(regionCoors),
                    config: {
                        color: 0x7dacdc,
                        name: '区域名称',
                        score: regionName,
                    },
                    data: {},
                };
                this.grid.createGrids([data], null, true, true);
            }
        },
        initCircle(lat, lng, radius) {
            const startPoint = { lat, lng };
            this.circular.createCircular(
                {
                    circleColor: 0x7dacdc,
                    circleOpacity: 0.1,
                    circleFrame: true,
                    circleFrameColor: 0x7dacdc,
                    cirCleShowClose: true,
                    circleShowRadius: true,
                    radius: Math.round(radius),
                    startPoint: startPoint,
                },
                {
                    dom: `<div class="ripple">
                          <div class="dot" style="z-index: 5;"></div>
                          <div 
                            class="text" 
                            style="
                                transform:translateX(-45%);
                                background:#EF6868;
                                border-radius:.25rem;
                                box-shadow: 0px 2px 4px 0px rgba(0,0,0,0.19);
                                white-space: nowrap;
                                width: auto;"
                            >
                                震中（${lng},${lat}）
                            </div>
                      </div>`,
                    point: startPoint,
                }
            );
        },
        getExternalSquare(centerPoint, radius) {
            const LAT_PIECE = 0.00001;
            const LNG_PIECE = 0.000009;
            const { lat, lng } = centerPoint;
            return [
                { lat: lat + radius * LAT_PIECE, lng: lng - radius * LNG_PIECE },
                { lat: lat + radius * LAT_PIECE, lng: lng + radius * LNG_PIECE },
                { lat: lat - radius * LAT_PIECE, lng: lng + radius * LNG_PIECE },
                { lat: lat - radius * LAT_PIECE, lng: lng - radius * LNG_PIECE },
            ];
        },
        async creatTask() {
            if (!this.handleDateChange(this.form.executionTime)) {
                return;
            }
            const res = await this.$refs.creationParams.validForm();
            if (res === true) {
                this.checkBaseStation(); //基站校验
            }
        },
        requestTaskCreate(params) {
            this.getPost('post', 'taskCreate', params, '创建任务接口请求', () => {
                this.$message({
                    message: '创建任务成功！',
                    type: 'success',
                });
                this.remove();
                this.jumpToTask();
            });
        },
        checkBaseStation() {
            const { shapeType, regionCoors, circle, regionName, regionId } = this.curRow;
            let {
                taskName,
                timeType,
                executionTime,
                disasterType,
                heatMapType,
                continuousAnalysis,
                populationTargetId,
                occurTime,
            } = this.form;
            const [startTime, endTime] = executionTime || [];
            if (!occurTime) {
                occurTime = startTime;
            }
            const params = {
                taskName,
                regionType: 1,
                region: {
                    shapeType,
                    regionCoors,
                    circle,
                },
                startTime,
                endTime,
                roleType: 6,
                disasterType,
                heatMapType,
                burialPepTaskPara: {
                    earthquakeCode: regionId,
                    earthquakeName: regionName,
                    timeType,
                    populationTargetId: populationTargetId.join(','),
                    continuousAnalysis,
                    occurTime,
                },
                taskCreateSource: 2,
            };
            this.isLoading = true;
            this.getPost('post', 'checkBaseStation', params, '基站校验', (res) => {
                this.isLoading = false;
                if (!res.allLacCont) {
                    this.isShowTips.visible = true;
                } else {
                    this.requestTaskCreate(params);
                }
            });
        },
        remove() {
            const GIS = this.dialogGIS;
            if (GIS) {
                GIS.layerList.圈选.removeAll();
                GIS.layerList.divLayer.removeAll();
            }
        },
        jumpToTask() {
            this.$router.push({
                name: 'myTask',
            });
        },
        handleDateChange(value) {
            const startDate = new Date(value[0]);
            const startMinutes = startDate.getMinutes();
            const endDate = new Date(value[1]);
            const endMinutes = endDate.getMinutes();
            if (this.form.timeType === 3) {
                if (
                    !(
                        [0, 30].includes(startMinutes) &&
                        [0, 30].includes(endMinutes)
                    )
                ) {
                    this.$message({
                        message: '时间粒度为30分钟时,时间选择的分钟只能是00, 30',
                        type: 'error',
                    });
                    return false;
                }
            } else if (this.form.timeType === 4) {
                if (
                   !(
                        [0, 15, 30, 45].includes(startMinutes) &&
                        [0, 15, 30, 45].includes(endMinutes)
                    ) 
                ) {
                    this.$message({
                        message: '时间粒度为15分钟时,时间选择的分钟只能是00,15,30,40',
                        type: 'error',
                    });
                }
            }
            return true;
        },
        handleTimeType() {
            this.form.executionTime = [];
        },
    },
};
</script>

<style lang="less" scoped>
.creation {
    width: 100%;
    height: 100%;
}
.creation-task {
    width: 100%;
    height: 100%;
}
.content {
    width: 100%;
    height: calc(100% - 76px);
    border-radius: @border-radius-base;
    position: relative;
    &-left {
        position: absolute;
        top: 24px;
        left: 24px;
        width: 430px;
        height: calc(100% - 48px);
        z-index: 5;
    }
    #createGIS {
        width: 100%;
        height: 100%;
    }
    .form {
        width: 100%;
        height: calc(100% - 2.22rem);
        overflow-x: hidden;
    }
}
@keyframes scale0 {
    from {
        transform: scale(1.3);
        opacity: 0.8;
    }
    to {
        transform: scale(1.6);
        opacity: 0.5;
    }
}
@keyframes scale1 {
    from {
        transform: scale(1.6);
        opacity: 0.5;
    }
    to {
        transform: scale(1.9);
        opacity: 0.3;
    }
}
@keyframes scale2 {
    from {
        transform: scale(1.9);
        opacity: 0.3;
    }
    to {
        transform: scale(2.2);
        opacity: 0;
    }
}
/deep/.ripple {
    // border-radius:50%;
    position: relative;
    top: -0.455rem;
    .wave0 {
        animation: scale0 1s;
        -webkit-animation: scale0 1s;
        animation-iteration-count: infinite;
        -webkit-animation-iteration-count: infinite;
        animation-timing-function: linear;
        -webkit-animation-timing-function: linear; /* Safari and Chrome */
    }
    .wave1 {
        animation: scale1 1s;
        -webkit-animation: scale1 1s;
        animation-iteration-count: infinite;
        -webkit-animation-iteration-count: infinite;
        animation-timing-function: linear;
        -webkit-animation-timing-function: linear; /* Safari and Chrome */
    }
    .wave2 {
        animation: scale2 1s;
        -webkit-animation: scale2 1s;
        animation-iteration-count: infinite;
        -webkit-animation-iteration-count: infinite;
        animation-timing-function: linear;
        -webkit-animation-timing-function: linear; /* Safari and Chrome */
    }
    .dot {
        float: left;
        width: 1rem;
        height: 1rem;
        border-radius: 50%;
        background: red;
        position: absolute;
        box-shadow: 0px 2px 10px 1px rgba(0, 0, 0, 0.21);
        border: 3px solid #ffffff;
    }
    .dot_left {
        margin-left: -50%;
    }
    .text {
        position: absolute;
        top: 2rem;
        left: -50%;
        width: 10rem;
        color: #fff;
        font-size: 14px;
        padding: 3px 10px;
    }
}
.table-wraper {
    height: 33.33rem;
}
.btn {
    width: 100%;
}
/deep/.el-icon-question {
    font-size: 16px;
}
</style>
