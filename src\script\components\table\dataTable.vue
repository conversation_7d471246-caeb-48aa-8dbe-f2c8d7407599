<!-- 数据表格 -->
<template>
	<div class="data-table" ref="tables">
		<el-table
		:size="size"
		:header-cell-style="theadColor"
		:row-style="rowStyle"
		style="width: 100%"
		:stripe="stripe"
		v-bind="$attrs"
		v-on="$listeners"
		>
		<!-- 自定义空内容 -->
		<template #empty>
			<div class="table-empty">
                <img src="../../../img/common/tableEmpty.png" alt="" />
                <div class="text">暂无数据</div>
            </div>
		</template>
		<!-- 列 -->
		<el-table-column v-if="isSelectable" type="selection" reserve-selection width="36" />
		<el-table-column
			v-for="item in columns"
			v-bind="item"
			v-slot="{ row, $index }"
			:key="item.prop"
			:show-overflow-tooltip="item.showOverflowTooltip === undefined ? true : item.showOverflowTooltip"
		>
			<slot :name="item.prop" :row="row" :inx="$index">
			{{ row[item.prop] }}
			</slot>
		</el-table-column>
		</el-table>
		<!-- 分页 -->
		<div v-if="total" class="wrap-pagination" :class="{ hideUpLine: isHideUpLine }">
		<slot name="pagination">
			<el-pagination
			class="pagination"
			:current-page="pagination.curPage"
			:page-sizes="pagination.pageSizes"
			:page-size="pagination.pageSize"
			:total="total"
			:layout="layout"
			:pager-count="pagination.pagerCount"
			background
			@size-change="handleSizeChange"
			@current-change="handleCurrentChange"
			/>
		</slot>
		</div>
	</div>
</template>

<script>

export default {
	name: 'data-table',
	inheritAttrs: false,
	props: {
		columns: {
		type: Array,
			default: () => [],
			required: true,
		},
		size: {
			type: String,
			default: 'small',
		},
		theadStyle: {
			type: Object,
			default: () => ({}),
		},
		rowPropStyle:{
			type: Object,
			default: () => ({}),
		},
		updateTable: {
			type: Function,
			default: () => {},
			validator (value) {
				return typeof value === 'function';
			},
		},
		total: {
			type: Number,
			validator: (value) => {
				return value >= 0;
			},
		},
		layout: {
			type: String,
			default: 'total, prev, pager, next, sizes, jumper',
		},
		isSelectable: {
			type: Boolean,
			default: false,
		},
		pagination: {
			type: Object,
			default: () => ({
				curPage: 1,
				pageSize: 15,
			}),
		},
		isHideUpLine: {
			type: Boolean,
			default: true,
		},
		stripe: {
			type: Boolean,
			default: true,
		},
  },
  data() {
    return {};
  },
  computed: {
		theadColor() {
			return {
				backgroundColor: '#1B293B',
				fontWeight: 'bold',
				color: '#9AAFC1',
				fontSize: '.88rem',
				height:'2.5rem',
				...this.theadStyle,
			};
		},
		rowStyle(){
			return{
				background:'#0D192B',
				color:'#FFFFFF',
				fontSize: '.88rem',
				height:'2.5rem',
				...this.rowPropStyle,
			}
		},
  },
  created() {
    	this.initPagination();
  },
  methods: {
    initPagination() {
		const { pageSize,  pageSizes, pagerCount } = this.pagination;
		const mapPageSizes = {
			10: [10, 15, 25, 40],
			15: [15, 20, 30, 50],
			20:	[10, 20, 40, 60],
		}
		if (!pageSizes || !pageSizes.length) {
			this.pagination.pageSizes = mapPageSizes[pageSize];
		}
		if (!pagerCount) {
			this.pagination.pagerCount = 7;
		}
    },
    handleSizeChange(pageSize) {
		Object.assign(this.pagination, {
			curPage: 1,
			pageSize,
		});
		this.updateTable({
			curPage: 1,
			pageSize,
		});
    },
    handleCurrentChange(curPage) {
		this.pagination.curPage = curPage;
		this.updateTable({
			curPage,
			pageSize: this.pagination.pageSize,
		});
    },
  },
};
</script>

<style lang="less" scoped>
.data-table {
	height: 100%;
	display: flex;
	flex-flow: column;
	/deep/.el-table {
		flex: 1;
		width: 100%;
		background: transparent;
		display: flex;
    	flex-direction: column;
		// /deep/ .el-table__header-wrapper .gutter {
		// 	background-color: #f6f7fa;
		// }
		.el-table__body-wrapper {
			// height: calc(100% - 2.92rem);
			flex:1;
			overflow-y: auto;
			
			&::-webkit-scrollbar {
				width: 6px;
				height: 6px;
			}
			&::-webkit-scrollbar-thumb {
				border-radius: 10px;
				box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
				background: #5C6F92;
			}
			&::-webkit-scrollbar-track {
				/* 滚动条里面轨道 */
				box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
				border-radius: 10px;
				background: transparent;
			}
			&::-webkit-scrollbar-corner {
				background: rgba(0, 0, 0, 0);
			}
			.el-table__row {
				&.success-row {
					background-color: #bfe3ff;
				}
				.cell {
					font-size: .81rem;
				}
				&.custom-cell {
					padding: .22rem 0;
					.cell {
						padding-left: .22rem;
						padding-right: .22rem;
						line-height: 1.11rem;
					}
				}
			}
			.el-table__body {
				width: 100% !important;
				background: transparent;
			}
		}
	}
	.wrap-pagination {
		margin-top: .72rem;
		border-top: .06rem solid #ddd;
		text-align: right;
		height: 2.22rem;
		.pagination {
		margin: .44rem 0;
		padding-right: 0;
		/deep/ .el-pagination__sizes {
			margin-right: 0;
		}
		/deep/.el-pagination__total{
			color:#fff;
		}
		/deep/.btn-prev,/deep/.btn-next{
			background: rgba(117, 163, 223, 0);
		}
		/deep/.el-pagination.is-background .el-pager li:not(.disabled).active{
			    background: rgba(117, 163, 223, 0);
				box-shadow: inset 0px 0px 8px 0px #4984FF;
				border-radius: 3px;
				border: 1px solid #A8BFE8;
		}
		/deep/.el-pagination__jump{
			color:#fff;
		}
		/deep/.el-input__inner{
			background: rgba(117, 163, 223, 0);
			box-shadow: inset 0px 0px 8px 0px #4984FF;
			border-radius: 3px;
			border: 1px solid #A8BFE8;
			color:#fff;
		}
		}
	}
	.hideUpLine {
		margin-top: .28rem;
		border-top: none;
	}
}
.table-empty {
    min-height: 100px;
    text-align: center;
    width: 100%;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    flex-direction: column;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    margin: auto;
    .text {
        width: 50%;
        color: #fff;
        font-size: .78rem;
        font-weight: 600;
        height: 1.11rem;
        line-height: 1.11rem;
    }
}
/deep/.el-table--scrollable-x .el-table__body-wrapper {
    &::-webkit-scrollbar {
        width: 6px;
        height: 6px;
    }
    &::-webkit-scrollbar-thumb {
        border-radius: 10px;
        box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
        background: #5C6F92;
    }
    &::-webkit-scrollbar-track {
        /* 滚动条里面轨道 */
        box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
        border-radius: 10px;
        background: transparent;
    }
    &::-webkit-scrollbar-corner {
        background: rgba(0, 0, 0, 0);
    }
}
/deep/.el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell{
	background-color: #1B293B;
}
/deep/.el-table th.el-table__cell.is-leaf {
	border-bottom:0px;
}
/deep/.el-table td.el-table__cell, .el-table th.el-table__cell.is-leaf{
	border-bottom:0px;
	padding:0px;
}
/deep/ .el-table::before{
	background-color:transparent;
}
/* 调整悬浮行的背景色和文本颜色 */
/deep/.el-table tbody tr:hover>td {
  background-color: #4d515e !important; /* 更更深的暗色背景 */
}
 
/* 调整选中行的背景色和文本颜色 */
.el-table .el-table__body tr.el-table__row.selected {
  background-color: #5a5f6f; /* 最深的暗色背景 */
}
/deep/ .el-pagination.is-background .el-pager li{
	background-color: transparent;
    color: #fff;
}
/deep/.el-pagination.is-background .el-pager li:not(.disabled).active{
	color: #FFF;
    background: rgba(117, 163, 223, 0);
    box-shadow: inset 0px 0px 8px 0px #4984FF;
    border-radius: 3px;
    border: 1px solid #A8BFE8;
}
/deep/.current-row{
	background: rgba(39,69,108,0.5) !important;
	box-shadow: inset 0px 0px 8px 0px #4984FF;
	border: 1px solid #A8BFE8;
}
/deep/.el-table__body tr.current-row>td.el-table__cell{
	background-color: transparent !important;
}
/deep/ .el-table td.el-table__cell, /deep/ .el-table th.el-table__cell {
    border-top: none !important;
    border-bottom: none !important;
}
</style>

<style lang="less">
.el-select-dropdown{
    background: #040A1E;
    box-shadow: inset 0px 0px 8px 0px #4984FF;
    border-radius: 2px;
    border: 1px solid #A8BFE8;
    color:#fff;
}
.el-select-dropdown__item{
    color:#fff;
}
.el-popper[x-placement^=top] .popper__arrow::after{
      border-top-color: #040A1E;
      box-shadow: inset 0px 0px 8px 0px #4984FF;
}
.el-select-dropdown__item.hover, .el-select-dropdown__item:hover{
    background-color: #1b293B;
}
</style>
