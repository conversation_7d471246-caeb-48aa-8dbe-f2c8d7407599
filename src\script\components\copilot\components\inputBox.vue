<template>
    <!-- 输入主体 -->
    <div class="inputBox">
        <el-input
            class="inputBox-top"
            size="mini"
            :placeholder="search.placeholder"
            v-model="search.content"
            type="textarea"
            ref="searchContent"
            :autosize="{ minRows: 1, maxRows: 2 }"
            @keydown.native="onKeySystem($event)"
        ></el-input>
        <div class="inputBox-bottom">
            <div class="model-select">
                <el-select
                    class="model"
                    :value="model"
                    size="mini"
                    popper-class="custom-select-model"
                    @change="$emit('update:model', $event)"
                >
                    <el-option v-for="(item, i) in modelOpts" :key="i" v-bind="item" />
                </el-select>
            </div>
            <div class="inputBox-icons">
                <div v-show="isRecording" class="process-text">
                    <span class="voice-line">
                        <hr class="hr1" />
                        <hr class="hr2" />
                        <hr class="hr3" />
                        <hr class="hr4" />
                        <hr class="hr5" />
                    </span>
                </div>
                <el-tooltip
                    popper-class="copilot-tooltip"
                    :content="isRecording ? '关闭录音' : '打开录音'"
                    placement="top"
                    :visible-arrow="false"
                >
                    <i
                        class="input-icon speek-btn"
                        :class="[isRecording ? 'copilot-icon-stopRecording' : 'copilot-icon-voice']"
                        @click="isRecording = !isRecording"
                    ></i>
                </el-tooltip>
                <el-tooltip
                    popper-class="copilot-tooltip"
                    :content="isVolume ? '点击关闭声音' : '点击打开声音'"
                    placement="top"
                    :visible-arrow="false"
                >
                    <i
                        class="input-icon speek-btn"
                        :class="[isVolume ? 'copilot-icon-volume' : 'copilot-icon-mute']"
                        @click="handlerVolume"
                    ></i>
                </el-tooltip>
                <el-tooltip
                    popper-class="copilot-tooltip"
                    :content="isSendEnd ? '发送' : '停止'"
                    placement="top"
                    :visible-arrow="false"
                >
                    <i
                        class="input-icon speek-btn"
                        :class="[isSendEnd ? 'copilot-icon-send' : 'copilot-icon-stop']"
                        @click="handleSend()"
                    ></i>
                </el-tooltip>
            </div>
        </div>
    </div>
</template>

<script>
import logo from '../img/logo.gif';
import { EventBus } from '../common/eventBus.js';
export default {
    name: 'inputBox',
    props: {
        modelOpts: {
            type: Array,
            default: () => []
        },
        model: {
            type: String,
            default: 'DeepSeek-V3'
        }
    },
    data() {
        return {
            logo,
            speed: 80,
            search: {
                placeholder: '提问模板：北京市+日期+通勤出行情况',
                content: ''
            },
            isRecording: false,
            isSendEnd: true,
            isVolume: true
        };
    },
    created() {
        EventBus.$on('sendText', (data) => {
            this.setText(data);
            const time = setTimeout(() => {
                this.handleSend(true);
                clearTimeout(time);
            }, 500);
        });
        EventBus.$on('initInputText', (data) => {
            if (this.isRecording) {
                this.search.content += data;
                const time = setTimeout(() => {
                    let robotEle = this.$refs.searchContent.$el;
                    robotEle.scrollTop = robotEle.scrollHeight;
                    clearTimeout(time);
                }, 500);
            }
        });
        EventBus.$on('isTypeComplete', (data) => {
            this.isSendEnd = data;
        });
    },
    mounted() {},
    beforeDestroy() {
        EventBus.$off('sendText');
        EventBus.$off('initInputText');
        EventBus.$off('isTypeComplete');
    },
    methods: {
        //监听键盘事件
        onKeySystem(e) {
            if (e.altKey) {
                if (e.keyCode == 13) this.search.content += '\n'; //增加换行符
            } else if (e.keyCode == 13) {
                this.handleSend(); //发送消息
                e.preventDefault(); // 阻止默认行为
            } else if (e.keyCode == 27) {
                // escape键
                this.isRecording = false;
                e.preventDefault(); // 阻止默认行为
            }
        },
        // voiceWakeUp 是否是语音唤醒
        handleSend() {
            if (!this.isSendEnd) {
                EventBus.$emit('stopTypeAndVoice');
                return;
            }
            if (this.search.content && this.isSendEnd) {
                this.$emit('sendText', this.search.content);
                this.search.content = '';
            }
        },
        setText(text) {
            this.$set(this.search, 'content', text);
        },
        handlerVolume() {
            this.isVolume = !this.isVolume;
            EventBus.$emit('changeVolume', this.isVolume);
        }
    }
};
</script>

<style lang="less" scoped>
.inputBox {
    width: 100%;
    height: 100%;
    border: 1px solid var(--baseBorderColor);
    border-radius: 7px;
    &:hover {
        box-shadow: inset 0 0 5px var(--baseBorderColor);
    }
    &-top {
        width: 100%;
        height: calc(100% - 47px);
        padding: 13px;
        box-sizing: border-box;
    }
    /deep/.el-textarea__inner {
        color: var(--baseTextColor);
        background-color: transparent;
        border: none;
        -ms-overflow-style: none; /* IE and Edge */
        scrollbar-width: none; /* Firefox */
        font-size: var(--subFontSize);
        resize: none;
    }

    &-bottom {
        width: 100%;
        height: 40px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .inputBox-icons {
            display: flex;
            align-items: center;
            cursor: pointer;
            padding-left: 15px;

            .input-icon {
                margin-right: 10px;
            }
            .speek-btn {
                width: 36px;
                height: 36px;
            }

            .send-btn {
                width: 37px;
                height: 37px;
            }
        }
    }
}
.process-text {
    .voice-line {
        hr {
            background-color: var(--baseBorderColor);
            width: 2px;
            height: 5px;
            margin: 0 -4px;
            display: inline-block;
            border: none;
        }
        hr {
            animation: note 0.2s ease-in-out;
            animation-iteration-count: infinite;
            animation-direction: alternate;
        }
        .hr1 {
            animation-delay: -1s;
        }
        .hr2 {
            animation-delay: -0.9s;
        }
        .hr3 {
            animation-delay: -0.8s;
        }
        .hr4 {
            animation-delay: -0.7s;
        }
        .hr5 {
            animation-delay: -0.6s;
        }
        @keyframes note {
            from {
                transform: scaleY(1);
            }
            to {
                transform: scaleY(4);
            }
        }
    }
}
.model-select {
    width: 170px;
    height: 30px;
    background: transparent;
    border: 1px solid var(--baseBorderColor);
    border-radius: 13px;
    margin-left: 10px;
    .model {
        width: 100%;
        border: none;
        background-color: transparent;
        background-repeat: no-repeat;
        background-size: 100% 100%;
        /deep/ .el-input__inner {
            color: var(--baseTextColor);
            background-color: transparent;
            border: none;
            border-radius: 13px;
            &:hover {
                box-shadow: inset 0 0 5px var(--baseBorderColor);
            }
        }
    }
}
</style>
<style lang="less">
.copilot-tooltip {
    &.el-tooltip__popper.is-dark {
        background: #73829e;
        padding: 5px;
    }
}
</style>
