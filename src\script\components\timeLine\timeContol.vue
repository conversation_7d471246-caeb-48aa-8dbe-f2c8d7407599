<template>
    <div class="timeContol">
        <div v-if="dataList.length > 12" :class="['to-left',{'no-click':noLeftOffset}]" @click="toLeft"></div>
        <div ref="dayNavScroll" class="navScroll" :class="{'hasCenter':dataList.length<=12}"
            @DOMMouseScroll="handleScroll($event,'dayNavScroll','daynav')" @mousewheel="handleScroll($event,'dayNavScroll','daynav')">
            <div ref="daynav" class="nav" :style="dayNavScroll">
                <div 
                    v-for="(item,index) in dataList" 
                    :key="index"
                    :class="['day-item',{'active':activeDayItem === index}]" 
                    :draggable="false"
                    @click="dateClick(item,index)" 
                >
                <span class="text">{{timeSplit(item)}}</span>
                </div>
            </div>
        </div>
        <div v-if="dataList.length > 12" :class="['to-right',{'no-click':noRightOffset}]" @click="toRight"></div>
    </div>
</template>

<script>
export default {
    name:'timeContol',
    props:{
        dataList:{
            type:Array,
            default:() => [
                '2024-11-01','2024-11-02','2024-11-03','2024-11-04','2024-11-05','2024-11-06','2024-11-07','2024-11-08','2024-11-09','2024-11-10','2024-11-11','2024-11-12','2024-11-13','2024-11-14','2024-11-15','2024-11-16'
            ]
        }
    },
    data(){
        return{
            activeDayItem:0,
            dayNavScroll:{
                transform: ''
            },
        }
    },
    computed: {
        timeSplit() {
            return function (val) {
                const data = val.split(' ');
                if (data.length > 1) {
                    return `${data[0]}\n ${data[1]}`;
                }
                return data[0];
            };
        },
        noLeftOffset(){
            const currentOffset = this.getCurrentScrollOffset('dayNavScroll');
            if (!currentOffset) {
                return true;
            }
            return false;
        },
        noRightOffset(){
            const navWidth = this.$refs['daynav'].clientWidth;
            const containerWidth = this.$refs['dayNavScroll'].clientWidth;
            const currentOffset = this.getCurrentScrollOffset('dayNavScroll');
            if (navWidth && currentOffset && navWidth - currentOffset <= containerWidth) {
                return true;
            }
            return false;
        }
    },
    methods:{
        handleScroll (e,scrollKey,navKey) {
            e.preventDefault();
            e.stopPropagation();
            const type = e.type;
            let delta = 0;
            if (type === 'DOMMouseScroll' || type === 'mousewheel') {
                delta = (e.wheelDelta) ? e.wheelDelta : -(e.detail || 0) * 40;
            }
            if (delta > 0) {
                this.scrollPrev(scrollKey,navKey);
            } else {
                this.scrollNext(scrollKey,navKey);
            }
        },
        toLeft(){
            if (this.noLeftOffset) return;
            this.scrollPrev('dayNavScroll','daynav');
        },
        toRight(){
            if (this.noRightOffset) return;
            this.scrollNext('dayNavScroll','daynav');
        },
        getCurrentScrollOffset(key) {
            const navStyle  = this[key];
            return navStyle.transform
                ? Number(navStyle.transform.match(/translateX\(-(\d+(\.\d+)*)px\)/)[1])
                : 0;
        },
        scrollPrev(scrollKey,navKey) {
            const containerWidth = this.$refs[scrollKey].clientWidth;
            const currentOffset = this.getCurrentScrollOffset(scrollKey);
            if (!currentOffset) return;
            let newOffset = currentOffset > containerWidth
                ? currentOffset - containerWidth
                : 0;
            this.setOffset(newOffset,scrollKey);
        },
        scrollNext(scrollKey,navKey) {
            const navWidth = this.$refs[navKey].clientWidth;
            const containerWidth = this.$refs[scrollKey].clientWidth;
            const currentOffset = this.getCurrentScrollOffset(scrollKey);
            if (navWidth - currentOffset <= containerWidth) return;
            let newOffset = navWidth - currentOffset > containerWidth * 2
                ? currentOffset + containerWidth
                : (navWidth - containerWidth);
            this.setOffset(newOffset,scrollKey);
        },
        setOffset(value,key) {
            this[key].transform = `translateX(-${value}px)`;
        },
        dateClick(item,index){
            this.activeDayItem = index;
            this.$emit('timeLineChangeAction', item);
        }
    }
}
</script>

<style lang="less" scoped>
.timeContol{
    width:100%;
    height:100%;
    padding:0 96px;
    position:relative;
    .to-left{
        position:absolute;
        left:55px;
        top:18px;
        width:34px;
        height:34px;
        background:url('../../../img/icon/to-left.png') no-repeat center center / 100% 100%;
        cursor:pointer;
        &:hover{
            background:url('../../../img/icon/to-left-active.png') no-repeat center center / 100% 100%;
        }
        &.no-click{
            background:url('../../../img/icon/to-left-no-click.png') no-repeat center center / 100% 100%;
        }
    }
    .to-right{
        position:absolute;
        right:55px;
        top:18px;
        width:34px;
        height:34px;
        background:url('../../../img/icon/to-right.png') no-repeat center center / 100% 100%;
        cursor:pointer;
        &:hover{
            background:url('../../../img/icon/to-right-active.png') no-repeat center center / 100% 100%;
        }
        &.no-click{
            background:url('../../../img/icon/to-right-no-click.png') no-repeat center center / 100% 100%;
        }
    }
    .navScroll{
        overflow: hidden;
        white-space: nowrap;
        margin-right: 15px;
        display: flex;
        transition: transform 0.5s ease;
        &.hasCenter{
            justify-content: center;
        }
    }
    .nav{
        padding-left: 0;
        margin: 0;
        float: left;
        list-style: none;
        box-sizing: border-box;
        position: relative;
        display: flex;
        transition: transform 0.5s ease;
        &:before,
        &:after {
            display: table;
            content: " ";
        }
        &:after {
            clear: both;
        }
    }
    .day-item{
        width: 130px;
        text-align: center;
        cursor:pointer;
        color: #9EA2AA;
        position:relative;
        height:60px;
        background:url('../../../img/analyzeDetails/timeSelect.png') no-repeat center center / 100% 100%;
        line-height: 18px;
        padding-top: 12px;
        &.active{
            background:url('../../../img/analyzeDetails/timeSelect_active.png') no-repeat center center / 100% 100%;
            color:#fff;
            text-shadow: 0px 0px 4px rgba(62,136,233,0.64);
            font-weight:600;
            height: 70px;
        }
    }
    .day-item +.day-item{
        margin-right:15px;
    }
}
.text{
    white-space: pre-wrap;
}
</style>