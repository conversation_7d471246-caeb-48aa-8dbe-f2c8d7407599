<!-- 基站列表 -->
<template>
    <div class="base-station">
        <div class="base-station__head">
            <span class="title">
                <span class="title-dot"></span>
                <span>{{ baseData.resourceName }}&nbsp;&nbsp;</span>
                <span style="font-size: 12px">(基站数量：{{ account }}个)</span>
            </span>
        </div>
        <div class="base-station__body">
            <el-tabs v-model="activeName">
                <el-tab-pane
                    v-for="tab in tabs"
                    :key="tab.name"
                    :name="tab.name"
                    :label="tab.label"
                >
                    <keep-alive>
                        <component
                            v-if="tab.name === activeName"
                            :is="tab.comp"
                            :outsideRow="outsideRow"
                            :baseInfo="baseInfo"
                            @getCurAllBases="getCurAllBases"
                            v-on="$listeners"
                        />
                    </keep-alive>
                </el-tab-pane>
            </el-tabs>
        </div>
    </div>
</template>
<script>
import station from './station.vue';
import airportStation from './airportStation.vue';
export default {
    name: 'baseStationShow',
    inject: ['setTabName', 'root'],
    props: {
        outsideRow: {
            type: Object,
            default: () => ({}),
        },
        baseInfo: {
            type: Object,
            default: () => ({}),
        },
    },
    data() {
        return {
            activeName: '1',
            baseData: {
                icon: '',
                resourceName: '',
            },
            tabs: [
                {
                    name: '1',
                    label: '区域圈选',
                    comp: station,
                    allBases: [],
                },
                {
                    name: '2',
                    label: '高质量',
                    comp: airportStation,
                    allBases: [],
                },
            ],
        };
    },
    computed: {
        userInfo() {
            return {
                id: 1,
                name: 'admin',
                describe: '系统管理员',
            };
        },
        curTab() {
            return this.tabs.find((item) => item.name === this.activeName);
        },
        account() {
            return this.curTab.allBases.length;
        },
        isAirportType() {
            return this.outsideRow.layerIDs.includes('12');
        },
    },
    watch: {
        activeName: {
            handler(newVal) {
                this.setTabName(newVal);
                this.root.viewBaseStation(true);
            },
        },
    },
    created() {
        Object.assign(this.baseData, {
            icon: this.outsideRow.icon,
            resourceName: this.outsideRow.resourceName,
        });
    },
    mounted() {
        if (!this.isAirportType) {
            this.tabs.pop();
        }
    },
    beforeDestroy() {
        this.setTabName('');
    },
    methods: {
        getCurAllBases(allBases) {
            this.curTab.allBases = allBases;
        },
    },
};
</script>
<style lang="less" scoped>
.base-station {
    &__head {
        margin: 0 -12px;
        padding: 6px 16px 12px 16px;
        line-height: 22px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.08);
        .title {
            width: 208px;
            height: 22px;
            font-size: 16px;
            font-weight: bold;
            color: #ffffff;
            .title-dot {
                display: inline-block;
                width: 8px;
                height: 8px;
                background: #53ffff;
                border-radius: 4px;
                vertical-align: middle;
                margin-right: 8px;
            }
        }
    }
    &__body {
        /deep/ .el-tabs__header {
            margin: 0;
        }
    }
}
</style>
