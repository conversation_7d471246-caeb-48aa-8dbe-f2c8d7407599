<template>
    <div ref="details" id="details" class="analyze-details">
        <Transition name="fade-top">
            <div v-show="show" class="analyze-details-top">
                <div class="title">{{ routeParmas.taskName }}</div>
                <div class="show-list">
                    <el-row :gutter="10">
                        <el-col
                            style="display: flex; align-items: center"
                            :span="item.span"
                            v-for="(item, index) in listDisplay"
                            :key="index"
                        >
                            <span class="name">{{ item.name }}</span>
                            <span
                                v-tooltip
                                :tips="item.name + timeTypeList[routeParmas[item.props]]"
                                v-if="item.props === 'timeType'"
                                class="value earth-text-ellipsis"
                                >{{ timeTypeList[routeParmas[item.props]] || '-' }}</span
                            >
                            <span
                                v-tooltip
                                :tips="item.name + analysisTime"
                                v-else-if="item.props === 'time'"
                                class="value earth-text-ellipsis"
                                >{{ analysisTime }}</span
                            >
                            <span
                                v-tooltip
                                :tips="item.name + routeParmas[item.props]"
                                v-else-if="item.props === 'earthquakeLevel'"
                                class="value level-text earth-text-ellipsis"
                                >{{ routeParmas[item.props] || '-' }}级</span
                            >
                            <span
                                v-tooltip
                                :tips="item.name + routeParmas[item.props]"
                                v-else
                                class="value earth-text-ellipsis"
                            >
                                {{
                                    item.formatter
                                        ? item.formatter(routeParmas[item.props])
                                        : routeParmas[item.props] || '-'
                                }}
                            </span>
                        </el-col>
                    </el-row>
                </div>
            </div>
        </Transition>
        <div class="analyze-details-content">
            <gisMap
                v-loading="isLoading"
                element-loading-text="拼命加载中"
                element-loading-spinner="el-icon-loading"
                element-loading-background="rgba(0, 0, 0, 0.8)"
                @loaded="gisLoaded"
            />
            <div class="top-tab">
                <div class="tab">
                    <img
                        v-if="populationIds.includes(1)"
                        :src="
                            require(`../../../img/analyzeDetails/affectedPopulation${
                                isActive === 1 ? '_active' : ''
                            }.png`)
                        "
                        class="tab-item"
                        @click="isActive = 1"
                    />
                    <img
                        v-if="populationIds.includes(2)"
                        :src="
                            require(`../../../img/analyzeDetails/suspectedMissingPersons${
                                isActive === 2 ? '_active' : ''
                            }.png`)
                        "
                        class="tab-item"
                        @click="isActive = 2"
                    />
                    <img
                        v-if="populationIds.includes(3)"
                        :src="
                            require(`../../../img/analyzeDetails/buryPersons${
                                isActive === 3 ? '_active' : ''
                            }.png`)
                        "
                        class="tab-item"
                        @click="isActive = 3"
                    />
                </div>
                <div class="baseStation">
                    <el-checkbox v-model="checked">基站</el-checkbox>
                </div>
            </div>
            <Transition name="fade-left">
                <div v-show="hasShowLeft" class="left-panel">
                    <leftPanel
                        :tableData="tableData"
                        :type="typeList[isActive - 1]"
                        @rowClick="rowClick"
                    ></leftPanel>
                    <div class="left-shrink" title="收起" @click="hasShowLeft = false"></div>
                </div>
            </Transition>
            <Transition name="fade-left">
                <div
                    v-show="!hasShowLeft"
                    class="left-open"
                    title="展开"
                    @click="hasShowLeft = true"
                ></div>
            </Transition>
            <Transition name="fade-right">
                <div v-show="hasShowRight" class="right-panel">
                    <rightPanel
                        :type="typeList[isActive - 1]"
                        :analysisOverview="analysisOverview"
                        :coreInformation="coreInformation"
                        :title="rightTitle"
                        :trendAnalysisData="trendAnalysisData"
                        :portraitAnalysisData="portraitAnalysisData"
                    ></rightPanel>
                    <div class="right-shrink" title="收起" @click="hasShowRight = false"></div>
                </div>
            </Transition>
            <Transition name="fade-right">
                <div
                    v-show="!hasShowRight"
                    class="right-open"
                    title="展开"
                    @click="hasShowRight = true"
                ></div>
            </Transition>
            <Transition name="fade-bottom">
                <div v-show="show" class="bottom-panel">
                    <div class="text">热力图时间选择</div>
                    <timeContol
                        ref="timeContol"
                        :dataList="mapTimeLineData"
                        @timeLineChangeAction="timeLineChangeAction"
                    />
                </div>
            </Transition>
            <div class="legend">
                <mapLegend :legends="legends" />
            </div>

            <person-num-tips
                class="num-tips"
                v-show="!rightTitle.includes('网格名称:') && rightTitle != '总览' && isActive === 1"
                :tipsData="tipsData"
            ></person-num-tips>
        </div>
    </div>
</template>

<script>
import { listDisplay, ageList } from '_const/analyzeDetails.js';
import { timeTypeList } from '_const/analyzeProduct.js';
import { listDisplay as presetList } from '_const/resultProduct.js';
import { timeTypeList as presetTimeTypeList } from '_const/analyzeProduct.js';
import gisMap from '_com/gisMap/index.vue';
import leftPanel from './components/leftPanel.vue';
import rightPanel from './components/rightPanel.vue';
import timeContol from '_com/timeLine/timeContol.vue';
import mapLegend from '_com/gisMap/mapLegend.vue';
import personNumTips from './components/personNumTips.vue';
import initCircular from '_com/gisMap/layer/circular.js';
import initRing from '_com/gisMap/layer/circle.js';
import initGrid from '_com/gisMap/layer/Grid.js';
import initPoint from '_com/gisMap/layer/point.js';
import initLine from '_com/gisMap/layer/line.js';
import { tooltip } from '@/script/common/directives/tooltip.js';
import { toCoordinate } from '@/utils/method.js';
// import { detailList, hotData } from './mookGZData.js';
const MAP_ACTIVE_VAL = {
    1: 1,
    2: 2,
    3: 3,
    '12': 1,
    '13': 1,
    '23': 2,
    '123': 1,
}
const dayjs = require('dayjs');
import html2canvas from 'html2canvas';
export default {
    name: 'analyzeDetails',
    components: {
        gisMap,
        leftPanel,
        rightPanel,
        timeContol,
        mapLegend,
        personNumTips,
    },
    data() {
        return {
            rightTitle: '总览',
            routeParmas: {},
            listDisplay,
            timeTypeList,
            isActive: 1,
            checked: false,
            show: false,
            mapTimeLineData: [],
            currentTimeLineValue: '',
            tableData: [],
            analysisOverview: '',
            legends: [
                { background: '#FF3333', mapColor: 0xff3333, value: '5000-10000' },
                { background: '#FFA040', mapColor: 0xffa040, value: '3000-5000' },
                { background: '#FFFF02', mapColor: 0xffff02, value: '1000-3000' },
                { background: '#00BE42', mapColor: 0x00be42, value: '1-1000' },
                { background: '#aaaaaa', mapColor: 0xffffff, value: '0' },
            ],
            s2List: [],
            lacellList: [],
            trendAnalysisData: {
                xAxis: [],
                data: [],
            },
            portraitAnalysisData: {
                xAxis: [
                    '0-10岁',
                    '10-20岁',
                    '20-30岁',
                    '30-40岁',
                    '40-50岁',
                    '50-60岁',
                    '60-70岁',
                    '70-80岁',
                    '80岁以上',
                ],
                data: [],
                pieData: [],
            },
            coreInformation: [],
            typeList: ['受影响人群', '疑似失联人群', '掩埋人群'],
            isLoading: false,
            tipsData: [
                {
                    icon: require('../../../img/analyzeDetails/icon_outflow.png'),
                    label: '区域流出人数',
                    value: 0,
                },
                {
                    icon: require('../../../img/analyzeDetails/icon_inflow.png'),
                    label: '区域流入人数',
                    value: 0,
                },
            ],
            hasShowLeft: true,
            hasShowRight: true,
            populationIds: [],
            renderInterval: null,
        };
    },
    directives: {
        tooltip,
    },
    computed: {
        analysisTime() {
            const { analysisStartTime, analysisEndTime } = this.routeParmas;
            if (analysisStartTime) {
                return analysisStartTime + ' - ' + analysisEndTime;
            }
            return '-';
        },
        routeQuery() {
            return this.$route.params;
        },
        isOutSide() {
            return this.routeQuery.outside;
        },
        BottomBackGround() {
            return {
                background: `url(${require('../../../img/analyzeDetails/thermalPanel.png')}) no-repeat center center / 100% 100%`,
            };
        },
    },
    watch: {
        checked: {
            handler(newV) {
                if (newV) {
                    if (this.lacellList.length) {
                        this.renderStationAndOutline(this.lacellList);
                    } else {
                        this.drawBaseStation();
                    }
                } else {
                    this.clearAll();
                }
            },
        },
        isActive: {
            handler(newV) {
                this.initData(newV);
                this.resetOverview();
            },
        },
    },
    created() {
        const populationIds = this.routeQuery.populationIds;
        this.populationIds = (populationIds || '').split(',').filter(Boolean).map(Number);
        if (this.populationIds.length) {
            this.populationIds.sort();
            const enumStr = this.populationIds.join('');
            this.isActive = MAP_ACTIVE_VAL[enumStr];
        }
        
        const type = this.$routeParmas.taskCreateSource;
        if (type == 2) {
            this.listDisplay = presetList;
            this.timeTypeList = presetTimeTypeList;
        }
    },
    mounted() {
        this.$nextTick(() => {
          /*   this.show = true;
            this.initialization(); */
            window.addEventListener('message', (e) => {
                if (e.origin == window.origin && e.data.type == 'screenshot') {
                    this.screenshot(e.data.sourceUrl);
                }
            });
        });
    },
    activated() {
        this.$nextTick(() => {
            this.show = true;
            this.initialization();
        });
    },
    methods: {
        screenshot(sourceUrl) {
            setTimeout(() => {
                const node = document.body;
                window.pageYoffset = 0;
                document.documentElement.scrollTop = 0;
                document.body.scrollTop = 0;
                html2canvas(node, {
                    scale: 2,
                    useCORS: true,
                    scrollX: 0,
                    scrollY: 0,
                    height: node.scrollHeight, //canvas高
                    width: node.scrollWidth, //canvas宽
                    windowHeight: node.scrollHeight,
                    windowWidth: node.scrollWidth,
                }).then((canvas) => {
                    const screenUrl = canvas.toDataURL('image/png');
                    window.parent.postMessage(
                        {
                            type: 'screenshot',
                            content: screenUrl,
                            sourceUrl: sourceUrl,
                        },
                        window.origin
                    );
                });
            }, 3000);
        },
        fromOutSideGetData() {
            this.getPost(
                'post',
                'getTaskList',
                {
                    taskId: this.routeQuery.taskId,
                    pageSize: 20,
                    currentPage: 1,
                },
                '获取任务报告列表信息',
                (res) => {
                    const data = res.detailList;
                    if (!data.length) {
                        this.$message({
                            message: '没有该任务数据！',
                            type: 'warning',
                        });
                        return;
                    }
                    this.routeParmas = res.detailList[0];
                    // this.routeParmas= {taskId: 1,
                    //     createUserName: 'xiaozhang',
                    //     taskName: '广州',
                    //     taskStatus: 1,
                    //     analysisStartTime: '2024-10-25 15:00:00',
                    //     analysisEndTime: '2024-11-11 15:00:00',
                    //     taskCreateTime: '2008-05-11 14:10:01',
                    //     executeStartTime: '2008-05-14 13:30:00',
                    //     executeEndTime: '2008-05-20 14:35:00',
                    //     earthquakeName: '上海市闽行区',
                    //     centerLon: 116.70249504859957,
                    //     centerLat: 39.916197694780266,
                    //     analysisRadius: 10,
                    //     earthquakeLevel: 7.8,
                    //     earthquakeDepth: 10,
                    //     occurTime: '2024-05-01 12:52:26',
                    //     timeType: 1,};
                    // this.creatTimeLineData();
                    this.initData();
                    this.drawOutline(this.g);
                }
            );
        },
        initialization() {
            if (this.isOutSide) {
                this.fromOutSideGetData();
            } else {
                const params = sessionStorage.getItem('routeParmas') || '{}';
                if (JSON.stringify(this.$route.params) !== '{}') {
                    this.routeParmas = this.$route.params;
                } else if (JSON.stringify(params) !== '{}') {
                    this.routeParmas = JSON.parse(params);
                }
                sessionStorage.setItem('routeParmas', JSON.stringify(this.routeParmas));
                // this.creatTimeLineData();
                this.initData(this.isActive);
                this.drawOutline(this.g);
            }
        },
        initData(type = 1) {
            this.rightTitle = '总览';
            this.tableData = [];
            this.line && this.line.removeAll();
            this.$refs.timeContol && (this.$refs.timeContol.activeDayItem = 0);
            this.getTrendAnalysisChartData(type);
        },
        gisLoaded(g) {
            this.g = g;
            this.circular = initCircular(g, {});
            this.Grid = initGrid(g, {
                click: this.handleGridClick,
                // clickOther:this.handleClickOther,
            });
            this.Polygon = initGrid(g);
            this.ActiveBaseStationRange = initGrid(g);
            this.basePoint = initPoint(g, {
                img: require('../../../img/icon/baseStation_icon.png'),
                activeImg: require('../../../img/icon/baseStation_icon.png'),
                click: this.handleBaseStationClick
            });
            this.pointCircle = initRing(g, {});
            this.pointRange = initGrid(g, {
                needFrame: false,
            });
            this.line = initLine(g);
            this.drawOutline(g);
            const layer = g.tileLayerList['高德底图'].Group;
            g.event.addClick(layer, () => {
                if (this.rightTitle !== '总览') {
                    this.handleClickOther();
                }
            });
        },
        drawOutline() {
            const { shapeType, regionCoors } = this.routeParmas;
            const isPolygon = Number(shapeType) === 2;
            if (isPolygon) {
                const data = {
                    centerPoint: {},
                    points: toCoordinate(regionCoors),
                    config: {
                        color: 0x7dacdc,
                    },
                    data: {},
                };
                this.Polygon && this.Polygon.createGrids([data], null, true, true);
            } else {
                this.initCircular(this.g, isPolygon);
            }
        },
        // 表格点击高亮
        rowClick(row) {
            const clickGrid = this.Grid.getGraphs().filter((grid) => grid.data.s2Id === row.s2Id);
            if (clickGrid.length) {
                this.Grid.removeHighlight();
                this.drawHeightLight(clickGrid[0].data);
            }

            this.rightTitle = '网格名称：' + (row.address || '');
            this.tipsData.forEach((item) => {
                item.label = '网格' + item.label.slice(-4);
            });
            this.getSummary(this.currentTimeLineValue, 2, row.s2Id);
            this.getPersonNum(this.currentTimeLineValue, 2, row.s2Id);
            this.getS2TrendAnalysis(this.isActive, {
                dataType: 2,
                s2Id: row.s2Id,
            });
            this.coreInformation = [row.accPepCnt, row.laccellCnt];
            this.getS2Analysis(this.currentTimeLineValue, row.s2Id);
        },
        handleGridClick(clickGrid) {
            this.line.removeAll();
            this.drawHeightLight(clickGrid.data);
            this.getS2Analysis(this.currentTimeLineValue, clickGrid.data.s2Id);
            this.rightTitle = '网格名称: ' + (clickGrid.data.address || '');
            this.tipsData.forEach((item) => {
                item.label = '网格' + item.label.slice(-4);
            });
            this.getSummary(this.currentTimeLineValue, 2, clickGrid.data.s2Id);
            this.getPersonNum(this.currentTimeLineValue, 2, clickGrid.data.s2Id);
            this.getS2TrendAnalysis(this.isActive, {
                dataType: 2,
                s2Id: clickGrid.data.s2Id,
            });
        },
        // 点击其他处总览数据展示
        handleClickOther() {
            this.initData();
            this.resetOverview();
        },
        // 重置为总览数据
        resetOverview() {
            this.line.removeAll();
            const { centerLon, centerLat, analysisRadius } = this.routeParmas;
            const startPoint = { lat: centerLat, lng: centerLon };
            this.circular.toMoveCenter(startPoint, analysisRadius);
        },
        // 绘制高亮线
        drawHeightLight(data) {
            this.line.removeAll();
            const { vertexList } = data;
            if (!vertexList || !vertexList.length) {
                return;
            }
            let point = [];
            let pointList = [];
            for (let i = 0; i < vertexList.length; i++) {
                const item = vertexList[i];
                const nextItem = i + 1 < vertexList.length ? vertexList[i + 1] : vertexList[0];
                point.push({
                    start: {
                        lng: item.lon,
                        lat: item.lat,
                    },
                    end: {
                        lng: nextItem.lon,
                        lat: nextItem.lat,
                    },
                    color: 0x12f2f3,
                });
                pointList.push({
                    lng: item.lon,
                    lat: item.lat,
                });
            }
            this.line.createLine(point, pointList);
        },
        initCircular(g, isPolygon) {
            const { centerLon, centerLat, analysisRadius } = this.routeParmas;
            if (!centerLon || !g) {
                return;
            }
            const startPoint = { lat: centerLat, lng: centerLon };
            this.circular.createCircular(
                {
                    circleColor: 0x7dacdc,
                    circleOpacity: 0.1,
                    circleFrame: true,
                    circleFrameColor: 0x7dacdc,
                    cirCleShowClose: true,
                    circleShowRadius: false,
                    radius: Number(analysisRadius),
                    startPoint: startPoint,
                },
                isPolygon
                    ? {
                          dom: `<div class="epicenter"></div>`,
                          point: startPoint,
                      }
                    : null
            );
        },
        creatTimeLineData(slicesData) {
            const { occurTime, earthquakeLevel } = this.routeParmas;
            // const slicesData = this.sliceTime(analysisStartTime, analysisEndTime, timeType);
            this.mapTimeLineData = slicesData;
            this.currentTimeLineValue = this.mapTimeLineData[0];
            this.earthquakeData = { occurTime, earthquakeLevel };
        },
        // 时间切片函数
        sliceTime(startTime, endTime, timeType) {
            const result = [];
            let currentTime = new Date(startTime);

            while (currentTime <= new Date(endTime)) {
                result.push(dayjs(currentTime).format('YYYY-MM-DD HH:mm:ss')); // 保存当前时间
                // 根据切片间隔更新当前时间
                if (timeType === 1) {
                    currentTime.setDate(currentTime.getDate() + 1);
                } else if (timeType === 2) {
                    currentTime.setHours(currentTime.getHours() + 1);
                } else if (timeType === 3) {
                    currentTime.setMinutes(currentTime.getMinutes() + 15);
                }
            }
            //15分钟粒度，去掉第一个时间切片
            if (timeType === 3) {
                if (result.length > 0) {
                    result.shift();
                }
            } else if (timeType === 2) {
                if (result.length > 0) {
                    result.pop();
                }
            }

            return result;
        },
        //时间轴切换
        timeLineChangeAction(params) {
            this.checked = false;
            this.currentTimeLineValue = params;
            this.getHotMapData(params);
            this.getS2Analysis(params);
            this.getTopN(params);
            this.getSummary(params, 1);
            this.getPersonNum(params, 1, '');
        },
        // 分析综述
        getSummary(time, type, s2Id) {
            let params = {
                startTime: time,
                endTime: time,
                populationTargetId: this.isActive,
                type: type,
                taskId: this.routeParmas.taskId,
            };
            if (type === 2) {
                params['s2Id'] = s2Id;
            }
            this.getPost('post', 'getSummary', params, '获取分析综述', (res) => {
                this.analysisOverview = res.summary || '';
            });
        },
        //重点区域
        getTopN(time) {
            this.getPost(
                'post',
                'getTopN',
                {
                    startTime: time,
                    endTime: time,
                    populationTargetId: this.isActive,
                    type: 1,
                    taskId: this.routeParmas.taskId,
                    pageNum: 1,
                    pageSize: 10,
                },
                '获取重点区域信息',
                (res) => {
                    this.tableData = res.resultList || [];
                }
            );
        },
        //右侧分析数据
        getS2Analysis(time, s2Id) {
            this.getPost(
                'post',
                's2Analysis',
                {
                    startTime: time,
                    endTime: time,
                    populationTargetId: this.isActive,
                    s2Id: s2Id,
                    taskId: this.routeParmas.taskId,
                },
                '获取栅格信息',
                (res) => {
                    this.handlerRightBoard(res);
                    if (s2Id) {
                        this.rightTitle = '网格名称：' + (res.gridName || '');
                    }
                }
            );
        },
        handlerLengendValue(newV) {
            if (newV) {
                const equal = newV / 4;
                for (let i = 0; i < 4; i++) {
                    const min = Math.floor(newV - equal * (i + 1));
                    const max = Math.floor(newV - equal * i);
                    this.legends[i].value = (min || 1) + '-' + max;
                }
            }
        },
        // 获取S2栅格
        getHotMapData(time) {
            this.isLoading = true;
            this.lacellList = [];
            this.getPost(
                'post',
                's2HeatList',
                {
                    startTime: time,
                    endTime: time,
                    populationTargetId: this.isActive,
                    type: 1,
                    taskId: this.routeParmas.taskId,
                },
                '获取S2栅格热力信息',
                (res) => {
                    this.Grid && this.Grid.removeAll();
                    const { s2List } = res;
                    if (s2List.length) {
                        const accPepCnt = Math.max(...s2List.map((item) => item.accPepCnt));
                        this.handlerLengendValue(accPepCnt);
                    }
                    if (this.checked) {
                        this.drawBaseStation();
                    }
                    this.s2List =
                        s2List &&
                        s2List.map((item) => {
                            return {
                                centerPoint: {
                                    lat: item.centerY,
                                    lng: item.centerX,
                                },
                                points: item.vertexList.map((val) => {
                                    return {
                                        lng: val.lon,
                                        lat: val.lat,
                                    };
                                }),
                                config: {
                                    color: this.handlerPointColor(item.accPepCnt),
                                    score: item.accPepCnt,
                                    name: '',
                                },
                                data: item,
                            };
                        });
                    this.Grid && this.Grid.createGrids(this.s2List);
                    this.isLoading = false;
                }
            );
        },
        // 渲染基站
        drawBaseStation() {
            this.isLoading = true;
            this.clearAll();

            const { taskId } = this.routeParmas;
            let params = {
                taskId,
            };
            this.getPost('post', 'getBaseDistribution', params, '获取基站分布', (res) => {
                this.isLoading = false;
                this.lacellList = (res.detailList || []).map((it) => {
                    return {
                        latLng: {
                            lat: it.cgiLat,
                            lng: it.cgiLng,
                        },
                        data: it,
                    };
                });
                // 渲染基站和基站范围数据
                this.renderStationAndOutline(this.lacellList);
            });
        },
        renderStationAndOutline(stationList) {
            const { polygonList, circleList } = this.getBaseStationOutline(stationList);
            this.pointCircle.createCircles(circleList);
            this.basePoint.createPoints(stationList);
            
            // 将polygonList按每10个为一组进行切分，用setInterval每隔1.5秒渲染一组
            const chunkSize = 10;
            const chunks = [];
            
            // 先将数据切分成多个数组
            for (let i = 0; i < polygonList.length; i += chunkSize) {
                chunks.push(polygonList.slice(i, i + chunkSize));
            }
            // 使用setInterval逐个渲染
            let currentIndex = 0;
            this.renderInterval = setInterval(() => {
                if (currentIndex > chunks.length - 1) {
                    currentIndex = 0
                }
                const chunk = chunks[currentIndex];
                if (chunk && chunk.length) {
                    this.pointRange.createGrids(chunk, null);
                }
                currentIndex++;
            }, 1500); // 每1.5秒渲染一组
        },
        getBaseStationOutline(points = []){
            const polygonList = [];
            const circleList = [];
            for (const point of points) {
                const { latLng, data } = point;
                const { regionCoors = '' } = data;
                if (regionCoors) {  
                    polygonList.push({  
                        centerPoint: {},
                        points: toCoordinate(regionCoors),
                        config: {
                            color: 0xff3333,
                            maxH: 0.02
                        },
                        data: {},
                    });
                } else {
                    circleList.push(latLng);
                }
            }
            return {
                polygonList,
                circleList,
            };
        },
        handleBaseStationClick(curPoint) {
            this.ActiveBaseStationRange.removeAll();
            const { data = {} } = curPoint;
            const regionCoors = data.regionCoors;
            if (!regionCoors) {
                return this.$message.warning('该基站暂无基站范围');
            } 
            this.ActiveBaseStationRange.createGrids([{
                centerPoint: {},
                points: toCoordinate(regionCoors),
                config: {
                    color: 0xff3333,
                    maxH: 0.03
                },
                data: {},
            }])
        },
        clearAll() {
            clearInterval(this.renderInterval);
            this.basePoint && this.basePoint.removeAll();
            this.pointCircle && this.pointCircle.removeAll();
            this.pointRange && this.pointRange.removeAll();
            this.ActiveBaseStationRange && this.ActiveBaseStationRange.removeAll();
        },
        // 右侧数据看板处理
        handlerRightBoard(res) {
            const { accPepCnt, laccellCnt, ageMap, genderMap } = res;
            this.coreInformation = [accPepCnt, laccellCnt];
            let portraitAnalysisData = {
                xAxis: this.portraitAnalysisData.xAxis,
                data: [],
                pieData: [],
            };
            if (accPepCnt) {
                ageMap &&
                    (portraitAnalysisData['data'] = this.portraitAnalysisData.xAxis.map((item) => {
                        return ageMap[ageList[item]];
                    }));
                genderMap &&
                    (portraitAnalysisData['pieData'] = [
                        {
                            name: '男',
                            value: genderMap.man || '-',
                            itemStyle: { color: '#04D9CF' },
                        },
                        {
                            name: '女',
                            value: genderMap.woman || '-',
                            itemStyle: { color: '#C99E2F' },
                        },
                        {
                            name: '未知',
                            value: genderMap.sex_null || '-',
                            itemStyle: { color: '#7D7D7D' },
                        },
                    ]);
            }
            this.portraitAnalysisData = portraitAnalysisData;
        },
        // 清空数据
        clearData() {
            this.mapTimeLineData = [];
            this.currentTimeLineValue = '';
            this.tableData = [];
            this.s2List = [];
            this.lacellList = [];
            this.trendAnalysisData = {
                xAxis: [],
                data: [],
            };
            this.portraitAnalysisData['pieData'] = [];
            this.portraitAnalysisData['data'] = [];
        },
        //获取总览趋势分析折线图数据
        getTrendAnalysisChartData(type) {
            const { analysisStartTime, analysisEndTime, taskId } = this.routeParmas;
            let params = {
                startTime: analysisStartTime,
                endTime: analysisEndTime,
                taskId,
                burialPepTaskPara: {
                    popType: type,
                    dataType: 2,
                },
                pageSize: 300,
                currentPage: 1,
            };
            this.getPost('post', 'getTaskResult', params, '获取趋势分析折线图信息', (res) => {
                const detailList = res.detailList;
                if (detailList && detailList.length) {
                    this.creatTimeLineData(detailList.map((item) => item.dataTime));
                    this.getSummary(this.currentTimeLineValue, 1);
                    this.getTopN(this.currentTimeLineValue);
                    this.dealTrendAnalysisChartData(detailList);
                    this.getHotMapData(this.currentTimeLineValue);
                    this.getS2Analysis(this.currentTimeLineValue);
                    this.getPersonNum(this.currentTimeLineValue, 1, '');
                } else {
                    this.clearData();
                }
            });
        },
        // 获取单个栅格的趋势分析数据
        getS2TrendAnalysis(type, paramsObj) {
            const { analysisStartTime, analysisEndTime, taskId } = this.routeParmas;
            let params = {
                startTime: analysisStartTime,
                endTime: analysisEndTime,
                taskId,
                burialPepTaskPara: {
                    popType: type,
                    ...paramsObj,
                },
                pageSize: 300,
                currentPage: 1,
            };
            this.getPost('post', 'getTaskResult', params, '获取趋势分析折线图信息', (res) => {
                this.dealTrendAnalysisChartData(res.detailList);
            });
        },
        //处理数据，渲染折线图
        dealTrendAnalysisChartData(detailList) {
            const xAxis = [];
            const data = [];
            detailList.forEach((item) => {
                xAxis.push(item.dataTime);
                data.push(item.popCnt);
            });
            this.trendAnalysisData = {
                xAxis,
                data,
            };
        },
        handlerPointColor(num) {
            const colorList = this.legends;
            const list = colorList.filter((item) => {
                const interval = item.value.split('-');
                const leftNum = Number(interval[0]);
                const rightNum = Number(interval[1]);
                if (Number(num) === 0 && leftNum === 0) {
                    return item;
                }
                if (num >= Number(leftNum) && num < Number(rightNum)) {
                    return item;
                }
            });
            return (list[0] && list[0].mapColor) || colorList[0].mapColor;
        },
        getPersonNum(time, type, s2Id) {
            if (type === 1 || this.isActive == 2 || this.isActive == 3) {
                //查询总览，切换提示
                // this.tipsData.forEach((item) => {
                //     item.label = '区域' + item.label.slice(-4);
                // });
                return;
            }
            let params = {
                startTime: time,
                endTime: time,
                taskId: this.routeParmas.taskId,
                s2Id: s2Id,
            };

            this.getPost(
                'post',
                'flowAnalysis',
                params,
                '获取流入流出分析信息',
                (res) => {
                    this.tipsData[0].value = res.outFlow;
                    this.tipsData[1].value = res.inFlow;
                },
                () => {
                    this.tipsData[0].value = '-';
                    this.tipsData[1].value = '-';
                }
            );
        },
    },
};
</script>

<style lang="less" scoped>
.analyze-details {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;
    &-top {
        height: 140px;
        background: linear-gradient(270deg, #101620 0%, #1b2f4d 100%);
        padding: 15px 40px 10px;
        .title {
            font-size: @font-size-h1;
            font-weight: bold;
            color: #ffffff;
            line-height: 28px;
            text-shadow: 0px 0px 4px rgba(62, 136, 233, 0.64);
            position: relative;
            &::after {
                position: absolute;
                content: '';
                left: -15px;
                top: 10px;
                width: 8px;
                height: 8px;
                border-radius: 50%;
                background: #53ffff;
            }
        }
    }
    .show-list {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        &-item {
            padding: 15px 80px 0px 0;
        }
        .name {
            font-weight: 400;
            font-size: 16px;
            color: #ffffff;
            width: auto;
            white-space: nowrap;
        }
        .value {
            font-weight: 400;
            font-size: 16px;
            color: #ffffff;
        }
        .level-text {
            font-size: 18px;
            font-weight: 600;
            color: #ffa040;
            font-style: italic;
        }
        /deep/.el-col {
            padding-top: 10px;
        }
    }
    &-content {
        width: 100%;
        height: calc(100% - 140px);
        position: relative;
        .left-panel {
            position: absolute;
            left: 24px;
            top: 24px;
            width: 530px;
            height: calc(100% - 140px);
            max-height: calc(100% - 140px);
            .left-shrink {
                width: 20px;
                height: 92px;
                position: absolute;
                right: -1.25rem;
                top: calc(50% - 46px);
                cursor: pointer;
                background: url('../../../img/icon/left-shrink.png') no-repeat center center / 100%
                    100%;
                &:hover {
                    background: url('../../../img/icon/left-shrink-active.png') no-repeat center
                        center / 100% 100%;
                }
            }
        }
        .left-open {
            width: 20px;
            height: 92px;
            position: absolute;
            left: 24px;
            top: calc(50% - 96px);
            cursor: pointer;
            background: url('../../../img/icon/left-open.png') no-repeat center center / 100% 100%;
            &:hover {
                background: url('../../../img/icon/left-open-active.png') no-repeat center center /
                    100% 100%;
            }
        }
        .right-panel {
            position: absolute;
            right: 24px;
            top: 24px;
            width: 470px;
            height: calc(100% - 140px);
            max-height: calc(100% - 140px);
            .right-shrink {
                width: 20px;
                height: 92px;
                position: absolute;
                left: -1.25rem;
                top: calc(50% - 46px);
                cursor: pointer;
                background: url('../../../img/icon/right-shrink.png') no-repeat center center / 100%
                    100%;
                &:hover {
                    background: url('../../../img/icon/right-shrink-active.png') no-repeat center
                        center / 100% 100%;
                }
            }
        }
        .right-open {
            width: 20px;
            height: 92px;
            position: absolute;
            right: 24px;
            top: calc(50% - 96px);
            cursor: pointer;
            background: url('../../../img/icon/right-open.png') no-repeat center center / 100% 100%;
            &:hover {
                background: url('../../../img/icon/right-open-active.png') no-repeat center center /
                    100% 100%;
            }
        }
        .top-tab {
            width: 100%;
            height: 46px;
            position: absolute;
            top: 24px;
            display: flex;
            justify-content: center;
            .tab-item {
                width: 160px;
                height: 46px;
                cursor: pointer;
                margin-right: 15px;
            }
            .baseStation {
                width: 92px;
                height: 32px;
                background: rgba(23, 42, 65, 0.6);
                box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.65);
                border-radius: 2px;
                backdrop-filter: blur(8px);
                position: absolute;
                right: 520px;
                /deep/.el-checkbox {
                    padding: 5px 17px;
                    height: 32px;
                }
                /deep/.el-checkbox__inner {
                    background: rgba(125, 172, 220, 0.2);
                    box-shadow: 0px 0px 30px 0px rgba(0, 245, 242, 0.2);
                    border-radius: 1px;
                    border: 1px solid #7dacdc;
                }
                /deep/.el-checkbox__label {
                    color: #fff;
                }
            }
        }
        .bottom-panel {
            position: absolute;
            bottom: 0px;
            width: 100%;
            height: 100px;
            // background-image:url('../../../img/analyzeDetails/thermalPanel.png');
            // background-repeat:no-repeat;
            // background-size:100% 100%;

            background: url('../../../img/analyzeDetails/thermalPanel.png') no-repeat center center /
                100% 100%;
            display: flex;
            flex-direction: column;
            .text {
                text-align: center;
                font-size: 16px;
                color: #ffffff;
                line-height: 22px;
                text-shadow: 0 0 5px #fff, 0 0 10px #fff, 0 0 15px #fff, 0 0 70px #ff00de,
                    0 0 70px rgba(62, 136, 233, 0.64);
                padding: 10px 0px 0px;
            }
        }
        .legend {
            position: absolute;
            right: 520px;
            bottom: 170px;
        }

        .num-tips {
            position: absolute;
            left: 600px;
            top: 100px;
            z-index: 666;
        }
    }
}
/deep/.epicenter {
    position: relative;
    background: url('../../../img/analyzeDetails/epicenter.png') no-repeat center center / 100% 100%;
    width: 48px;
    height: 52px;
    transform: translate(-50%, -50%);
}
</style>
